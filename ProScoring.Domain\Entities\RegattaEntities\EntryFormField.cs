using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents a field that can be added to an entry form for a regatta.
/// These fields are copied to a regatta-specific table where the organizing authority can modify
/// descriptions and enum values as needed. A separate table holds the registrants' responses.
/// </summary>
public class EntryFormField : LastChangeTrackingWithAutoInsertedIdBase, IHasInitialSeedData<EntryFormField>
{
    #region constants

    public const string ID_PREFIX = "EF";

    #endregion

    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for the entry form field.
    /// </summary>
    [Key]
    [MaxLength(10)]
    [Column(Order = 100)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets the prefix used for generating IDs for entry form fields.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the category to which this field belongs for grouping purposes.
    /// </summary>
    [Column(Order = 210)]
    [StringLength(50, MinimumLength = 3)]
    public string? Category { get; set; }

    /// <summary>
    /// Gets or sets a description or hint text for the field.
    /// </summary>
    [Column(Order = 230)]
    [MaxLength(255)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the comma-separated list of values for fields of type Enum.
    /// </summary>
    [Column(Order = 240)]
    [MaxLength(500)]
    public string? DefaultEnumValues { get; set; }

    /// <summary>
    /// Gets or sets the field name that will be displayed on the entry form.
    /// </summary>
    [Column(Order = 200)]
    [StringLength(255, MinimumLength = 3)]
    public required string Field { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the data type of this field.
    /// </summary>
    [Column(Order = 220)]
    public FieldType Type { get; set; } = FieldType.Text100;

    #endregion

    #region nested types

    /// <summary>
    /// Defines the data types available for entry form fields.
    /// </summary>
    public enum FieldType
    {
        /// <summary>
        /// A 3-character text field, suitable for country codes.
        /// </summary>
        Text3 = 10,

        /// <summary>
        /// A 25-character text field for short inputs.
        /// </summary>
        Text25 = 100,

        /// <summary>
        /// A 50-character text field for medium-length inputs.
        /// </summary>
        Text50 = 200,

        /// <summary>
        /// A 100-character text field for longer inputs.
        /// </summary>
        Text100 = 300,

        /// <summary>
        /// A 255-character text field for extended inputs.
        /// </summary>
        Text255 = 400,

        /// <summary>
        /// A boolean field for yes/no questions.
        /// </summary>
        Bool = 500,

        /// <summary>
        /// A date field.
        /// </summary>
        Date = 600,

        /// <summary>
        /// An integer field for whole number inputs.
        /// </summary>
        Int = 700,

        /// <summary>
        /// A floating-point field for decimal number inputs.
        /// </summary>
        Float = 800,

        /// <summary>
        /// A file upload field.
        /// </summary>
        File = 900,

        /// <summary>
        /// An enumerated list field with predefined options.
        /// </summary>
        Enum = 1000,
    }

    #endregion

    #region SeedData

    /// <summary>
    /// Gets the initial seed data for populating the EntryFormField database table.
    /// </summary>
    public static EntryFormField[] SeedData =>
        [
            new()
            {
                Field = "Measurement Cert.",
                Category = "Boat Information",
                Type = FieldType.File,
                Description = "Upload Cert as .pdf file.",
            },
            //new EntryFormField{Field="Score on Skipper/Boat", Category="Boat Information", Type=FieldType.Text50},
            new()
            {
                Field = "Crew Size",
                Category = "Boat Information",
                Type = FieldType.Int,
            },
            new()
            {
                Field = "Handicap Rating",
                Category = "Boat Information",
                Type = FieldType.Float,
            },
            new()
            {
                Field = "Boat Name",
                Category = "Boat Information",
                Type = FieldType.Text100,
            },
            new()
            {
                Field = "Bow Number",
                Category = "Boat Information",
                Type = FieldType.Int,
            },
            //new EntryFormField{Field="Automatically Assign Bow Numbers", Category="Boat Information", Type=FieldType.Text50},
            new()
            {
                Field = "Sail Country Code",
                Category = "Boat Information",
                Type = FieldType.Text3,
            },
            new()
            {
                Field = "Sail Number",
                Category = "Boat Information",
                Type = FieldType.Text25,
            },
            new()
            {
                Field = "Hull Color",
                Category = "Boat Information",
                Type = FieldType.Text25,
            },
            new()
            {
                Field = "Spinnaker Color",
                Category = "Boat Information",
                Type = FieldType.Text25,
            },
            new()
            {
                Field = "Overall Length",
                Category = "Boat Information",
                Type = FieldType.Float,
                Description = "Change this value to indicate unit (ft/meter).",
            },
            new()
            {
                Field = "Length Waterline",
                Category = "Boat Information",
                Type = FieldType.Float,
                Description = "Change this value to indicate unit (ft/meter).",
            },
            new()
            {
                Field = "Draft",
                Category = "Boat Information",
                Type = FieldType.Float,
                Description = "Change this value to indicate unit (ft/meter).",
            },
            new()
            {
                Field = "Beam",
                Category = "Boat Information",
                Type = FieldType.Float,
                Description = "Change this value to indicate unit (ft/meter).",
            },
            new()
            {
                Field = "Sail Plan",
                Category = "Boat Information",
                Type = FieldType.Text25,
                Description = "Sloop/Ketch/Schooner/etc.",
            },
            new()
            {
                Field = "Boat Manufacturer",
                Category = "Boat Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Allow Multiple Skipper/Owner Name(s)",
                Category = "Personal Information",
                Type = FieldType.Bool,
            },
            new()
            {
                Field = "Parent/Guardian Information",
                Category = "Personal Information",
                Type = FieldType.Text100,
            },
            new()
            {
                Field = "Emergency Contact Information",
                Category = "Personal Information",
                Type = FieldType.Text255,
            },
            new()
            {
                Field = "Graduation Year",
                Category = "Personal Information",
                Type = FieldType.Enum,
            },
            new()
            {
                Field = "Registrants Weight",
                Category = "Personal Information",
                Type = FieldType.Float,
                Description = "Change this value to indicate unit (lbs, kg, etc).",
            },
            new()
            {
                Field = "Registrants Gender",
                Category = "Personal Information",
                Type = FieldType.Text25,
            },
            new()
            {
                Field = "Shirt Size",
                Category = "Personal Information",
                Type = FieldType.Enum,
                DefaultEnumValues = "XS,S,M,L,XL,2XL,3XL,4XL",
            },
            //new EntryFormField{Field="Show Youth Sizes Also.", Category="Personal Information", Type=FieldType.Bool},
            new()
            {
                Field = "Medical Information",
                Category = "Personal Information",
                Type = FieldType.Text255,
                Description = "Special Requirements, Medications, etc.",
            },
            new()
            {
                Field = "School Name",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "School District",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Team Name",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Team Designation",
                Category = "Personal Information",
                Type = FieldType.Text50,
                Description = "Varsity, JV1, JV2, JV3, etc.",
            },
            new()
            {
                Field = "Division",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            // at this time I'm not sure how we would do this.
            //new EntryFormField{Field="Automatically Assign Divisions", Category="Personal Information", Type=FieldType.Text50},
            new()
            {
                Field = "Coach Information",
                Category = "Personal Information",
                Type = FieldType.Text100,
            },
            //new EntryFormField{Field="Historical Race Data", Category="Personal Information", Type=FieldType.Text50},
            new()
            {
                Field = "Hometown Newspaper Information",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Country Representing",
                Category = "Personal Information",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Paralympic Ratings on Paralympic Classes",
                Category = "Personal Information",
                Type = FieldType.Text100,
            },
            new()
            {
                Field = "Flight Information",
                Category = "Personal Information",
                Type = FieldType.Text100,
            },
            new()
            {
                Field = "Lodging Requirements",
                Category = "Personal Information",
                Type = FieldType.Text255,
            },
            new()
            {
                Field = "Yacht Club Affiliation",
                Category = "Membership Information/Requirements",
                Type = FieldType.Text100,
            },
            new()
            {
                Field = "Require a VALID US SAILING number to register",
                Category = "Membership Information/Requirements",
                Type = FieldType.Bool,
            },
            //new EntryFormField{Field="Class Association Status", Category="Membership Information/Requirements", Type=FieldType.Text50},
            // Regatta network verifies this field for certain class associations.
            //new EntryFormField{Field="Class Assoc. Member Number", Category="Membership Information/Requirements", Type=FieldType.Text50},
            //new EntryFormField{Field="Allow Non-Members To Register", Category="Membership Information/Requirements", Type=FieldType.Text50},
            // Regatta network verifies this field.
            new()
            {
                Field = "World Sailing Sailor ID",
                Category = "Membership Information/Requirements",
                Type = FieldType.Text50,
            },
            new()
            {
                Field = "Require a valid Sailor ID To Register",
                Category = "Membership Information/Requirements",
                Type = FieldType.Bool,
            },
            new()
            {
                Field = "US SAILING Area",
                Category = "Membership Information/Requirements",
                Type = FieldType.Enum,
                DefaultEnumValues = "A,B,C,D,E,F,G,H,J,K,L",
                Description = "See https://www.ussailing.org/competition/resources/area-map-and-representatives/",
            },
            new()
            {
                Field = "Junior Event (Disables Public Personal Information Reports)",
                Category = "Additional Form Options",
                Type = FieldType.Bool,
            },
        ];

    #endregion
}
