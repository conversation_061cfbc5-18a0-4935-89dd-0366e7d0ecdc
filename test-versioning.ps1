# Test script for versioning logic
# This script simulates the behavior of the update-build-number.ps1 script
# without actually modifying any files

Write-Host "Starting test-versioning.ps1..." -ForegroundColor Cyan

# Get the current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Green

# Use the current directory as the repository root
$repoRoot = $currentDir
Write-Host "Using current directory as repository root: $repoRoot" -ForegroundColor Green

# Find all AssemblyInfo.cs files
$assemblyInfoFiles = Get-ChildItem -Path $repoRoot -Filter "AssemblyInfo.cs" -Recurse
Write-Host "Found $($assemblyInfoFiles.Count) AssemblyInfo.cs files" -ForegroundColor Green

# Function to extract version information from a file
function Get-VersionInfo {
  param (
    [string]$FilePath
  )

  $result = @{
    Path                 = $FilePath
    Version              = "Not found"
    InformationalVersion = "Not found"
    Major                = 0
    Minor                = 0
    Build                = 0
  }

  try {
    $content = Get-Content -Path $FilePath -ErrorAction Stop

    # Extract AssemblyVersion
    $versionLine = $content | Where-Object { $_ -match 'AssemblyVersion\(' }
    if ($versionLine) {
      $versionMatch = [regex]::Match($versionLine, 'AssemblyVersion\("([^"]+)"')
      if ($versionMatch.Success) {
        $result.Version = $versionMatch.Groups[1].Value

        # Parse version components
        $versionParts = $result.Version.Split('.')
        if ($versionParts.Length -ge 3) {
          $result.Major = [int]$versionParts[0]
          $result.Minor = [int]$versionParts[1]
          $result.Build = [int]$versionParts[2]
        }
      }
    }

    # Extract AssemblyInformationalVersion
    $infoVersionLine = $content | Where-Object { $_ -match 'AssemblyInformationalVersion\(' }
    if ($infoVersionLine) {
      $infoVersionMatch = [regex]::Match($infoVersionLine, 'AssemblyInformationalVersion\("([^"]+)"')
      if ($infoVersionMatch.Success) {
        $result.InformationalVersion = $infoVersionMatch.Groups[1].Value
      }
    }
  }
  catch {
    Write-Host "Error reading file ${FilePath}: $($_.Exception.Message)" -ForegroundColor Red
  }

  return $result
}

# Create a mapping of project directories to their AssemblyInfo.cs files
$projectToAssemblyInfo = @{}

foreach ($file in $assemblyInfoFiles) {
  # Get the project directory (parent of the directory containing AssemblyInfo.cs)
  $assemblyDir = Split-Path -Parent $file.FullName
  $projectDir = Split-Path -Parent $assemblyDir

  # Use the relative path from the repository root
  $relativePath = $projectDir.Substring($repoRoot.Length + 1).Replace("\", "/")

  # Store the mapping
  $projectToAssemblyInfo[$relativePath] = $file.FullName

  Write-Host "Project: $relativePath -> $($file.FullName)"
}

# Simulate a list of staged files
# For testing, we'll pretend these files are being committed
$stagedFiles = @(
  "ProScoring.Blazor/ProScoring.Blazor/Components/Layout/MainLayout.razor",
  "ProScoring.Blazor/ProScoring.Blazor/Controllers/VersionController.cs",
  "update-build-number.ps1"
)
Write-Host "`nSimulated staged files:" -ForegroundColor Cyan
foreach ($file in $stagedFiles) {
  Write-Host "  $file" -ForegroundColor Yellow
}

# Track which assemblies need to be updated
$assembliesToUpdate = @{}

# Check each staged file to see which project it belongs to
foreach ($stagedFile in $stagedFiles) {
  # Check each project directory to see if the staged file is within it
  foreach ($projectDir in $projectToAssemblyInfo.Keys) {
    if ($stagedFile.StartsWith($projectDir)) {
      # This file belongs to this project, mark it for update
      $assembliesToUpdate[$projectDir] = $projectToAssemblyInfo[$projectDir]
      Write-Host "File $stagedFile belongs to project $projectDir" -ForegroundColor Green
      break
    }
  }
}

Write-Host "`nAssemblies that would be updated:" -ForegroundColor Cyan
foreach ($projectDir in $assembliesToUpdate.Keys) {
  Write-Host "  $projectDir -> $($assembliesToUpdate[$projectDir])" -ForegroundColor Yellow
}

# Check the current version in each AssemblyInfo.cs file
Write-Host "`nCurrent versions in AssemblyInfo.cs files:" -ForegroundColor Cyan
foreach ($file in $assemblyInfoFiles) {
  $versionInfo = Get-VersionInfo -FilePath $file.FullName

  $assemblyDir = Split-Path -Parent $file.FullName
  $projectDir = Split-Path -Parent $assemblyDir
  $relativePath = $projectDir.Substring($repoRoot.Length + 1).Replace("\", "/")

  Write-Host "  $relativePath" -ForegroundColor Yellow
  Write-Host "    Version: $($versionInfo.Version)"
  Write-Host "    InformationalVersion: $($versionInfo.InformationalVersion)"

  # Check if this assembly would be updated
  if ($assembliesToUpdate.ContainsKey($relativePath)) {
    $newBuild = $versionInfo.Build + 1
    Write-Host "    Would update to: $($versionInfo.Major).$($versionInfo.Minor).$newBuild.0" -ForegroundColor Green
  }
}

Write-Host "`nTest completed. No files were modified." -ForegroundColor Green
