using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Proscoring.TestData.Playwright.DataLoaders;

/// <summary>
/// Loads and parses JSON data for test operations.
/// </summary>
public class JsonDataLoader
{
    private readonly ILogger<JsonDataLoader> _logger;
    private readonly IConfiguration _configuration;

    public JsonDataLoader(ILogger<JsonDataLoader> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Loads user data from the configured JSON file.
    /// </summary>
    /// <returns>A collection of user objects.</returns>
    public async Task<IEnumerable<UserData>> LoadUsersAsync()
    {
        var filePath = _configuration["DataFiles:UsersJsonPath"];
        if (string.IsNullOrEmpty(filePath))
        {
            throw new ArgumentException("UsersJsonPath configuration is missing or empty");
        }
        _logger.LogInformation("Loading users from {FilePath}", filePath);

        try
        {
            var jsonString = await File.ReadAllTextAsync(filePath);
            var users = JsonSerializer.Deserialize<List<UserData>>(
                jsonString,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );

            _logger.LogInformation("Loaded {Count} users", users?.Count);
            return users ?? Enumerable.Empty<UserData>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load users from {FilePath}", filePath);
            return Enumerable.Empty<UserData>();
        }
    }

    /// <summary>
    /// Loads organizing authority data from the configured JSON file.
    /// </summary>
    /// <returns>A collection of organizing authority objects.</returns>
    public async Task<IEnumerable<OrganizingAuthorityData>> LoadOrganizingAuthoritiesAsync()
    {
        var filePath = _configuration["DataFiles:OrganizingAuthoritiesJsonPath"];
        if (string.IsNullOrEmpty(filePath))
        {
            throw new ArgumentException("OrganizingAuthoritiesJsonPath configuration is missing or empty");
        }
        _logger.LogInformation("Loading organizing authorities from {FilePath}", filePath);

        try
        {
            var jsonString = await File.ReadAllTextAsync(filePath);
            var authorities = JsonSerializer.Deserialize<List<OrganizingAuthorityData>>(
                jsonString,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            );

            _logger.LogInformation("Loaded {Count} organizing authorities", authorities?.Count);
            return authorities ?? Enumerable.Empty<OrganizingAuthorityData>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load organizing authorities from {FilePath}", filePath);
            return Enumerable.Empty<OrganizingAuthorityData>();
        }
    }

    /// <summary>
    /// Represents user data loaded from JSON.
    /// </summary>
    public class UserData
    {
        public string Id { get; set; } = string.Empty;
        public string GivenName { get; set; } = string.Empty;
        public string Surname { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string CreatedById { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string UpdatedById { get; set; } = string.Empty;
        public DateTime UpdatedAt { get; set; }
        public bool EmailConfirmed { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public bool LockoutEnabled { get; set; }
        public int AccessFailedCount { get; set; }
    }

    /// <summary>
    /// Represents organizing authority data loaded from JSON.
    /// </summary>
    public class OrganizingAuthorityData
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Website { get; set; } = string.Empty;
        public bool? Private { get; set; }
        public string AddressLine1 { get; set; } = string.Empty;
        public string AddressLine2 { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastModifiedAt { get; set; }
        public string CreatedById { get; set; } = string.Empty;
    }
}
