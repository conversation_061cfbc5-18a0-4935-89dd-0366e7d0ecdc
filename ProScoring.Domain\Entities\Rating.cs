using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a rating for a boat.
/// A boat can have multiple ratings of different types.
///
/// <i>How do we deal with ratings changing in or after a regatta?
/// I think that I need to copy <see cref="Rating"/> to <see cref="RegattaRating"/>.
/// <p/>
/// I will need logic to check if the rating has changed, and ask about updating the RegattaRating.
/// <p/>
/// I may also need multiple RegattaRatings for a single Regatta to deal with Ratings changing mid-regatta.
// </i>
/// </summary>
public class Rating : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<Rating>
{
    public const string ID_PREFIX = "H";
    public override string IdPrefix => ID_PREFIX;

    #region Fields

    #endregion

    #region Properties
    /// <summary>
    /// Gets or sets the unique identifier of the rating.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets or sets the boat this rating belongs to.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [ForeignKey(nameof(Boat))]
    public required string BoatId { get; set; }

    /// <summary>
    /// Gets or sets the rating type.
    /// </summary>
    [Required]
    [Column(Order = 30)]
    [ForeignKey(nameof(RatingType))]
    public required string RatingTypeId { get; set; }

    /// <summary>
    /// Gets or sets notes about this rating.
    /// </summary>
    [Column(Order = 50)]
    [StringLength(500)]
    public string? RatingNote { get; set; }
    #endregion

    #region Navigation Properties
    /// <summary>
    /// Gets or sets the boat that this rating belongs to.
    /// </summary>
    public virtual Boat Boat { get; set; } = null!;

    /// <summary>
    /// Gets or sets the rating type of this rating.
    /// </summary>
    public virtual RatingType RatingType { get; set; } = null!;

    /// <summary>
    /// Gets or sets the collection of rating values associated with this rating.
    /// </summary>
    public virtual ICollection<RatingValue> RatingValues { get; set; } = new List<RatingValue>();

    /// <summary>
    /// Gets or sets the collection of regatta ratings that were copied from this rating.
    /// </summary>
    public virtual ICollection<RegattaEntities.RegattaRating> RegattaRatings { get; set; } =
        new List<RegattaEntities.RegattaRating>();
    #endregion

    #region Methods
    /// <summary>
    /// Configures foreign key relationships for the Rating entity.
    /// </summary>
    /// <param name="entity">The entity type builder for Rating.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<Rating> entity)
    {
        entity
            .HasOne(r => r.Boat)
            .WithMany(b => b.Ratings)
            .HasForeignKey(r => r.BoatId)
            .OnDelete(DeleteBehavior.Cascade);

        entity
            .HasOne(r => r.RatingType)
            .WithMany()
            .HasForeignKey(r => r.RatingTypeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
    #endregion
}
