using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Tests.Helpers;

public class FixedDateTimeOffsetProvider : IDateTimeOffsetProvider
{
    public DateTimeOffset UtcNow => _dateTimeOffsetStack.Peek();

    private readonly Stack<DateTimeOffset> _dateTimeOffsetStack = new();

    public FixedDateTimeOffsetProvider(DateTimeOffset dateTimeOffset)
    {
        _dateTimeOffsetStack.Push(dateTimeOffset);
    }

    public FixedDateTimeOffsetProvider(
        int year,
        int month = 1,
        int day = 1,
        int hour = 0,
        int minute = 0,
        int second = 0
    )
    {
        var newDateTimeOffset = new DateTimeOffset(year, month, day, hour, minute, second, TimeSpan.Zero);
        _dateTimeOffsetStack.Push(newDateTimeOffset);
    }

    public FixedDateTimeOffsetProvider()
    {
        _dateTimeOffsetStack.Push(DateTimeOffset.UtcNow);
    }

    #region methods

    public DateTimeOffset Pop()
    {
        if (_dateTimeOffsetStack.Count == 1)
        {
            throw new InvalidOperationException("Cannot reset. Already at initial value.");
        }
        _dateTimeOffsetStack.Pop();
        return UtcNow;
    }

    public DateTimeOffset SetDate(int year, int month, int day)
    {
        var newDateTimeOffset = new DateTimeOffset(
            year,
            month,
            day,
            UtcNow.Hour,
            UtcNow.Minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetDay(int day)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            day,
            UtcNow.Hour,
            UtcNow.Minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    // public methods for setting the date and the time offset
    public DateTimeOffset SetHour(int hour)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            UtcNow.Day,
            hour,
            UtcNow.Minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetMinute(int minute)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            UtcNow.Day,
            UtcNow.Hour,
            minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetMonth(int month)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            month,
            UtcNow.Day,
            UtcNow.Hour,
            UtcNow.Minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetOffset(TimeSpan offset)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            UtcNow.Day,
            UtcNow.Hour,
            UtcNow.Minute,
            UtcNow.Second,
            offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetSecond(int second)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            UtcNow.Day,
            UtcNow.Hour,
            UtcNow.Minute,
            second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetTime(int hour, int minute, int second)
    {
        var newDateTimeOffset = new DateTimeOffset(
            UtcNow.Year,
            UtcNow.Month,
            UtcNow.Day,
            hour,
            minute,
            second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    public DateTimeOffset SetYear(int year)
    {
        var newDateTimeOffset = new DateTimeOffset(
            year,
            UtcNow.Month,
            UtcNow.Day,
            UtcNow.Hour,
            UtcNow.Minute,
            UtcNow.Second,
            UtcNow.Offset
        );
        _dateTimeOffsetStack.Push(newDateTimeOffset);
        return newDateTimeOffset;
    }

    #endregion
}
