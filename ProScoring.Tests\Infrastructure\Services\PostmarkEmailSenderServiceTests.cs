using System;
using System.Collections.Generic; // For List
using System.Linq; // For .AsEnumerable()
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using ProScoring.Domain.Common; // For ConfigurationErrorsException
using ProScoring.Infrastructure.Exceptions; // For EmailException
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class PostmarkEmailSenderServiceTests
    {
        private readonly IConfiguration _configurationSubstitute; // Changed
        private readonly ILogger<PostmarkEmailSenderService> _loggerSubstitute; // Changed
        private PostmarkEmailSenderService _postmarkEmailSenderService;

        public PostmarkEmailSenderServiceTests()
        {
            _configurationSubstitute = Substitute.For<IConfiguration>();
            _loggerSubstitute = Substitute.For<ILogger<PostmarkEmailSenderService>>();
        }

        // Helper to create a substitute for IConfigurationSection representing a specific string value
        private IConfigurationSection CreateConfigSectionSubstitute(
            string key,
            string value,
            string parentPath = EmailSenderOptions.SECTION_NAME
        )
        {
            var section = Substitute.For<IConfigurationSection>();
            section.Key.Returns(key);
            section.Path.Returns($"{parentPath}:{key}");
            section.Value.Returns(value);
            section.Exists().Returns(true);
            return section;
        }

        // Helper to create a substitute for IConfigurationSection that "does not exist"
        private IConfigurationSection CreateNonExistentConfigSectionSubstitute(
            string key,
            string parentPath = EmailSenderOptions.SECTION_NAME
        )
        {
            var section = Substitute.For<IConfigurationSection>();
            section.Key.Returns(key);
            section.Path.Returns($"{parentPath}:{key}");
            section.Value.Returns((string)null);
            section.Exists().Returns(false); // Key does not exist
            return section;
        }

        private void SetupEmailOptions(bool enabled, string apiKey, string fromAddress, string fromName = "Test App")
        {
            var sectionSubstitute = Substitute.For<IConfigurationSection>();
            sectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Exists().Returns(true);
            sectionSubstitute.Value.Returns((string)null);

            var childrenSubstitutes = new List<IConfigurationSection>();

            var enabledSubstituteSection = CreateConfigSectionSubstitute(
                nameof(EmailSenderOptions.Enabled),
                enabled.ToString().ToLowerInvariant()
            );
            childrenSubstitutes.Add(enabledSubstituteSection);

            IConfigurationSection apiKeySubstituteSection;
            if (apiKey != null)
            {
                apiKeySubstituteSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey), apiKey);
            }
            else // Simulate key is missing
            {
                apiKeySubstituteSection = CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey));
            }
            childrenSubstitutes.Add(apiKeySubstituteSection);

            IConfigurationSection fromAddressSubstituteSection;
            if (fromAddress != null)
            {
                fromAddressSubstituteSection = CreateConfigSectionSubstitute(
                    nameof(EmailSenderOptions.FromAddress),
                    fromAddress
                );
            }
            else // Simulate key is missing
            {
                fromAddressSubstituteSection = CreateNonExistentConfigSectionSubstitute(
                    nameof(EmailSenderOptions.FromAddress)
                );
            }
            childrenSubstitutes.Add(fromAddressSubstituteSection);

            var fromNameSubstituteSection = CreateConfigSectionSubstitute(
                nameof(EmailSenderOptions.FromName),
                fromName
            );
            childrenSubstitutes.Add(fromNameSubstituteSection);

            sectionSubstitute.GetChildren().Returns(childrenSubstitutes.AsEnumerable());

            // Setup indexer access for each property for the binder
            sectionSubstitute.GetSection(nameof(EmailSenderOptions.Enabled)).Returns(enabledSubstituteSection);
            sectionSubstitute.GetSection(nameof(EmailSenderOptions.ApiKey)).Returns(apiKeySubstituteSection);
            sectionSubstitute.GetSection(nameof(EmailSenderOptions.FromAddress)).Returns(fromAddressSubstituteSection);
            sectionSubstitute.GetSection(nameof(EmailSenderOptions.FromName)).Returns(fromNameSubstituteSection);

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(sectionSubstitute);

            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute); // Removed .Object
        }

        [Fact]
        public async Task SendEmailAsync_MissingApiKey_ThrowsInvalidOperationException()
        {
            // Arrange
            var optionsSectionSubstitute = Substitute.For<IConfigurationSection>();
            optionsSectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Exists().Returns(true);

            var apiKeySection = CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey));
            var fromAddressSection = CreateConfigSectionSubstitute(
                nameof(EmailSenderOptions.FromAddress),
                "<EMAIL>"
            );
            var fromNameSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromName), "Test App");
            var enabledSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.Enabled), "true");

            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.ApiKey)).Returns(apiKeySection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.FromAddress)).Returns(fromAddressSection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.FromName)).Returns(fromNameSection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.Enabled)).Returns(enabledSection);

            var children = new List<IConfigurationSection>
            {
                // Not adding ApiKey to GetChildren() list simulates it missing for some binding scenarios too.
                fromAddressSection,
                fromNameSection,
                enabledSection,
                // ApiKey child is effectively missing or set up as non-existent by indexer access.
            };
            optionsSectionSubstitute.GetChildren().Returns(children.AsEnumerable());

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(optionsSectionSubstitute);
            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );
            Assert.Contains("Postmark API key is not set.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_MissingFromAddress_ThrowsInvalidOperationException()
        {
            // Arrange
            var optionsSectionSubstitute = Substitute.For<IConfigurationSection>();
            optionsSectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Exists().Returns(true);

            var apiKeySection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey), "test_api_key");
            var fromAddressSection = CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.FromAddress));
            var fromNameSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromName), "Test App");
            var enabledSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.Enabled), "true");

            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.ApiKey)).Returns(apiKeySection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.FromAddress)).Returns(fromAddressSection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.FromName)).Returns(fromNameSection);
            optionsSectionSubstitute.GetSection(nameof(EmailSenderOptions.Enabled)).Returns(enabledSection);

            var children = new List<IConfigurationSection>
            {
                apiKeySection,
                fromNameSection,
                enabledSection,
                // FromAddress child is effectively missing or set up as non-existent by indexer access.
            };
            optionsSectionSubstitute.GetChildren().Returns(children.AsEnumerable());

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(optionsSectionSubstitute);
            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );
            Assert.Contains("Postmark From address is not set.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_PostmarkClientThrowsException_ThrowsEmailExceptionAndLogsError()
        {
            // Arrange
            SetupEmailOptions(enabled: true, apiKey: "bogus_api_key_that_will_fail", fromAddress: "<EMAIL>");
            var expectedLogMessagePart = "Error sending email with Postmark:";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<EmailException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );

            _loggerSubstitute
                .Received(1)
                .Log( // Changed
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }
    }
}
