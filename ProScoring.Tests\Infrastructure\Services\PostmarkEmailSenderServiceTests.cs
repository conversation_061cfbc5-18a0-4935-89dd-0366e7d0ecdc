using System;
using System.Collections.Generic; // For List
using System.Linq; // For .AsEnumerable()
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using ProScoring.Domain.Common; // For ConfigurationErrorsException
using ProScoring.Infrastructure.Exceptions; // For EmailException
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class PostmarkEmailSenderServiceTests
    {
        private readonly ILogger<PostmarkEmailSenderService> _loggerSubstitute; // Changed
        private PostmarkEmailSenderService _postmarkEmailSenderService;

        public PostmarkEmailSenderServiceTests()
        {
            _loggerSubstitute = Substitute.For<ILogger<PostmarkEmailSenderService>>();
        }

        private void SetupEmailOptions(bool enabled, string? apiKey, string? fromAddress, string fromName = "Test App")
        {
            // Use in-memory configuration instead of complex mocking
            var configData = new Dictionary<string, string?>();

            configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.Enabled)}"] = enabled.ToString();
            configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromName)}"] = fromName;

            // Only add keys that are not null (to simulate missing configuration)
            if (apiKey != null)
            {
                configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.ApiKey)}"] = apiKey;
            }

            if (fromAddress != null)
            {
                configData[$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromAddress)}"] = fromAddress;
            }

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

            _postmarkEmailSenderService = new PostmarkEmailSenderService(configuration, _loggerSubstitute);
        }

        [Fact]
        public async Task SendEmailAsync_MissingApiKey_ThrowsInvalidOperationException()
        {
            // Arrange - apiKey is null to simulate missing configuration
            SetupEmailOptions(enabled: true, apiKey: null, fromAddress: "<EMAIL>");

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );
            Assert.Contains("Postmark API key is not set.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_MissingFromAddress_ThrowsInvalidOperationException()
        {
            // Arrange - fromAddress is null to simulate missing configuration
            SetupEmailOptions(enabled: true, apiKey: "test_api_key", fromAddress: null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );
            Assert.Contains("Postmark From address is not set.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_PostmarkClientThrowsException_ThrowsEmailExceptionAndLogsError()
        {
            // Arrange
            SetupEmailOptions(enabled: true, apiKey: "bogus_api_key_that_will_fail", fromAddress: "<EMAIL>");
            var expectedLogMessagePart = "failed to send email";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<EmailException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message")
            );

            _loggerSubstitute
                .Received(1)
                .Log( // Changed
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }
    }
}
