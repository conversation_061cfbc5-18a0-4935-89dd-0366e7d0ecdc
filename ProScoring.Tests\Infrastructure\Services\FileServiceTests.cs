using System;
using System.Collections.Generic; // For IReadOnlyList used in logger verification
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web; // For HttpUtility.HtmlEncode
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute; // Changed from Moq
using ProScoring.Domain.Dtos; // For FileUploadResult
using ProScoring.Domain.Entities; // For FileRecord
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces; // For IDateTimeOffsetProvider
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class FileServiceTests : IDisposable
    {
        private readonly IHostEnvironment _mockHostEnvironment; // Changed from Mock<T>
        private readonly IOptions<FileUploadOptions> _mockFileUploadOptions; // Changed
        private readonly IDateTimeOffsetProvider _mockDateTimeProvider; // Changed
        private readonly ILogger<FileService> _mockLogger; // Changed
        private readonly IConfiguration _mockConfiguration; // Changed
        private readonly IOptions<DatabaseOptions> _mockDbOptions; // Changed

        private readonly ApplicationDbContext _dbContext;
        private readonly FileService _fileService;
        private readonly FileUploadOptions _fileUploadOptions;
        private readonly string _fakeContentRootPath = Path.Combine(
            Path.GetTempPath(),
            "ProScoringTests_FakeContentRoot",
            Guid.NewGuid().ToString()
        );
        private readonly string _unsafeUploadsSubPath = "unsafe_uploads";

        public FileServiceTests()
        {
            _mockHostEnvironment = Substitute.For<IHostEnvironment>();
            _mockFileUploadOptions = Substitute.For<IOptions<FileUploadOptions>>();
            _mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            _mockLogger = Substitute.For<ILogger<FileService>>();
            _mockConfiguration = Substitute.For<IConfiguration>();
            _mockDbOptions = Substitute.For<IOptions<DatabaseOptions>>();

            // Setup IHostEnvironment
            _mockHostEnvironment.ContentRootPath.Returns(_fakeContentRootPath); // Changed
            Directory.CreateDirectory(Path.Combine(_fakeContentRootPath, _unsafeUploadsSubPath));

            // Setup FileUploadOptions
            _fileUploadOptions = new FileUploadOptions { MaxSize = 1024 * 1024, AllowedExtensions = [".txt", ".jpg"] };
            _mockFileUploadOptions.Value.Returns(_fileUploadOptions); // Changed

            // Setup IDateTimeOffsetProvider
            var fixedDateTime = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);
            _mockDateTimeProvider.UtcNow.Returns(fixedDateTime); // Changed

            // Setup DatabaseOptions
            _mockDbOptions.Value.Returns(new DatabaseOptions()); // Changed

            // Setup ApplicationDbContext (In-Memory)
            var dbOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            // Create a mock AuthenticationStateProvider
            var mockAuthStateProvider = Substitute.For<AuthenticationStateProvider>();

            // Create a mock IValueGenerator
            var mockValueGenerator = Substitute.For<IValueGenerator>();

            // Create a mock logger for ApplicationDbContext
            var mockDbLogger = Substitute.For<ILogger<ApplicationDbContext>>();

            _dbContext = new ApplicationDbContext(
                dbOptions,
                mockAuthStateProvider,
                mockValueGenerator,
                mockDbLogger,
                _mockDateTimeProvider
            );

            // Initialize FileService
            _fileService = new FileService(
                _mockHostEnvironment, // Removed .Object
                _mockFileUploadOptions, // Removed .Object
                _dbContext,
                _mockDateTimeProvider, // Removed .Object
                _mockLogger
            ); // Removed .Object
        }

        // --- UploadAsync Tests ---
        [Fact]
        public async Task UploadAsync_NullFile_ReturnsError()
        {
            // Act
            var result = await _fileService.UploadAsync(null, "test_note");

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileNull, result.ErrorCode);
            Assert.Null(result.Id);
        }

        [Fact]
        public async Task UploadAsync_EmptyFileName_ReturnsError()
        {
            // Arrange
            var mockFormFile = Substitute.For<IFormFile>(); // Changed
            mockFormFile.FileName.Returns(""); // Changed
            mockFormFile.Length.Returns(100); // Changed

            // Act
            var result = await _fileService.UploadAsync(mockFormFile, "test_note"); // Removed .Object

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileNull, result.ErrorCode);
        }

        [Fact]
        public async Task UploadAsync_FileTooLarge_ReturnsError()
        {
            // Arrange
            var mockFormFile = Substitute.For<IFormFile>(); // Changed
            mockFormFile.FileName.Returns("largefile.txt"); // Changed
            mockFormFile.Length.Returns(_fileUploadOptions.MaxSize + 1); // Changed

            // Act
            var result = await _fileService.UploadAsync(mockFormFile, "test_note"); // Removed .Object

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileTooLarge, result.ErrorCode);
        }

        [Fact]
        public async Task UploadAsync_EmptyFile_ReturnsError()
        {
            // Arrange
            var mockFormFile = Substitute.For<IFormFile>(); // Changed
            mockFormFile.FileName.Returns("emptyfile.txt"); // Changed
            mockFormFile.Length.Returns(0); // Changed

            // Act
            var result = await _fileService.UploadAsync(mockFormFile, "test_note"); // Removed .Object

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileEmpty, result.ErrorCode);
        }

        [Fact]
        public async Task UploadAsync_ValidFile_SavesFileAndRecord()
        {
            // Arrange
            var fileName = "testfile.txt";
            var fileContent = "Hello, world!";
            var fileContentType = "text/plain";
            var note = "A test file.";
            var fileBytes = Encoding.UTF8.GetBytes(fileContent);
            var memoryStream = new MemoryStream(fileBytes); // This stream will be returned by OpenReadStream

            var mockFormFile = Substitute.For<IFormFile>(); // Changed
            mockFormFile.FileName.Returns(fileName); // Changed
            mockFormFile.Length.Returns(memoryStream.Length); // Changed
            mockFormFile.ContentType.Returns(fileContentType); // Changed
            mockFormFile.OpenReadStream().Returns(memoryStream); // Changed

            // For CopyToAsync, the service reads from OpenReadStream and writes to its own file stream.
            // The mock just needs to confirm CopyToAsync is callable and returns a completed task.
            // The original Moq setup with Callback was simulating the copy, which is more than typical for a mock.
            // NSubstitute equivalent for the original intent (simulating the copy):
            mockFormFile
                .CopyToAsync(Arg.Any<Stream>(), Arg.Any<CancellationToken>())
                .Returns(callInfo =>
                {
                    var targetStream = callInfo.Arg<Stream>();
                    // memoryStream here is the one from the Arrange phase, it's captured in the closure
                    memoryStream.Seek(0, SeekOrigin.Begin);
                    memoryStream.CopyTo(targetStream);
                    return Task.CompletedTask;
                });

            // Act
            var result = await _fileService.UploadAsync(mockFormFile, note); // Removed .Object

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.None, result.ErrorCode);
            Assert.False(string.IsNullOrEmpty(result.Id));
            Assert.Equal(HttpUtility.HtmlEncode(fileName), result.TrustedFileNameForDisplay);

            var record = await _dbContext.Files.FindAsync(result.Id);
            Assert.NotNull(record);
            Assert.Equal(fileName, record.UntrustedName);
            Assert.Equal(HttpUtility.HtmlEncode(fileName), record.TrustedFileNameForDisplay);
            Assert.Contains(_unsafeUploadsSubPath, record.Path);
            Assert.Contains(result.Id, record.Path);
            Assert.StartsWith(_fakeContentRootPath, record.Path);
            Assert.Equal(note, record.Note);
            Assert.Equal(memoryStream.Length, record.Size);
            Assert.Equal(fileContentType, record.ContentType);
            Assert.Equal(_mockDateTimeProvider.UtcNow, record.UploadDate); // Removed .Object from _mockDateTimeProvider

            var expectedPhysicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                record.Id + Path.GetExtension(fileName)
            );
            Assert.True(File.Exists(expectedPhysicalPath), $"File not found at {expectedPhysicalPath}");
            var writtenContent = await File.ReadAllTextAsync(expectedPhysicalPath);
            Assert.Equal(fileContent, writtenContent);

            await memoryStream.DisposeAsync();
        }

        // --- UploadFromDataUriAsync Tests ---
        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("data:")]
        [InlineData("data:text/plain")]
        [InlineData("data:text/plain,bm90YmFzZTY0")]
        [InlineData("text/plain;base64,SGVsbG8=")]
        public async Task UploadFromDataUriAsync_InvalidOrMalformedDataUri_ReturnsError(string dataUri)
        {
            // Act
            var result = await _fileService.UploadFromDataUriAsync("testfile.txt", "test note", dataUri);

            // Assert
            Assert.True(
                result.ErrorCode == (int)FileUploadResult.ErrorCodes.FileNull
                    || result.ErrorCode == (int)FileUploadResult.ErrorCodes.InvalidFileType,
                $"Expected FileNull or InvalidFileType, but got {result.ErrorCode}"
            );
            Assert.Null(result.Id);
        }

        [Fact]
        public async Task UploadFromDataUriAsync_FileTooLarge_ReturnsError()
        {
            // Arrange
            var oversizedData = new byte[_fileUploadOptions.MaxSize + 1];
            Array.Fill<byte>(oversizedData, 0x41);
            var oversizedBase64 = Convert.ToBase64String(oversizedData);
            var dataUri = $"data:application/octet-stream;base64,{oversizedBase64}";

            // Act
            var result = await _fileService.UploadFromDataUriAsync("largefile.bin", "test note", dataUri);

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileTooLarge, result.ErrorCode);
        }

        [Fact]
        public async Task UploadFromDataUriAsync_EmptyFile_ReturnsError()
        {
            // Arrange
            var dataUri = "data:text/plain;base64,";

            // Act
            var result = await _fileService.UploadFromDataUriAsync("emptyfile.txt", "test note", dataUri);

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.FileEmpty, result.ErrorCode);
        }

        [Fact]
        public async Task UploadFromDataUriAsync_ValidDataUri_SavesFileAndRecord()
        {
            // Arrange
            var fileName = "datauri_test.txt";
            var note = "Uploaded from Data URI.";
            var fileContent = "Hello from Data URI!";
            var fileContentBytes = Encoding.UTF8.GetBytes(fileContent);
            var base64Content = Convert.ToBase64String(fileContentBytes);
            var dataUri = $"data:text/plain;base64,{base64Content}";
            var expectedContentType = "text/plain";

            // Act
            var result = await _fileService.UploadFromDataUriAsync(fileName, note, dataUri);

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.None, result.ErrorCode);
            Assert.False(string.IsNullOrEmpty(result.Id));
            Assert.Equal(HttpUtility.HtmlEncode(fileName), result.TrustedFileNameForDisplay);

            var record = await _dbContext.Files.FindAsync(result.Id);
            Assert.NotNull(record);
            Assert.Equal(fileName, record.UntrustedName);
            Assert.Equal(HttpUtility.HtmlEncode(fileName), record.TrustedFileNameForDisplay);
            Assert.Contains(_unsafeUploadsSubPath, record.Path);
            Assert.Contains(result.Id, record.Path);
            Assert.StartsWith(_fakeContentRootPath, record.Path);
            Assert.Equal(note, record.Note);
            Assert.Equal(fileContentBytes.Length, record.Size);
            Assert.Equal(expectedContentType, record.ContentType);
            Assert.Equal(_mockDateTimeProvider.UtcNow, record.UploadDate); // Removed .Object

            var expectedPhysicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                result.Id + Path.GetExtension(fileName)
            );
            Assert.True(File.Exists(expectedPhysicalPath), $"File not found at {expectedPhysicalPath}");
            var writtenBytes = await File.ReadAllBytesAsync(expectedPhysicalPath);
            Assert.Equal(fileContentBytes, writtenBytes);
        }

        [Fact]
        public async Task UploadFromDataUriAsync_ValidDataUri_ExtensionFromFileName_SavesFileAndRecord()
        {
            // Arrange
            var fileName = "datauri_test_no_mimetype.png";
            var note = "Uploaded from Data URI no mime type.";
            var fileContent = "Fake PNG content";
            var fileContentBytes = Encoding.UTF8.GetBytes(fileContent);
            var base64Content = Convert.ToBase64String(fileContentBytes);
            var dataUri = $"data:;base64,{base64Content}";
            var expectedContentType = "image/png";

            // Act
            var result = await _fileService.UploadFromDataUriAsync(fileName, note, dataUri);

            // Assert
            Assert.Equal((int)FileUploadResult.ErrorCodes.None, result.ErrorCode);
            Assert.False(string.IsNullOrEmpty(result.Id));

            var record = await _dbContext.Files.FindAsync(result.Id);
            Assert.NotNull(record);
            Assert.Equal(fileName, record.UntrustedName);
            Assert.Equal(expectedContentType, record.ContentType);

            var expectedPhysicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                result.Id + Path.GetExtension(fileName)
            );
            Assert.True(File.Exists(expectedPhysicalPath), $"File not found at {expectedPhysicalPath}");
            var writtenBytes = await File.ReadAllBytesAsync(expectedPhysicalPath);
            Assert.Equal(fileContentBytes, writtenBytes);
        }

        // --- DownloadAsync Tests ---
        [Fact]
        public async Task DownloadAsync_FileNotFoundInDb_ThrowsFileNotFoundException()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid().ToString();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<FileNotFoundException>(
                () => _fileService.DownloadAsync(nonExistentId)
            );
            Assert.Contains(nonExistentId, exception.Message);
        }

        [Fact]
        public async Task DownloadAsync_FileRecordExistsButFileMissingOnDisk_ThrowsFileNotFoundExceptionAndLogsError()
        {
            // Arrange
            var recordId = Guid.NewGuid().ToString();
            var fileName = "disk_missing_file.txt";
            var physicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                recordId + Path.GetExtension(fileName)
            );
            var expectedLogMessagePart = $"Physical file not found at path {physicalPath} for FileRecord Id {recordId}";

            var fileRecord = new FileRecord
            {
                Id = recordId,
                UntrustedName = fileName,
                TrustedFileNameForDisplay = HttpUtility.HtmlEncode(fileName),
                Path = physicalPath,
                ContentType = "text/plain",
                Size = 123,
                UploadDate = _mockDateTimeProvider.UtcNow,
            }; // Removed .Object
            _dbContext.Files.Add(fileRecord);
            await _dbContext.SaveChangesAsync();

            if (File.Exists(physicalPath))
                File.Delete(physicalPath);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<FileNotFoundException>(() => _fileService.DownloadAsync(recordId));
            Assert.Contains(recordId, exception.Message);
            Assert.Contains("Physical file not found at path", exception.Message);

            _mockLogger
                .Received(1)
                .Log( // Changed
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                    Arg.Any<FileNotFoundException>(),
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task DownloadAsync_FileFound_ReturnsFileDownloadResult()
        {
            // Arrange
            var recordId = Guid.NewGuid().ToString();
            var fileName = "download_test.txt";
            var fileContent = "This is fake content for download test.";
            var fileContentType = "text/plain";
            var physicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                recordId + Path.GetExtension(fileName)
            );

            Directory.CreateDirectory(Path.GetDirectoryName(physicalPath)!);
            await File.WriteAllTextAsync(physicalPath, fileContent);

            var fileRecord = new FileRecord
            {
                Id = recordId,
                UntrustedName = fileName,
                TrustedFileNameForDisplay = HttpUtility.HtmlEncode(fileName),
                Path = physicalPath,
                ContentType = fileContentType,
                Size = fileContent.Length,
                UploadDate = _mockDateTimeProvider.UtcNow,
            }; // Removed .Object
            _dbContext.Files.Add(fileRecord);
            await _dbContext.SaveChangesAsync();

            // Act
            var result = await _fileService.DownloadAsync(recordId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(fileContentType, result.ContentType);
            Assert.Equal(HttpUtility.HtmlEncode(fileName), result.FileName);
            Assert.NotNull(result.Stream);

            using var reader = new StreamReader(result.Stream);
            var downloadedContent = await reader.ReadToEndAsync();
            Assert.Equal(fileContent, downloadedContent);

            await result.Stream.DisposeAsync();
        }

        // --- DownloadAsDataUriAsync Tests ---
        [Fact]
        public async Task DownloadAsDataUriAsync_FileNotFoundInDb_ThrowsFileNotFoundException()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid().ToString();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<FileNotFoundException>(
                () => _fileService.DownloadAsDataUriAsync(nonExistentId)
            );
            Assert.Contains(nonExistentId, exception.Message);
        }

        [Fact]
        public async Task DownloadAsDataUriAsync_FileRecordExistsButFileMissingOnDisk_ThrowsFileNotFoundExceptionAndLogsError()
        {
            // Arrange
            var recordId = Guid.NewGuid().ToString();
            var fileName = "disk_missing_for_datauri.txt";
            var physicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                recordId + Path.GetExtension(fileName)
            );
            var expectedLogMessagePart = $"Physical file not found at path {physicalPath} for FileRecord Id {recordId}";

            var fileRecord = new FileRecord
            {
                Id = recordId,
                UntrustedName = fileName,
                TrustedFileNameForDisplay = HttpUtility.HtmlEncode(fileName),
                Path = physicalPath,
                ContentType = "text/plain",
                Size = 123,
                UploadDate = _mockDateTimeProvider.UtcNow,
            }; // Removed .Object
            _dbContext.Files.Add(fileRecord);
            await _dbContext.SaveChangesAsync();

            if (File.Exists(physicalPath))
                File.Delete(physicalPath);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<FileNotFoundException>(
                () => _fileService.DownloadAsDataUriAsync(recordId)
            );
            Assert.Contains(recordId, exception.Message);
            Assert.Contains("Physical file not found at path", exception.Message);

            _mockLogger
                .Received(1)
                .Log( // Changed
                    LogLevel.Error,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                    Arg.Any<FileNotFoundException>(),
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task DownloadAsDataUriAsync_FileFound_ReturnsDataUriString()
        {
            // Arrange
            var recordId = Guid.NewGuid().ToString();
            var fileName = "datauri_download_test.txt";
            var fileContent = "This is fake content for data URI download test!";
            var fileContentBytes = Encoding.UTF8.GetBytes(fileContent);
            var expectedBase64Content = Convert.ToBase64String(fileContentBytes);
            var fileContentType = "text/plain";
            var physicalPath = Path.Combine(
                _fakeContentRootPath,
                _unsafeUploadsSubPath,
                recordId + Path.GetExtension(fileName)
            );

            Directory.CreateDirectory(Path.GetDirectoryName(physicalPath)!);
            await File.WriteAllBytesAsync(physicalPath, fileContentBytes);

            var fileRecord = new FileRecord
            {
                Id = recordId,
                UntrustedName = fileName,
                TrustedFileNameForDisplay = HttpUtility.HtmlEncode(fileName),
                Path = physicalPath,
                ContentType = fileContentType,
                Size = fileContentBytes.Length,
                UploadDate = _mockDateTimeProvider.UtcNow,
            }; // Removed .Object
            _dbContext.Files.Add(fileRecord);
            await _dbContext.SaveChangesAsync();

            // Act
            var dataUriResult = await _fileService.DownloadAsDataUriAsync(recordId);

            // Assert
            Assert.NotNull(dataUriResult);
            Assert.StartsWith($"data:{fileContentType};base64,", dataUriResult);
            var base64Part = dataUriResult[$"data:{fileContentType};base64,".Length..];
            Assert.Equal(expectedBase64Content, base64Part);

            var decodedBytes = Convert.FromBase64String(base64Part);
            var decodedContent = Encoding.UTF8.GetString(decodedBytes);
            Assert.Equal(fileContent, decodedContent);
        }

        // --- GetFileRecordAsync Tests ---
        [Fact]
        public async Task GetFileRecordAsync_NotFound_ThrowsFileNotFoundException()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid().ToString();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<FileNotFoundException>(
                () => _fileService.GetFileRecordAsync(nonExistentId)
            );
            Assert.Contains(nonExistentId, exception.Message);
        }

        [Fact]
        public async Task GetFileRecordAsync_Found_ReturnsFileRecord()
        {
            // Arrange
            var recordId = Guid.NewGuid().ToString();
            var fileName = "get_record_test.txt";
            var expectedFileRecord = new FileRecord
            {
                Id = recordId,
                UntrustedName = fileName,
                TrustedFileNameForDisplay = HttpUtility.HtmlEncode(fileName),
                Path = Path.Combine(
                    _fakeContentRootPath,
                    _unsafeUploadsSubPath,
                    recordId + Path.GetExtension(fileName)
                ),
                ContentType = "text/plain",
                Size = 100,
                Note = "Test note for GetFileRecordAsync",
                UploadDate = _mockDateTimeProvider.UtcNow, // Removed .Object
            };

            _dbContext.Files.Add(expectedFileRecord);
            await _dbContext.SaveChangesAsync();

            // Act
            var actualFileRecord = await _fileService.GetFileRecordAsync(recordId);

            // Assert
            Assert.NotNull(actualFileRecord);
            Assert.Equal(expectedFileRecord.Id, actualFileRecord.Id);
            Assert.Equal(expectedFileRecord.UntrustedName, actualFileRecord.UntrustedName);
            Assert.Equal(expectedFileRecord.TrustedFileNameForDisplay, actualFileRecord.TrustedFileNameForDisplay);
            Assert.Equal(expectedFileRecord.Path, actualFileRecord.Path);
            Assert.Equal(expectedFileRecord.ContentType, actualFileRecord.ContentType);
            Assert.Equal(expectedFileRecord.Size, actualFileRecord.Size);
            Assert.Equal(expectedFileRecord.Note, actualFileRecord.Note);
            Assert.Equal(expectedFileRecord.UploadDate, actualFileRecord.UploadDate);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            try
            {
                if (Directory.Exists(_fakeContentRootPath))
                {
                    Directory.Delete(_fakeContentRootPath, true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during cleanup: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }
    }
}
