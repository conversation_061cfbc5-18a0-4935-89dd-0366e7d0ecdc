using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.DbSupportBaseClasses;

namespace ProScoring.Domain.Entities;

public class ImageMetadata : LastChangeTrackingWithAutoInsertedIdBase
{
    public const string ID_PREFIX = "I";

    #region properties
    public string? AltText { get; set; }
    public string? Caption { get; set; }
    public string? Description { get; set; }

    // Navigation property
    public virtual required FileRecord FileRecord { get; set; }

    [Required]
    [MaxLength(10)]
    [ForeignKey(nameof(FileRecord))]
    public required string FileRecordId { get; set; }

    public int Height { get; set; }

    [Key]
    [MaxLength(10)]
    public override string? Id { get; set; }

    public override string IdPrefix => ID_PREFIX;
    public string? Title { get; set; }
    public int Width { get; set; }
    #endregion
}
