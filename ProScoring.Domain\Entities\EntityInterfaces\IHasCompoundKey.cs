using System.Linq.Expressions;

namespace ProScoring.Domain.Entities.EntityInterfaces;

/// <summary>
/// Interface for entities that have compound keys.
/// </summary>
/// <typeparam name="T">The entity type.</typeparam>
public interface IHasCompoundKey<T>
    where T : class
{
    /// <summary>
    /// Defines the compound key for the entity.
    /// </summary>
    /// <returns>An expression that defines the compound key.</returns>
    public static abstract Expression<Func<T, object?>> DefineCompoundKey();
}
