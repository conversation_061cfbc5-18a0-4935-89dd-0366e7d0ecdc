using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Each record holds a suggested fleet for a regatta.
/// This is used for autofill when creating a new regatta.
/// </summary>
public class FleetSuggestion
{
    /// <summary>
    /// Gets or sets the unique identifier for the fleet suggestion.
    /// </summary>
    [Key]
    [Column(Order = 10)]
    public string Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the fleet suggestion.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [StringLength(50, MinimumLength = 3)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Gets or sets the description of the fleet suggestion.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;
}
