using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.DbSupportBaseClasses;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a record of a file in the system, including metadata such as content type, path, size, and upload date.
/// </summary>
public class FileRecord : LastChangeTrackingWithAutoInsertedIdBase
{
    public const string ID_PREFIX = "F";

    #region properties

    [Required]
    [Column(Order = 40)]
    public required string ContentType { get; set; }

    [Key]
    [MaxLength(10)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    public override string IdPrefix => ID_PREFIX;

    [Display(Name = "Note")]
    [Column(Order = 50)]
    public string? Note { get; set; }

    [Required]
    [MaxLength(255)]
    [Column(Order = 70)]
    public required string Path { get; set; } = String.Empty;

    [Column(Order = 30)]
    public long Size { get; set; }

    /// <summary>
    /// This can be displayed safely on the UI to the user
    /// </summary>
    [Display(Name = "File Name")]
    [Column(Order = 60)]
    public required string TrustedFileNameForDisplay { get; set; }

    /// <summary>
    /// This is the original file name, which may contain unsafe characters
    /// </summary>
    [Column(Order = 20)]
    public required string UntrustedName { get; set; }

    [Display(Name = "Uploaded (UTC)")]
    [Column(Order = 80)]
    public DateTimeOffset UploadDate { get; set; }

    #endregion
}
