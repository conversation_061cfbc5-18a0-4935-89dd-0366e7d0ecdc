using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler that denies authorization to all users.
/// This handler is useful for resources that should be temporarily restricted for all users.
/// </summary>
public class AuthorizeNoneHandler : AuthorizationHandler<AuthorizeNoneHandler.Requirement>
{
    private readonly ILogger<AuthorizeNoneHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="AuthorizeNoneHandler"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for this handler.</param>
    public AuthorizeNoneHandler(ILogger<AuthorizeNoneHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// The policy name used for the authorize none requirement.
    /// </summary>
    public static readonly string PolicyName = nameof(AuthorizeNoneHandler).Replace("Handler", "Policy");

    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, Requirement requirement)
    {
        _logger.LogInformation(
            "AuthorizeNoneHandler invoked for user {UserId}",
            context.User.Identity?.Name ?? "unknown"
        );

        // Always fail the requirement, regardless of user authentication status
        context.Fail();

        return Task.CompletedTask;
    }

    /// <summary>
    /// The authorization requirement for the authorize none policy.
    /// This class is used to identify the requirement type for the handler.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
