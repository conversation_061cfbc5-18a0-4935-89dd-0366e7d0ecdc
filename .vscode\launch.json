{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch ProScoring AppHost",
      "type": "dotnet",
      "request": "launch",
      "projectPath": "${workspaceFolder}/ProScoring.AppHost/ProScoring.AppHost.csproj",
      "preLaunchTask": "build",
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": "Watch",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/ProScoring.AppHost/bin/Debug/net9.0/ProScoring.AppHost.dll",
      "preLaunchTask": "build",
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "args": [
        "watch",
        "--project",
        ".",
        "--verbose"
      ],
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "stopAtEntry": false,
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Attach",
      "type": "coreclr",
      "request": "attach",
      "processId": "${command:pickProcess}"
    },
    {
      "name": "Z_old_Launch and Debug Blazor",
      "type": "blazorwasm",
      "request": "launch",
      "program": "${workspaceFolder}/ProScoring.AppHost/bin/Debug/net9.0/ProScoring.AppHost.dll",
      "cwd": "${workspaceFolder}",
      "hosted": true,
      "url": "http://localhost:5103",
      "browser": "edge",
      "webRoot": "${workspaceFolder}/ProScoring.Blazor",
    },
    {
      "name": "Z_old_Launch TestData.Playwright",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/ProScoring.TestData.Playwright/bin/Debug/net9.0/ProScoring.TestData.Playwright.dll",
      "args": [],
      "currentDir": "${workspaceFolder}/ProScoring.TestData.Playwright",
      "stopAtEntry": false,
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Z_old_TestData.Playwright - Users Only",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/ProScoring.TestData.Playwright/bin/Debug/net9.0/ProScoring.TestData.Playwright.dll",
      "args": [
        "--users"
      ],
      "currentDir": "${workspaceFolder}/ProScoring.TestData.Playwright",
      "stopAtEntry": false,
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Z_old_TestData.Playwright - OAs Only",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/ProScoring.TestData.Playwright/bin/Debug/net9.0/ProScoring.TestData.Playwright.dll",
      "args": [
        "--authorities"
      ],
      "currentDir": "${workspaceFolder}/ProScoring.TestData.Playwright",
      "stopAtEntry": false,
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  ]
}