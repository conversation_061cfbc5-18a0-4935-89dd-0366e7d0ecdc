@using Blazored.LocalStorage
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using ProScoring.Blazor.Components.Shared
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services
@using ProScoring.Domain.Dtos
@using ProScoring.Domain.Entities
@using Ra<PERSON>zen
@using Radzen.Blazor
@using System.Collections.Generic
@using System.Linq
@inject ILocalStorageServiceWithExpiration LocalStorage
@inject IJSRuntime JSRuntime
@inject IOrganizingAuthorityHttpClient OrganizingAuthorityClient

<style>
    .checkbox-filter-container {
        display: flex;
        flex-wrap: wrap;
        max-height: 200px;
        overflow-y: auto;
        padding: 0.5rem;
        border: var(--rz-input-border);
        border-radius: var(--rz-input-border-radius);
        background-color: var(--rz-base-background-color);
    }

    .checkbox-filter-item {
        display: flex;
        align-items: center;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
        min-width: 120px;
    }

    .checkbox-filter-item :deep(label) {
        margin-left: 0.5rem;
        margin-bottom: 0;
    }

    .filter-toggle-button {
        display: flex;
        align-items: left;
        width: 100%;
        padding: 0.75rem 1rem;
        background-color: var(--rz-base-background-color);
        border: var(--rz-border);
        border-radius: var(--rz-border-radius);
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .filter-toggle-button:hover {
        background-color: var(--rz-secondary-lighter);
    }

    .filter-toggle-button-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle-icon {
        transition: transform 0.3s ease;
    }

    .filter-toggle-icon.expanded {
        transform: rotate(180deg);
    }

    .filter-panel {
        overflow: hidden;
        transition: max-height 0.3s ease;
        max-height: 0;
        border-left: var(--rz-border);
        border-right: var(--rz-border);
        border-bottom: var(--rz-border);
        border-bottom-left-radius: var(--rz-border-radius);
        border-bottom-right-radius: var(--rz-border-radius);
    }

    .filter-panel.expanded {
        max-height: 500px;
        padding: 1rem;
    }

    /* Hide the dropdown button next to the plus button in Radzen DataFilter */
    .rz-datafilter .rz-datafilter-item .rz-splitbutton .rz-splitbutton-menubutton {
        display: none;
    }
</style>

<div class="organizing-authorities-list" style="padding: 1rem;">
    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" JustifyContent="JustifyContent.SpaceBetween"
            AlignItems="AlignItems.Start" Style="flex-wrap: wrap;">
            <div style="flex: 1 1 auto; min-width: 0;">
                <div class="filter-toggle-button" @onclick="ToggleFilterPanel"
                    @attributes="@("oa-filter-toggle-button".AsTestId())">
                    <div class="filter-toggle-icon @(isFilterPanelExpanded ? "expanded" : "")">
                        <RadzenIcon Icon="expand_more" />
                    </div>
                    <div class="filter-toggle-button-content">
                        <RadzenIcon Icon="@(filterExists ? "filter_list_alt" : "filter_alt")" />
                        <span>
                            @if (filterExists)
                            {
                                <text>Filters Applied (@oaCount of @totalOrganizingAuthorities total)</text>
                            }
                            else
                            {
                                <text>Filters</text>
                            }
                        </span>
                    </div>
                </div>
                <div class="filter-panel @(isFilterPanelExpanded ? "expanded" : "")">
                    <RadzenDataFilter @ref="dataFilter" Auto="@autoFilter" Data="@OAs"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TItem="OrganizingAuthorityInfoDto"
                        ViewChanged="@OnFilterViewChanged" @attributes="@("oa-data-filter".AsTestId())">
                        <Properties>
                            <RadzenDataFilterProperty Property="@nameof(OrganizingAuthorityInfoDto.Name)" Title="Name"
                                FilterOperator="FilterOperator.Contains" />
                            <RadzenDataFilterProperty Property="@nameof(OrganizingAuthorityInfoDto.City)" Title="City"
                                FilterOperator="FilterOperator.Contains" />
                            <RadzenDataFilterProperty Property="@nameof(OrganizingAuthorityInfoDto.State)" Title="State"
                                Type="typeof(IEnumerable<string>)" FilterOperator="FilterOperator.Contains"
                                FilterOperators="@(new[] { FilterOperator.Contains, FilterOperator.DoesNotContain })"
                                FilterValue="@selectedStates">
                                <FilterTemplate>
                                    <RadzenDropDown @bind-value=@selectedStates Style="width: 100%;" Multiple="true"
                                        Data="@stateOptions" AllowClear="true"
                                        Change="@(args => OnStateDropdownChange(args))"
                                        @attributes="@("oa-state-filter-dropdown".AsTestId())" />
                                </FilterTemplate>
                            </RadzenDataFilterProperty>
                            <RadzenDataFilterProperty Property="@nameof(OrganizingAuthorityInfoDto.Country)"
                                Title="Country" Type="typeof(IEnumerable<string>)"
                                FilterOperator="FilterOperator.Contains"
                                FilterOperators="@(new[] { FilterOperator.Contains, FilterOperator.DoesNotContain })"
                                FilterValue="@selectedCountries">
                                <FilterTemplate>
                                    <RadzenDropDown @bind-value=@selectedCountries Style="width: 100%;" Multiple="true"
                                        Data="@countryOptions" AllowClear="true"
                                        Change="@(args => OnCountryDropdownChange(args))"
                                        @attributes="@("oa-country-filter-dropdown".AsTestId())" />
                                </FilterTemplate>
                            </RadzenDataFilterProperty>
                        </Properties>
                    </RadzenDataFilter>
                </div>
            </div>

            <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                <RadzenSelectBar Value="@displayMode" Visible="@(!IsSmallScreen)"
                    @attributes="@("oa-display-mode-toggle".AsTestId())"
                    ValueChanged="@(async (DisplayMode value) => { displayMode = value; await OnDisplayModeChanged(value); })">
                    <Items>
                        <RadzenSelectBarItem Value="@DisplayMode.Card" Icon="view_module" />
                        <RadzenSelectBarItem Value="@DisplayMode.Row" Icon="view_list" />
                    </Items>
                </RadzenSelectBar>
            </RadzenStack>
        </RadzenStack>

        @if (isLoading)
        {
            <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate"
                Style="margin-bottom: 20px" @attributes="@("oa-loading-indicator".AsTestId())" />
        }
        else if (errorMessage != null)
        {
            <RadzenAlert AlertStyle="AlertStyle.Danger" ShowIcon="true" @attributes="@("oa-error-message".AsTestId())">
                @errorMessage
            </RadzenAlert>
        }
        else if (OAs == null || !OAs.Any())
        {
            <RadzenAlert AlertStyle="AlertStyle.Info" ShowIcon="true" @attributes="@("oa-no-results-message".AsTestId())">
                No organizing authorities found.
            </RadzenAlert>
        }
        <div
            style="@(isLoading
                                                                                                 || errorMessage != null
                                                                                                 || OAs == null || !OAs.Any() ? "display: none;" : null)">
            <RadzenDataList @ref="dataList" AllowVirtualization="false" WrapItems="true" AllowPaging="true"
                IsLoading="@isLoading" LoadData=@LoadAuthoritiesAsync Data="@OAs" Count=@totalOrganizingAuthorities
                TItem=OrganizingAuthorityInfoDto PageSize=@pageSize PagerHorizontalAlign="HorizontalAlign.Center"
                PageSizeOptions="@pageSizeOptions" ShowPagingSummary="true" class="v-scrollable-content">
                <Template Context="oa">
                    <OrganizingAuthorityInfo Authority="@oa" DisplayMode="@displayMode" ShowAdditionalInfo="false" />
                </Template>
            </RadzenDataList>
        </div>
    </RadzenStack>
</div>

@code {
    #region fields
    // General fields
    private const string PAGE_SIZE_SETTING_KEY = "organizingAuthoritiesPageSize";
    private const string DISPLAY_MODE_SETTING_KEY = "organizingAuthoritiesDisplayMode";
    private bool autoFilter = true;
    private List<string> countryOptions = new List<string>();
    private RadzenDataFilter<OrganizingAuthorityInfoDto>? dataFilter;
    private RadzenDataList<OrganizingAuthorityInfoDto>? dataList;
    private DisplayMode displayMode = DisplayMode.Card;
    private string? errorMessage;
    private bool isFilterPanelExpanded = false;
    private bool isLoading = true;
    private string? lastFilter;
    private List<OrganizingAuthorityInfoDto>? OAs;

    // Pagination fields
    private int oaCount = 0;
    private int pageSize = 10;
    private List<int> pageSizeOptions = new List<int> { 5, 10, 20, 50, 100 };

    // Filter fields
    private IEnumerable<string>? selectedCountries;
    private IEnumerable<string>? selectedStates;
    private List<string> stateOptions = new List<string>();
    private int totalOrganizingAuthorities = 0;
    private int windowWidth = 1200; // Default to desktop size
    #endregion fields

    #region constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="OrganizingAuthoritiesList"/> class.
    /// </summary>
    public OrganizingAuthoritiesList()
    {
        // Constructor logic if needed
    }
    #endregion constructors

    #region properties
    /// <summary>
    /// Gets or sets the CSS class for the component.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets whether any filters are currently applied.
    /// </summary>
    private bool filterExists => !string.IsNullOrEmpty(lastFilter);

    /// <summary>
    /// Gets whether the current screen size is considered small.
    /// </summary>
    private bool IsSmallScreen => windowWidth < 576; // SM breakpoint

    /// <summary>
    /// Gets or sets whether to show the display mode toggle.
    /// </summary>
    [Parameter]
    public bool ShowDisplayModeToggle { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to show the search box.
    /// </summary>
    [Parameter]
    public bool ShowSearch { get; set; } = true;
    #endregion properties

    #region lifecycle methods
    /// <summary>
    /// Cleans up event handlers when the component is disposed.
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        await JSRuntime.InvokeVoidAsync("window.removeEventListener", "resize",
        DotNetObjectReference.Create(this), "HandleWindowResize");
    }

    /// <summary>
    /// Sets up the resize event handler after the component is rendered.
    /// </summary>
    /// <param name="firstRender">Whether this is the first time the component is being rendered.</param>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("window.addEventListener", "resize",
            DotNetObjectReference.Create(this), "HandleWindowResize");

            // Get initial window width
            windowWidth = await JSRuntime.InvokeAsync<int>("getWindowWidth");

            // Add a default filter if needed
            if (dataFilter != null)
            {
                // Example: Add a default filter for USA country
                // await dataFilter.AddFilter(new CompositeFilterDescriptor()
                // {
                // Property = "Country",
                // FilterValue = "USA",
                // FilterOperator = FilterOperator.Equals
                // });
            }

            StateHasChanged();
        }

        // Equalize card dimensions whenever the component renders
        // Only do this when in card view and when we have data
        if (displayMode == DisplayMode.Card && OAs != null && OAs.Any() && !isLoading)
        {
            // Small delay to ensure the cards are fully rendered
            await Task.Delay(100);
            await JSRuntime.InvokeVoidAsync("equalizeCardDimensions");
        }
    }

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        // Load states and countries first
        await LoadFilterOptionsAsync();

        // Load total count of organizing authorities
        await LoadTotalCountAsync();

        await LoadSettingsFromLocalStorageAsync();
        await LoadAuthoritiesAsync();
    }
    #endregion lifecycle methods

    #region methods
    /// <summary>
    /// Handles window resize events from JavaScript.
    /// </summary>
    /// <param name="width">The new window width.</param>
    [JSInvokable]
    public void HandleWindowResize(int width)
    {
        windowWidth = width;

        // Force row view on small screens
        if (IsSmallScreen && displayMode == DisplayMode.Card)
        {
            displayMode = DisplayMode.Row;
            SaveDisplayModeAsync(displayMode).ConfigureAwait(false);
        }
        else if (!IsSmallScreen && displayMode == DisplayMode.Card && OAs != null && OAs.Any())
        {
            // If we're in card view and not on a small screen, equalize card dimensions
            JSRuntime.InvokeVoidAsync("equalizeCardDimensions").ConfigureAwait(false);
        }

        StateHasChanged();
    }

    /// <summary>
    /// Initializes filter options from the loaded authorities.
    /// This is a fallback method in case the API call fails.
    /// </summary>
    private void InitializeFilterOptions()
    {
        if (OAs == null)
            return;

        // Only initialize if the options are empty (API call failed)
        if (stateOptions.Count == 0)
        {
            // Extract unique states for dropdown options
            stateOptions = OAs
            .Where(a => !string.IsNullOrEmpty(a.State))
            .Select(a => a.State!)
            .Distinct()
            .OrderBy(s => s)
            .ToList();
        }

        if (countryOptions.Count == 0)
        {
            // Extract unique countries for dropdown options
            countryOptions = OAs
            .Where(a => !string.IsNullOrEmpty(a.Country))
            .Select(a => a.Country!)
            .Distinct()
            .OrderBy(c => c)
            .ToList();
        }
    }

    /// <summary>
    /// Loads organizing authorities from the controller using the HTTP client.
    /// </summary>
    private async Task LoadAuthoritiesAsync(LoadDataArgs? args = null)
    {
        try
        {
            args ??= new LoadDataArgs
            {
                Skip = 0,
                Top = pageSize
            };

            isLoading = true;
            errorMessage = null;
            StateHasChanged();

            // Build OData query
            var queryParts = new List<string>();

            // Add pagination
            queryParts.Add($"$skip={args.Skip}");
            queryParts.Add($"$top={args.Top}");

            // Add ordering
            queryParts.Add("$orderby=Name asc");

            // Add count to get total number of items
            queryParts.Add("$count=true");

            // Add filters from the DataFilter if any are applied
            var filterParts = new List<string>();

            // Add DataFilter filters
            var filter = dataFilter?.ToODataFilterString();
            lastFilter = filter;
            if (!string.IsNullOrWhiteSpace(filter))
                queryParts.Add($"$filter={filter}");

            // Combine query parts
            var query = string.Join("&", queryParts);

            // Get organizing authorities using the HTTP client
            var result = await OrganizingAuthorityClient.GetInfoWithODataQueryAsync(query);

            // Extract data and count from the result
            OAs = result.Items.ToList();
            oaCount = result.TotalCount;

            // Initialize filter options if needed
            if (stateOptions.Count == 0 || countryOptions.Count == 0)
            {
                InitializeFilterOptions();
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading organizing authorities: {ex.Message}";
        }
        finally
        {
            isLoading = false;

            // Use InvokeAsync to ensure we're on the correct thread for UI updates
            await InvokeAsync(async () =>
            {
                // Ensure UI is updated with the current page
                StateHasChanged();

                // If in card view, equalize card dimensions after loading
                if (displayMode == DisplayMode.Card && OAs != null && OAs.Any())
                {
                    // Small delay to allow the UI to update
                    await Task.Delay(100);
                    await JSRuntime.InvokeVoidAsync("equalizeCardDimensions");
                }
            });
        }
    }

    private async Task LoadSettingsFromLocalStorageAsync()
    {
        await LoadSavedDisplayModeAsync();
        await LoadPageSize();
    }

    /// <summary>
    /// Loads filter options (states and countries) from the API.
    /// </summary>
    private async Task LoadFilterOptionsAsync()
    {
        try
        {
            // Load states and countries from the API
            var statesTask = OrganizingAuthorityClient.GetAllStatesAsync();
            var countriesTask = OrganizingAuthorityClient.GetAllCountriesAsync();

            // Wait for both tasks to complete
            await Task.WhenAll(statesTask, countriesTask);

            // Get the results
            stateOptions = (await statesTask).ToList();
            countryOptions = (await countriesTask).ToList();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading filter options: {ex.Message}";
        }
    }

    private async Task LoadPageSize()
    {
        try
        {
            var savedPageSize = await LocalStorage.GetItemAsync<int?>(PAGE_SIZE_SETTING_KEY);
            if (savedPageSize.HasValue)
            {
                pageSize = savedPageSize.Value;
            }
        }
        catch
        {
            // Ignore errors reading from local storage
        }
    }

    /// <summary>
    /// Loads the saved display mode from local storage.
    /// </summary>
    private async Task LoadSavedDisplayModeAsync()
    {
        try
        {
            var savedMode = await LocalStorage.GetItemAsync<DisplayMode?>(DISPLAY_MODE_SETTING_KEY);
            if (savedMode.HasValue)
            {
                if (!IsSmallScreen || savedMode == DisplayMode.Row)
                {
                    displayMode = savedMode.Value;
                }
            }
            @*
            if (!string.IsNullOrEmpty(savedMode) && Enum.TryParse<DisplayMode>(savedMode, out var mode))
            {
                // Only apply the saved mode if we're not on a small screen or if the saved mode is Row
                if (!IsSmallScreen || mode == DisplayMode.Row)
                {
                    displayMode = mode;
                }
            }
            *@
        }
        catch
        {
            // Ignore errors reading from local storage
        }
    }

    /// <summary>
    /// Loads the total count of organizing authorities.
    /// </summary>
    private async Task LoadTotalCountAsync()
    {
        try
        {
            totalOrganizingAuthorities = await OrganizingAuthorityClient.GetTotalCountAsync();
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading total count: {ex.Message}";
        }
    }

    /// <summary>
    /// Handles country dropdown change events.
    /// </summary>
    /// <param name="value">The new value.</param>
    private void OnCountryDropdownChange(object value)
    {
        if (selectedCountries != null && !selectedCountries.Any())
        {
            selectedCountries = new List<string>();
        }
    }

    /// <summary>
    /// Handles display mode changes.
    /// </summary>
    /// <param name="value">The new display mode value.</param>
    private async Task OnDisplayModeChanged(DisplayMode value)
    {
        await SaveDisplayModeAsync(value);

        // If switching to card view, equalize card dimensions
        if (value == DisplayMode.Card && OAs != null && OAs.Any())
        {
            // Small delay to allow the UI to update
            await Task.Delay(100);
            await JSRuntime.InvokeVoidAsync("equalizeCardDimensions");
        }
    }

    /// <summary>
    /// Handles the filter view changed event from the DataFilter.
    /// </summary>
    private void OnFilterViewChanged()
    {
        if (selectedStates?.Any() == true && dataFilter?.Filters.Any(f => f.Property == "State") != true)
        {
            selectedStates = null;
        }

        if (selectedCountries?.Any() == true && dataFilter?.Filters.Any(f => f.Property == "Country") != true)
        {
            selectedCountries = null;
        }

        // Check if the filter has changed
        if (lastFilter == dataFilter?.ToODataFilterString())
            return;

        // Reload data with the new filters using InvokeAsync to ensure we're on the UI thread
        InvokeAsync(() => LoadAuthoritiesAsync()).ConfigureAwait(false);
    }

    /// <summary>
    /// Handles page size changes from the dropdown.
    /// </summary>
    /// <param name="newPageSize">The new page size.</param>
    private async Task OnPageSizeChanged(int newPageSize)
    {
        if (newPageSize != pageSize)
        {
            pageSize = newPageSize;
            await LocalStorage.SetItemAsync(PAGE_SIZE_SETTING_KEY, newPageSize.ToString());
        }
        await LoadAuthoritiesAsync();
    }

    /// <summary>
    /// Handles state dropdown change events.
    /// </summary>
    /// <param name="value">The new value.</param>
    private void OnStateDropdownChange(object value)
    {
        if (selectedStates != null && !selectedStates.Any())
        {
            selectedStates = null;
        }
    }

    /// <summary>
    /// Saves the current display mode to local storage when it changes.
    /// </summary>
    /// <param name="newMode">The new display mode.</param>
    private async Task SaveDisplayModeAsync(DisplayMode newMode)
    {
        try
        {
            await LocalStorage.SetItemAsync(DISPLAY_MODE_SETTING_KEY, newMode.ToString());
        }
        catch
        {
            // Ignore errors writing to local storage
        }
    }

    /// <summary>
    /// Toggles the visibility of the filter panel.
    /// </summary>
    private void ToggleFilterPanel()
    {
        isFilterPanelExpanded = !isFilterPanelExpanded;
        StateHasChanged();
    }
    #endregion methods
}
