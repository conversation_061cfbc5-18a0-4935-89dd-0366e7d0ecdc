using System.Reflection;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Blazor.Controllers;
using ProScoring.Infrastructure.ServiceInterfaces;
using Xunit;

namespace ProScoring.Tests.WebApi;

public class FileControllerAnonymousAccessTests
{
    private readonly FileController _controller;
    private readonly IFileService _mockFileService;
    private readonly IConfiguration _mockConfiguration;
    private readonly ILogger<FileController> _mockLogger;

    public FileControllerAnonymousAccessTests()
    {
        _mockFileService = Substitute.For<IFileService>();
        _mockConfiguration = Substitute.For<IConfiguration>();
        _mockLogger = Substitute.For<ILogger<FileController>>();

        _controller = new FileController(_mockFileService, _mockConfiguration, _mockLogger);
    }

    [Fact]
    public void Controller_HasAuthorizeAttribute()
    {
        // Arrange & Act
        var controllerType = typeof(FileController);
        var authorizeAttribute = controllerType.GetCustomAttribute<AuthorizeAttribute>();

        // Assert
        authorizeAttribute
            .Should()
            .NotBeNull("FileController should have an Authorize attribute at the controller level");
    }

    [Fact]
    public void Download_ShouldHaveAllowAnonymousAttribute()
    {
        // Arrange
        var methodInfo = typeof(FileController).GetMethod("Download");

        // Act
        var allowAnonymousAttribute = methodInfo?.GetCustomAttribute<AllowAnonymousAttribute>();

        // Assert
        allowAnonymousAttribute.Should().NotBeNull("Download method should have AllowAnonymous attribute");
    }

    [Fact]
    public void DownloadFileData_ShouldHaveAllowAnonymousAttribute()
    {
        // Arrange
        var methodInfo = typeof(FileController).GetMethod("DownloadFileData");

        // Act
        var allowAnonymousAttribute = methodInfo?.GetCustomAttribute<AllowAnonymousAttribute>();

        // Assert
        allowAnonymousAttribute.Should().NotBeNull("DownloadFileData method should have AllowAnonymous attribute");
    }
}
