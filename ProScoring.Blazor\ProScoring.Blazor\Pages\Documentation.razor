@page "/documentation"
@page "/documentation/{DocPath}"
@using System.Net.Http
@using System.Text.RegularExpressions
@using Markdig
@using Microsoft.AspNetCore.Components.Web
@inject IHttpClientFactory HttpClientFactory
@inject NavigationManager NavigationManager

<PageTitle>Documentation - ProScoring</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Documentation</h5>
                </div>
                <div class="card-body">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link @(string.IsNullOrEmpty(DocPath) ? "active" : "")"
                                href="/documentation">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(DocPath == "odata-api" ? "active" : "")"
                                href="/documentation/odata-api">OData API</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-body v-scrollable-content">
                    @if (isLoading)
                    {
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    }
                    else if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            @errorMessage
                        </div>
                    }
                    else
                    {
                        <div class="markdown-content">
                            @((MarkupString)htmlContent)
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
@code {
    [Parameter]
    public string? DocPath { get; set; }

    private string htmlContent = string.Empty;
    private string errorMessage = string.Empty;
    private bool isLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        await LoadDocumentationAsync();
    }

    private async Task LoadDocumentationAsync()
    {
        isLoading = true;
        errorMessage = string.Empty;

        try
        {
            // Create a client with the base address set to the current base URI
            var client = HttpClientFactory.CreateClient("DocumentationClient");
            client.BaseAddress = new Uri(NavigationManager.BaseUri);

            string path = string.IsNullOrEmpty(DocPath) ? "index" : DocPath;
            string markdownUrl = $"docs/{path}.md";

            var response = await client.GetAsync(markdownUrl);

            if (response.IsSuccessStatusCode)
            {
                string markdown = await response.Content.ReadAsStringAsync();

                // Convert markdown to HTML
                var pipeline = new MarkdownPipelineBuilder()
                .UseAdvancedExtensions()
                .Build();

                htmlContent = Markdown.ToHtml(markdown, pipeline);

                // Fix relative links
                htmlContent = FixRelativeLinks(htmlContent);
            }
            else
            {
                errorMessage = $"Documentation not found: {markdownUrl}";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading documentation: {ex.Message}";
        }
        finally
        {
            isLoading = false;
        }
    }

    private string FixRelativeLinks(string html)
    {
        // Replace relative markdown links with documentation page links
        return Regex.Replace(
        html,
        @"href=""([^/][^:""]+)\.md""",
        match => $"href=\"/documentation/{match.Groups[1].Value}\""
        );
    }
}
