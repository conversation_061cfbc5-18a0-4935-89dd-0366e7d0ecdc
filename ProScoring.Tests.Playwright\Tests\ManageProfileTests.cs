using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the user profile management functionality of the ProScoring application.
/// </summary>
public class ManageProfileTests : IClassFixture<PlaywrightFixture>
{
    protected readonly PlaywrightFixture _fixture;

    #region Constructors

    /// <summary>
    /// Initializes a new instance of the <see cref="ManageProfileTests"/> class.
    /// </summary>
    /// <param name="fixture">The Playwright fixture.</param>
    public ManageProfileTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }

    #endregion Constructors

    #region Test Methods

    /// <summary>
    /// Tests that a user can view their profile information.
    /// </summary>
    [Fact]
    public async Task ManagePage_DisplaysUserProfileInformation()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login first
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Act - Navigate to manage profile page
            var managePage = await NavigateToManageProfilePage(page);
            await managePage.VerifyPageLoadedAsync();

            // Assert - Profile information should be displayed
            var username = await managePage.GetUsernameAsync();
            username.Should().NotBeNullOrEmpty();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that a user can update their profile information.
    /// </summary>
    [Fact]
    public async Task ManagePage_CanUpdateProfileInformation()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login first
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            var managePage = await NavigateToManageProfilePage(page);
            await managePage.VerifyPageLoadedAsync();

            // Get original values for comparison
            var originalGivenName = await managePage.GetGivenNameAsync();
            var originalSurname = await managePage.GetSurnameAsync();
            var originalPhoneNumber = await managePage.GetPhoneNumberAsync();

            // Act - Update profile with new values
            var newGivenName = $"Test{System.DateTime.Now.Ticks % 10000}";
            var newSurname = $"User{System.DateTime.Now.Ticks % 10000}";
            var newPhoneNumber = $"555-{System.DateTime.Now.Ticks % 1000}-{System.DateTime.Now.Ticks % 10000}";

            await managePage.UpdateProfileAsync(newGivenName, newSurname, newPhoneNumber);

            // Assert - Status message should indicate success
            var statusMessage = await managePage.GetStatusMessageAsync();
            statusMessage.Should().Contain("profile has been updated");

            await managePage.NavigateAsync();

            var updatedGivenName = await managePage.GetGivenNameAsync();
            var updatedSurname = await managePage.GetSurnameAsync();
            var updatedPhoneNumber = await managePage.GetPhoneNumberAsync();

            updatedGivenName.Should().Be(newGivenName);
            updatedSurname.Should().Be(newSurname);
            updatedPhoneNumber.Should().Be(newPhoneNumber);

            await managePage.UpdateProfileAsync(originalGivenName, originalSurname, originalPhoneNumber);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that validation errors are displayed when invalid data is submitted.
    /// </summary>
    [Fact]
    public async Task ManagePage_DisplaysValidationErrorsForInvalidInput()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login first
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Navigate to manage page
            var manageProfilePage = new ManageProfilePage(page);
            await manageProfilePage.NavigateAsync();

            // Get original values to restore later
            var originalGivenName = await manageProfilePage.GetGivenNameAsync();
            var originalSurname = await manageProfilePage.GetSurnameAsync();
            var originalPhoneNumber = await manageProfilePage.GetPhoneNumberAsync();

            // Act - Submit invalid phone number
            string invalidPhoneNumber = "not-a-phone-number";
            await manageProfilePage.UpdateProfileAsync(phoneNumber: invalidPhoneNumber);

            // Assert - Validation errors should be displayed
            var errors = await manageProfilePage.GetValidationErrorsAsync();
            errors.Should().NotBeNull();
            errors.Should().Contain("Phone");

            // Restore original values
            await manageProfilePage.NavigateAsync(); // Navigate again to reset form
            await manageProfilePage.UpdateProfileAsync(originalGivenName, originalSurname, originalPhoneNumber);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that a user can update only their given name without changing other fields.
    /// </summary>
    [Fact]
    public async Task ManagePage_CanUpdateOnlyGivenName()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login first
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Navigate to manage page
            var manageProfilePage = new ManageProfilePage(page);
            await manageProfilePage.NavigateAsync();

            // Get original values
            var originalGivenName = await manageProfilePage.GetGivenNameAsync();
            var originalSurname = await manageProfilePage.GetSurnameAsync();
            var originalPhoneNumber = await manageProfilePage.GetPhoneNumberAsync();

            // Act - Update only given name
            string newGivenName = $"TestName{System.DateTime.Now.Ticks % 10000}";
            await manageProfilePage.UpdateProfileAsync(givenName: newGivenName);

            // Assert - Status message should indicate success
            var statusMessage = await manageProfilePage.GetStatusMessageAsync();
            statusMessage.Should().Contain("profile has been updated");

            // Refresh the page to verify changes were saved
            await manageProfilePage.NavigateAsync();

            // Verify only given name was updated, other fields remained the same
            var updatedGivenName = await manageProfilePage.GetGivenNameAsync();
            var updatedSurname = await manageProfilePage.GetSurnameAsync();
            var updatedPhoneNumber = await manageProfilePage.GetPhoneNumberAsync();

            updatedGivenName.Should().Be(newGivenName);
            updatedSurname.Should().Be(originalSurname);
            updatedPhoneNumber.Should().Be(originalPhoneNumber);

            // Restore original values
            await manageProfilePage.UpdateProfileAsync(originalGivenName);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that a user can update their profile with a blank phone number.
    /// </summary>
    [Fact]
    public async Task ManagePage_CanUpdateWithBlankPhoneNumber()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login first
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Navigate to manage page
            var manageProfilePage = new ManageProfilePage(page);
            await manageProfilePage.NavigateAsync();

            // Get original values to restore later
            var originalGivenName = await manageProfilePage.GetGivenNameAsync();
            var originalSurname = await manageProfilePage.GetSurnameAsync();
            var originalPhoneNumber = await manageProfilePage.GetPhoneNumberAsync();

            // Act - Update with blank phone number
            string blankPhoneNumber = "";
            await manageProfilePage.UpdateProfileAsync(originalGivenName, originalSurname, blankPhoneNumber);

            // Assert - Status message should indicate success
            var statusMessage = await manageProfilePage.GetStatusMessageAsync();
            statusMessage.Should().Contain("profile has been updated");

            // Refresh the page to verify changes were saved
            await manageProfilePage.NavigateAsync();

            // Verify phone number is blank
            var updatedPhoneNumber = await manageProfilePage.GetPhoneNumberAsync();
            updatedPhoneNumber.Should().BeEmpty();

            // Restore original values
            await manageProfilePage.UpdateProfileAsync(originalGivenName, originalSurname, originalPhoneNumber);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Navigates to the manage profile page.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <returns>The manage profile page object.</returns>
    private static async Task<ManageProfilePage> NavigateToManageProfilePage(IPage page)
    {
        var homePage = new HomePage(page);
        await homePage.ClickMyAccountMenuItem();
        await homePage.ClickAccountInfoOption();
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
        return new ManageProfilePage(page);
    }

    /// <summary>
    /// Gets the test page to use for the current test.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <returns>An instance of a ManageProfilePage.</returns>
    protected ManageProfilePage GetTestPage(IPage page)
    {
        return new ManageProfilePage(page);
    }

    #endregion Test Methods
}
