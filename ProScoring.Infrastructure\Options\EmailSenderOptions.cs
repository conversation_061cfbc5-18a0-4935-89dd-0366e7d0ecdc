namespace ProScoring.Infrastructure.Options;

/// <summary>
/// Configuration options for email sending services.
/// </summary>
public class EmailSenderOptions
{
    /// <summary>
    /// The configuration section name for email sender options.
    /// </summary>
    public const string SECTION_NAME = "EmailSender";

    /// <summary>
    /// Gets or sets the API key used for the email service provider.
    /// </summary>
    public string? ApiKey { get; set; }

    /// <summary>
    /// Gets or sets whether email sending is enabled.
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// Gets or sets the email address from which emails are sent.
    /// </summary>
    /// <remarks>
    /// This may be moved to a different setting for different types of email in the future.
    /// </remarks>
    public string FromAddress { get; set; } = null!;

    /// <summary>
    /// Gets or sets the display name for the sender of emails.
    /// </summary>
    /// <remarks>
    /// This may be moved to a different setting for different types of email in the future.
    /// </remarks>
    public string FromName { get; set; } = null!;
}
