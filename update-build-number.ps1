# PowerShell script to update build number in AssemblyInfo.cs files
# This script is called by the pre-commit hook to automatically increment
# the build number in assemblies that have changed, added, or deleted files.

# Check if --dry-run parameter is provided
$dryRun = $args -contains "--dry-run"
if ($dryRun) {
    Write-Host "Running in dry-run mode - no files will be modified" -ForegroundColor Cyan
}

# Get the repository root directory
$repoRoot = git rev-parse --show-toplevel

# Find all AssemblyInfo.cs files
$assemblyInfoFiles = Get-ChildItem -Path $repoRoot -Filter "AssemblyInfo.cs" -Recurse
Write-Host "Found $($assemblyInfoFiles.Count) AssemblyInfo.cs files" -ForegroundColor Green

# Function to extract version from AssemblyInfo.cs
function Get-AssemblyVersion {
    param (
        [string]$FilePath
    )

    $result = @{
        Major    = 0
        Minor    = 0
        Build    = 0
        Version  = "0.0.0"
    }

    try {
        $content = Get-Content -Path $FilePath -ErrorAction Stop
        $versionLine = $content | Where-Object { $_ -match 'AssemblyVersion\(' }

        if ($versionLine) {
            $versionMatch = [regex]::Match($versionLine, 'AssemblyVersion\("([^"]+)"')
            if ($versionMatch.Success) {
                $result.Version = $versionMatch.Groups[1].Value

                # Parse version components
                $versionParts = $result.Version.Split('.')
                if ($versionParts.Length -ge 3) {
                    $result.Major = [int]$versionParts[0]
                    $result.Minor = [int]$versionParts[1]
                    $result.Build = [int]$versionParts[2]
                }

                return $result
            }
        }
    }
    catch {
        Write-Host "Error reading version from ${FilePath}: $($_.Exception.Message)" -ForegroundColor Red
    }

    return $result
}

# We'll get the version for each assembly individually

# Get list of all staged files that are being committed (including added, modified, and deleted)
Write-Host "Checking for changed, added, and deleted files..." -ForegroundColor Cyan
$stagedFilesStatus = git diff --cached --name-status
$stagedFiles = @()

foreach ($fileStatus in $stagedFilesStatus) {
    # Split the status and filename
    $parts = $fileStatus.Split("`t")

    if ($parts.Length -ge 2) {
        $status = $parts[0].Trim()
        $filename = $parts[1].Trim()

        # Add the file to our list
        $stagedFiles += $filename

        # For debugging
        $statusText = switch ($status) {
            "A" { "Added" }
            "M" { "Modified" }
            "D" { "Deleted" }
            default { "Unknown status: $status" }
        }
        Write-Host "  $($statusText): $filename" -ForegroundColor Yellow
    }
}

# If no staged files, simulate some for testing in dry-run mode
if ($stagedFiles.Count -eq 0 -and $dryRun) {
    Write-Host "No staged files found. Simulating changes for testing..." -ForegroundColor Yellow
    $stagedFiles = @(
        "ProScoring.ApiService/Properties/AssemblyInfo.cs",
        "ProScoring.Blazor/ProScoring.Blazor/Components/Layout/MainLayout.razor"
    )
}

# For debugging
Write-Host "Staged files:" -ForegroundColor Cyan
foreach ($file in $stagedFiles) {
    Write-Host "  $file" -ForegroundColor Yellow
}

# Create a mapping of project directories to their AssemblyInfo.cs files
$projectToAssemblyInfo = @{}

foreach ($file in $assemblyInfoFiles) {
    # Get the project directory (parent of the directory containing AssemblyInfo.cs)
    $assemblyDir = Split-Path -Parent $file.FullName
    $projectDir = Split-Path -Parent $assemblyDir

    # Use the relative path from the repository root
    $relativePath = $projectDir.Substring($repoRoot.Length + 1).Replace("\", "/")

    # Store the mapping
    $projectToAssemblyInfo[$relativePath] = $file.FullName
}

# Track which assemblies need to be updated
$assembliesToUpdate = @{}

# Check each staged file to see which project it belongs to
foreach ($stagedFile in $stagedFiles) {
    # Check each project directory to see if the staged file is within it
    foreach ($projectDir in $projectToAssemblyInfo.Keys) {
        if ($stagedFile.StartsWith($projectDir)) {
            # This file belongs to this project, mark it for update
            $assembliesToUpdate[$projectDir] = $projectToAssemblyInfo[$projectDir]
            break
        }
    }
}

# If no assemblies need to be updated, exit
if ($assembliesToUpdate.Count -eq 0) {
    Write-Host "No assemblies need to be updated." -ForegroundColor Green
    exit 0
}

# Update only the AssemblyInfo.cs files for projects with changes
foreach ($projectDir in $assembliesToUpdate.Keys) {
    $assemblyInfoPath = $assembliesToUpdate[$projectDir]
    $content = Get-Content -Path $assemblyInfoPath

    # Get the current version from this assembly's AssemblyInfo.cs file
    $currentVersion = Get-AssemblyVersion -FilePath $assemblyInfoPath

    # Increment the build and revision numbers for this assembly
    $newBuild = $currentVersion.Build + 1

    # Create the new version strings
    $newVersion = "$($currentVersion.Major).$($currentVersion.Minor).$newBuild"
    $currentDateTime = (Get-Date).ToUniversalTime().ToString("yyyyMMddHHmm")
    $infoVersion = "$newVersion+$currentDateTime"

    Write-Host "Updating $projectDir from $($currentVersion.Major).$($currentVersion.Minor).$($currentVersion.Build) to $newVersion" -ForegroundColor Cyan

    # Update AssemblyVersion
    $content = $content -replace '\[assembly: AssemblyVersion\("[^"]+"\)\]', "[assembly: AssemblyVersion(`"$newVersion`")]"

    # Update AssemblyFileVersion
    $content = $content -replace '\[assembly: AssemblyFileVersion\("[^"]+"\)\]', "[assembly: AssemblyFileVersion(`"$newVersion`")]"

    # Check if AssemblyInformationalVersion already exists
    $hasInfoVersion = $content | Where-Object { $_ -match 'AssemblyInformationalVersion' }

    if ($hasInfoVersion) {
        # Update existing AssemblyInformationalVersion
        $content = $content -replace '\[assembly: AssemblyInformationalVersion\("[^"]+"\)\]', "[assembly: AssemblyInformationalVersion(`"$infoVersion`")]"
    }
    else {
        # Add AssemblyInformationalVersion after AssemblyFileVersion
        $content = $content -replace '(\[assembly: AssemblyFileVersion\("[^"]+"\)\])', "$1`n[assembly: AssemblyInformationalVersion(`"$infoVersion`")]"
    }

    # Write the updated content back to the file if not in dry-run mode
    if (-not $dryRun) {
        Set-Content -Path $assemblyInfoPath -Value $content
        Write-Host "Updated version for $projectDir to $newVersion" -ForegroundColor Green
    }
    else {
        Write-Host "Would update version for $projectDir to $newVersion" -ForegroundColor Yellow
    }
}

# For assemblies without changes, ensure AssemblyInformationalVersion exists
foreach ($file in $assemblyInfoFiles) {
    $filePath = $file.FullName
    $assemblyDir = Split-Path -Parent $filePath
    $projectDir = Split-Path -Parent $assemblyDir
    $relativePath = $projectDir.Substring($repoRoot.Length + 1).Replace("\", "/")

    # Skip files that were already updated
    if ($assembliesToUpdate.ContainsKey($relativePath)) {
        continue
    }

    $content = Get-Content -Path $filePath
    $hasInfoVersion = $content | Where-Object { $_ -match 'AssemblyInformationalVersion' }

    if (-not $hasInfoVersion) {
        # Extract the current version from AssemblyVersion
        $version = Get-AssemblyVersion -FilePath $filePath

        # Add placeholder timestamp
        $infoVersion = "$($version.Version)+000000000000"
        $content = $content -replace '(\[assembly: AssemblyFileVersion\("[^"]+"\)\])', "$1`n[assembly: AssemblyInformationalVersion(`"$infoVersion`")]"

        # Write the updated content back to the file if not in dry-run mode
        if (-not $dryRun) {
            Set-Content -Path $filePath -Value $content
            Write-Host "Added AssemblyInformationalVersion to $relativePath" -ForegroundColor Green
        }
        else {
            Write-Host "Would add AssemblyInformationalVersion to $relativePath" -ForegroundColor Yellow
        }
    }
}

# Re-add all modified files to git staging
if (-not $dryRun -and $assembliesToUpdate.Count -gt 0) {
    Write-Host "Re-adding modified AssemblyInfo.cs files to git staging..." -ForegroundColor Cyan

    foreach ($projectDir in $assembliesToUpdate.Keys) {
        $assemblyInfoPath = $assembliesToUpdate[$projectDir]
        # Convert to relative path for git
        $relativePath = $assemblyInfoPath.Substring($repoRoot.Length + 1).Replace("\", "/")
        git add $relativePath
        Write-Host "  Added $relativePath" -ForegroundColor Green
    }

    Write-Host "Updated versions for assemblies with changes, additions, or deletions" -ForegroundColor Green
}
else {
    Write-Host "No changes were made to any files." -ForegroundColor Green
}
