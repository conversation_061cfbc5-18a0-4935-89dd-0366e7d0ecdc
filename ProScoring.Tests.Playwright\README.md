# ProScoring Playwright Tests

This project contains automated UI tests for the ProScoring web application using Playwright.

## Setup

1. Ensure you have the .NET 9 SDK installed.
2. Make sure you have PowerShell installed for Windows or Bash for Linux/macOS.
3. The project will automatically install Playwright browsers upon restore.

## Running Tests

### From Visual Studio:
1. Open the solution in Visual Studio.
2. Make sure the ProScoring application is running (or will be started by the test project).
3. Open Test Explorer and run the tests.

### From VS Code:
1. Open the repository in VS Code.
2. Ensure you have the C# Dev Kit extension installed.
3. Open a terminal and run:
   ```
   cd c:\_dev\ProScoring\ProScoringNet9\ProScoring.Tests.Playwright
   dotnet test
   ```

### From Command Line:
```
cd c:\_dev\ProScoring\ProScoringNet9\ProScoring.Tests.Playwright
dotnet test
```

## Configuration

The test settings are configured in `appsettings.Test.json`. You can modify:
- `BaseUrl`: The URL of the application to test
- `TestUserEmail` and `TestUserPassword`: Credentials for login tests
- `Headless`: Run browsers in headless mode (true/false)

## Test Structure

- `PageObjects`: Contains classes that model each page in the application
- `TestFixtures`: Contains the Playwright fixture for browser management
- `Tests`: Contains the actual test classes grouped by functionality

## Viewing Test Results

After running tests, HTML reports are generated in the `test-results` directory.
To view reports, open the HTML files in any browser.

## Adding New Tests

1. Create page objects for new pages in the `PageObjects` directory
2. Create test classes in the `Tests` directory
3. Use the existing tests as templates for new tests

## CI/CD Integration

These tests can be integrated into your CI/CD pipeline. Ensure the pipeline:
1. Installs .NET 9 SDK
2. Installs Playwright browsers
3. Runs the tests with `dotnet test`
4. Publishes test results as artifacts
