﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using ProScoring.Infrastructure.Database;

#nullable disable

namespace ProScoring.Infrastructure.Database.SQLite_Migrations
{
    [DbContext(typeof(SqliteCreationApplicationDbContext))]
    [Migration("20250320225426_SQLite_InitialCreate")]
    partial class SQLite_InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.2");

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(1000);

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("GivenName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(60);

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("Surname")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("UpdatedById");

                    b.ToTable("AspNetUsers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = "U000000000",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "00000000-0000-0000-0000-000000000000",
                            CreatedAt = new DateTimeOffset(new DateTime(2025, 1, 1, 1, 1, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            CreatedById = "U000000000",
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            GivenName = "<EMAIL>",
                            LockoutEnabled = true,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "<EMAIL>",
                            PasswordHash = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
                            Surname = "<EMAIL>",
                            TwoFactorEnabled = false,
                            UpdatedAt = new DateTimeOffset(new DateTime(2025, 1, 1, 1, 1, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)),
                            UpdatedById = "U000000000",
                            UserName = "<EMAIL>"
                        });
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Boat", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("BoatType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("CountryCodeId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<float?>("Length")
                        .HasColumnType("REAL")
                        .HasColumnOrder(60);

                    b.Property<int?>("LengthUnit")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(70);

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("OwnerId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<string>("SailNumber")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CountryCodeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("OwnerId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Boat");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Competitor", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("Address")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("Address2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(60);

                    b.Property<DateOnly?>("BirthDate")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(110);

                    b.Property<string>("City")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<string>("Country")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(100);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Email")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("EmergencyContactName1")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(150);

                    b.Property<string>("EmergencyContactName2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(170);

                    b.Property<string>("EmergencyContactPhone1")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(160);

                    b.Property<string>("EmergencyContactPhone2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(180);

                    b.Property<string>("MemberNationalAuthority")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(130);

                    b.Property<string>("MnaNumber")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(140);

                    b.Property<string>("Name")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(190);

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("State")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(15);

                    b.Property<string>("WorldSailingNumber")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(120);

                    b.Property<string>("ZipCode")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(90);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("UserId");

                    b.ToTable("Competitor");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.CountryCode", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(3)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.HasKey("Code");

                    b.ToTable("CountryCode");

                    b.HasData(
                        new
                        {
                            Code = "AHO",
                            Name = "Netherlands Antilles"
                        },
                        new
                        {
                            Code = "ALG",
                            Name = "Algeria"
                        },
                        new
                        {
                            Code = "AND",
                            Name = "Andorra"
                        },
                        new
                        {
                            Code = "ANG",
                            Name = "Angola"
                        },
                        new
                        {
                            Code = "ANT",
                            Name = "Antigua and Barbuda"
                        },
                        new
                        {
                            Code = "ARG",
                            Name = "Argentina"
                        },
                        new
                        {
                            Code = "ARM",
                            Name = "Armenia"
                        },
                        new
                        {
                            Code = "ARU",
                            Name = "Aruba"
                        },
                        new
                        {
                            Code = "ASA",
                            Name = "American Samoa"
                        },
                        new
                        {
                            Code = "AUS",
                            Name = "Australia"
                        },
                        new
                        {
                            Code = "AUT",
                            Name = "Austria"
                        },
                        new
                        {
                            Code = "AZE",
                            Name = "Azerbaijan"
                        },
                        new
                        {
                            Code = "BAH",
                            Name = "Bahamas"
                        },
                        new
                        {
                            Code = "BAR",
                            Name = "Barbados"
                        },
                        new
                        {
                            Code = "BEL",
                            Name = "Belgium"
                        },
                        new
                        {
                            Code = "BER",
                            Name = "Bermuda"
                        },
                        new
                        {
                            Code = "BIZ",
                            Name = "Belize"
                        },
                        new
                        {
                            Code = "BLR",
                            Name = "Belarus"
                        },
                        new
                        {
                            Code = "BOL",
                            Name = "Bolivia"
                        },
                        new
                        {
                            Code = "BOT",
                            Name = "Botswana"
                        },
                        new
                        {
                            Code = "BRA",
                            Name = "Brazil"
                        },
                        new
                        {
                            Code = "BRN",
                            Name = "Bahrain"
                        },
                        new
                        {
                            Code = "BRU",
                            Name = "Brunei"
                        },
                        new
                        {
                            Code = "BUL",
                            Name = "Bulgaria"
                        },
                        new
                        {
                            Code = "CAM",
                            Name = "Cambodia"
                        },
                        new
                        {
                            Code = "CAN",
                            Name = "Canada"
                        },
                        new
                        {
                            Code = "CAY",
                            Name = "Cayman Islands"
                        },
                        new
                        {
                            Code = "CHI",
                            Name = "Chile"
                        },
                        new
                        {
                            Code = "CHN",
                            Name = "China"
                        },
                        new
                        {
                            Code = "COK",
                            Name = "Cook Islands"
                        },
                        new
                        {
                            Code = "COL",
                            Name = "Colombia"
                        },
                        new
                        {
                            Code = "CRO",
                            Name = "Croatia"
                        },
                        new
                        {
                            Code = "CUB",
                            Name = "Cuba"
                        },
                        new
                        {
                            Code = "CYP",
                            Name = "Cyprus"
                        },
                        new
                        {
                            Code = "CZE",
                            Name = "Czech Republic"
                        },
                        new
                        {
                            Code = "DEN",
                            Name = "Denmark"
                        },
                        new
                        {
                            Code = "DJI",
                            Name = "Djibouti"
                        },
                        new
                        {
                            Code = "DOM",
                            Name = "Dominican Republic"
                        },
                        new
                        {
                            Code = "ECU",
                            Name = "Ecuador"
                        },
                        new
                        {
                            Code = "EGY",
                            Name = "Egypt"
                        },
                        new
                        {
                            Code = "ESA",
                            Name = "El Salvador"
                        },
                        new
                        {
                            Code = "ESP",
                            Name = "Spain"
                        },
                        new
                        {
                            Code = "EST",
                            Name = "Estonia"
                        },
                        new
                        {
                            Code = "FIJ",
                            Name = "Fiji"
                        },
                        new
                        {
                            Code = "FIN",
                            Name = "Finland"
                        },
                        new
                        {
                            Code = "FRA",
                            Name = "France"
                        },
                        new
                        {
                            Code = "GBR",
                            Name = "Great Britain"
                        },
                        new
                        {
                            Code = "GEO",
                            Name = "Georgia"
                        },
                        new
                        {
                            Code = "GER",
                            Name = "Germany"
                        },
                        new
                        {
                            Code = "GRE",
                            Name = "Greece"
                        },
                        new
                        {
                            Code = "GRN",
                            Name = "Grenada"
                        },
                        new
                        {
                            Code = "GUA",
                            Name = "Guatemala"
                        },
                        new
                        {
                            Code = "GUM",
                            Name = "Guam"
                        },
                        new
                        {
                            Code = "HKG",
                            Name = "Hong Kong"
                        },
                        new
                        {
                            Code = "HUN",
                            Name = "Hungary"
                        },
                        new
                        {
                            Code = "INA",
                            Name = "Indonesia"
                        },
                        new
                        {
                            Code = "IND",
                            Name = "India"
                        },
                        new
                        {
                            Code = "IRI",
                            Name = "Iran"
                        },
                        new
                        {
                            Code = "IRL",
                            Name = "Ireland"
                        },
                        new
                        {
                            Code = "IRQ",
                            Name = "Iraq"
                        },
                        new
                        {
                            Code = "ISL",
                            Name = "Iceland"
                        },
                        new
                        {
                            Code = "ISR",
                            Name = "Israel"
                        },
                        new
                        {
                            Code = "ISV",
                            Name = "U.S. Virgin Islands"
                        },
                        new
                        {
                            Code = "ITA",
                            Name = "Italy"
                        },
                        new
                        {
                            Code = "IVB",
                            Name = "British Virgin Islands"
                        },
                        new
                        {
                            Code = "JAM",
                            Name = "Jamaica"
                        },
                        new
                        {
                            Code = "JOR",
                            Name = "Jordan"
                        },
                        new
                        {
                            Code = "JPN",
                            Name = "Japan"
                        },
                        new
                        {
                            Code = "KAZ",
                            Name = "Kazakhstan"
                        },
                        new
                        {
                            Code = "KEN",
                            Name = "Kenya"
                        },
                        new
                        {
                            Code = "KGZ",
                            Name = "Kyrgyzstan"
                        },
                        new
                        {
                            Code = "KOR",
                            Name = "South Korea"
                        },
                        new
                        {
                            Code = "KOS",
                            Name = "Kosovo"
                        },
                        new
                        {
                            Code = "KSA",
                            Name = "Saudi Arabia"
                        },
                        new
                        {
                            Code = "KUW",
                            Name = "Kuwait"
                        },
                        new
                        {
                            Code = "LAT",
                            Name = "Latvia"
                        },
                        new
                        {
                            Code = "LBA",
                            Name = "Libya"
                        },
                        new
                        {
                            Code = "LCA",
                            Name = "Saint Lucia"
                        },
                        new
                        {
                            Code = "LIB",
                            Name = "Lebanon"
                        },
                        new
                        {
                            Code = "LIE",
                            Name = "Liechtenstein"
                        },
                        new
                        {
                            Code = "LTU",
                            Name = "Lithuania"
                        },
                        new
                        {
                            Code = "LUX",
                            Name = "Luxembourg"
                        },
                        new
                        {
                            Code = "MAC",
                            Name = "Macau"
                        },
                        new
                        {
                            Code = "MAD",
                            Name = "Madagascar"
                        },
                        new
                        {
                            Code = "MAR",
                            Name = "Morocco"
                        },
                        new
                        {
                            Code = "MAS",
                            Name = "Malaysia"
                        },
                        new
                        {
                            Code = "MDA",
                            Name = "Moldova"
                        },
                        new
                        {
                            Code = "MEX",
                            Name = "Mexico"
                        },
                        new
                        {
                            Code = "MKD",
                            Name = "North Macedonia"
                        },
                        new
                        {
                            Code = "MLT",
                            Name = "Malta"
                        },
                        new
                        {
                            Code = "MNE",
                            Name = "Montenegro"
                        },
                        new
                        {
                            Code = "MNT",
                            Name = "Montserrat"
                        },
                        new
                        {
                            Code = "MON",
                            Name = "Monaco"
                        },
                        new
                        {
                            Code = "MOZ",
                            Name = "Mozambique"
                        },
                        new
                        {
                            Code = "MRI",
                            Name = "Mauritius"
                        },
                        new
                        {
                            Code = "MYA",
                            Name = "Myanmar"
                        },
                        new
                        {
                            Code = "NAM",
                            Name = "Namibia"
                        },
                        new
                        {
                            Code = "NCA",
                            Name = "Nicaragua"
                        },
                        new
                        {
                            Code = "NED",
                            Name = "Netherlands"
                        },
                        new
                        {
                            Code = "NGR",
                            Name = "Nigeria"
                        },
                        new
                        {
                            Code = "NOR",
                            Name = "Norway"
                        },
                        new
                        {
                            Code = "NZL",
                            Name = "New Zealand"
                        },
                        new
                        {
                            Code = "OMA",
                            Name = "Oman"
                        },
                        new
                        {
                            Code = "PAK",
                            Name = "Pakistan"
                        },
                        new
                        {
                            Code = "PAN",
                            Name = "Panama"
                        },
                        new
                        {
                            Code = "PAR",
                            Name = "Paraguay"
                        },
                        new
                        {
                            Code = "PER",
                            Name = "Peru"
                        },
                        new
                        {
                            Code = "PHI",
                            Name = "Philippines"
                        },
                        new
                        {
                            Code = "PLE",
                            Name = "Palestine"
                        },
                        new
                        {
                            Code = "PNG",
                            Name = "Papua New Guinea"
                        },
                        new
                        {
                            Code = "POL",
                            Name = "Poland"
                        },
                        new
                        {
                            Code = "POR",
                            Name = "Portugal"
                        },
                        new
                        {
                            Code = "PRK",
                            Name = "North Korea"
                        },
                        new
                        {
                            Code = "PUR",
                            Name = "Puerto Rico"
                        },
                        new
                        {
                            Code = "QAT",
                            Name = "Qatar"
                        },
                        new
                        {
                            Code = "ROU",
                            Name = "Romania"
                        },
                        new
                        {
                            Code = "RSA",
                            Name = "South Africa"
                        },
                        new
                        {
                            Code = "RUS",
                            Name = "Russia"
                        },
                        new
                        {
                            Code = "SAM",
                            Name = "Samoa"
                        },
                        new
                        {
                            Code = "SEN",
                            Name = "Senegal"
                        },
                        new
                        {
                            Code = "SEY",
                            Name = "Seychelles"
                        },
                        new
                        {
                            Code = "SGP",
                            Name = "Singapore"
                        },
                        new
                        {
                            Code = "SKN",
                            Name = "Saint Kitts and Nevis"
                        },
                        new
                        {
                            Code = "SLO",
                            Name = "Slovenia"
                        },
                        new
                        {
                            Code = "SMR",
                            Name = "San Marino"
                        },
                        new
                        {
                            Code = "SOL",
                            Name = "Solomon Islands"
                        },
                        new
                        {
                            Code = "SRB",
                            Name = "Serbia"
                        },
                        new
                        {
                            Code = "SRI",
                            Name = "Sri Lanka"
                        },
                        new
                        {
                            Code = "SUD",
                            Name = "Sudan"
                        },
                        new
                        {
                            Code = "SUI",
                            Name = "Switzerland"
                        },
                        new
                        {
                            Code = "SVK",
                            Name = "Slovakia"
                        },
                        new
                        {
                            Code = "SWE",
                            Name = "Sweden"
                        },
                        new
                        {
                            Code = "TAH",
                            Name = "French Polynesia"
                        },
                        new
                        {
                            Code = "TAN",
                            Name = "Tanzania"
                        },
                        new
                        {
                            Code = "TCA",
                            Name = "Turks and Caicos Islands"
                        },
                        new
                        {
                            Code = "TGA",
                            Name = "Tonga"
                        },
                        new
                        {
                            Code = "THA",
                            Name = "Thailand"
                        },
                        new
                        {
                            Code = "TJK",
                            Name = "Tajikistan"
                        },
                        new
                        {
                            Code = "TLS",
                            Name = "Timor-Leste"
                        },
                        new
                        {
                            Code = "TPE",
                            Name = "Chinese Taipei"
                        },
                        new
                        {
                            Code = "TTO",
                            Name = "Trinidad and Tobago"
                        },
                        new
                        {
                            Code = "TUN",
                            Name = "Tunisia"
                        },
                        new
                        {
                            Code = "TUR",
                            Name = "Turkey"
                        },
                        new
                        {
                            Code = "UAE",
                            Name = "United Arab Emirates"
                        },
                        new
                        {
                            Code = "UGA",
                            Name = "Uganda"
                        },
                        new
                        {
                            Code = "UKR",
                            Name = "Ukraine"
                        },
                        new
                        {
                            Code = "URU",
                            Name = "Uruguay"
                        },
                        new
                        {
                            Code = "USA",
                            Name = "United States"
                        },
                        new
                        {
                            Code = "VAN",
                            Name = "Vanuatu"
                        },
                        new
                        {
                            Code = "VEN",
                            Name = "Venezuela"
                        },
                        new
                        {
                            Code = "VIE",
                            Name = "Vietnam"
                        },
                        new
                        {
                            Code = "VIN",
                            Name = "Saint Vincent and the Grenadines"
                        },
                        new
                        {
                            Code = "ZIM",
                            Name = "Zimbabwe"
                        });
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.FileRecord", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Note")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<long>("Size")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(30);

                    b.Property<string>("TrustedFileNameForDisplay")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(60);

                    b.Property<string>("UntrustedName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<DateTimeOffset>("UploadDate")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Files");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.OrganizingAuthority", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<string>("AddressLine2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(90);

                    b.Property<string>("Country")
                        .HasMaxLength(60)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(120);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Email")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("ImageId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(130);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("PostalCode")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(110);

                    b.Property<bool>("Private")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(60);

                    b.Property<string>("State")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(100);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("Website")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ImageId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("OrganizingAuthorities");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Rating", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("BoatId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("RatingNote")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("RatingTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("BoatId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RatingTypeId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Rating");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingType", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RatingType");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingValue", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("RatingId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<float>("Value")
                        .HasColumnType("REAL")
                        .HasColumnOrder(40);

                    b.Property<string>("ValueTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RatingId");

                    b.HasIndex("UpdatedById");

                    b.HasIndex("ValueTypeId");

                    b.ToTable("RatingValue");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingValueType", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("RatingTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RatingTypeId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RatingValueType");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.LinkType", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(25)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.HasKey("Id");

                    b.ToTable("LinkType");

                    b.HasData(
                        new
                        {
                            Id = "facebook",
                            Name = "Facebook Link"
                        },
                        new
                        {
                            Id = "instagram",
                            Name = "Instagram Link"
                        },
                        new
                        {
                            Id = "bluesky",
                            Name = "BlueSky Link"
                        },
                        new
                        {
                            Id = "twitter",
                            Name = "X/Twitter Link"
                        },
                        new
                        {
                            Id = "whatsapp",
                            Name = "WhatsApp Link"
                        },
                        new
                        {
                            Id = "tracker",
                            Name = "Tracker Link"
                        });
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.Regatta", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("AddressLine1")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(90);

                    b.Property<string>("AddressLine2")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(100);

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(110);

                    b.PrimitiveCollection<string>("CompetitionDays")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(60);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<DateOnly>("EndDate")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(130);

                    b.Property<string>("EventLogoId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(120);

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(140);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("OrganizingAuthorityId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<bool>("Private")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(150);

                    b.Property<DateTimeOffset?>("RegistrationDeadline")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<DateTimeOffset?>("RegistrationOpening")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<DateOnly>("StartDate")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(160);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(170);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EventLogoId");

                    b.HasIndex("OrganizingAuthorityId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("Regattas");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaBoat", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("BoatType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("CountryCodeId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(15);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<float?>("Length")
                        .HasColumnType("REAL")
                        .HasColumnOrder(50);

                    b.Property<int?>("LengthUnit")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(60);

                    b.Property<string>("MasterBoatId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("RegattaClassId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(90);

                    b.Property<string>("RegattaFleetId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<string>("SailNumber")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("Skipper2Id")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(110);

                    b.Property<string>("SkipperId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(100);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CountryCodeId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("MasterBoatId");

                    b.HasIndex("RegattaClassId");

                    b.HasIndex("RegattaFleetId");

                    b.HasIndex("RegattaId");

                    b.HasIndex("Skipper2Id");

                    b.HasIndex("SkipperId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaBoats");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaBoatCompetitor", b =>
                {
                    b.Property<string>("RegattaBoatId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RegattaCompetitorId")
                        .HasColumnType("TEXT");

                    b.Property<int>("Id")
                        .HasColumnType("INTEGER");

                    b.HasKey("RegattaBoatId", "RegattaCompetitorId");

                    b.HasIndex("RegattaCompetitorId");

                    b.ToTable("RegattaBoatCompetitor");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaClass", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("RegattaFleetId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RegattaFleetId");

                    b.HasIndex("RegattaId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaClasses");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("Address")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(60);

                    b.Property<string>("Address2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(70);

                    b.Property<DateOnly?>("BirthDate")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(120);

                    b.Property<string>("City")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(80);

                    b.Property<string>("CompetitorId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("Country")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(110);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Email")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("EmergencyContactName1")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(160);

                    b.Property<string>("EmergencyContactName2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(180);

                    b.Property<string>("EmergencyContactPhone1")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(170);

                    b.Property<string>("EmergencyContactPhone2")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(190);

                    b.Property<string>("MemberNationalAuthority")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(140);

                    b.Property<string>("MnaNumber")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(150);

                    b.Property<string>("Name")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(200);

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<string>("State")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(90);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("WorldSailingNumber")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(130);

                    b.Property<string>("ZipCode")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(100);

                    b.HasKey("Id");

                    b.HasIndex("CompetitorId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RegattaId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaCompetitors");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaExternalLink", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("LinkTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("RegattaId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LinkTypeId");

                    b.HasIndex("RegattaId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaExternalLinks");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaFleet", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<bool>("CrewRequired")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(110);

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<bool>("RatingRequired")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(100);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RegattaId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaFleets");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaRating", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<bool>("KeepSynced")
                        .HasColumnType("INTEGER")
                        .HasColumnOrder(60);

                    b.Property<string>("RatingNote")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("RatingTypeId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.Property<string>("RegattaBoatId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<string>("SourceRatingId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RatingTypeId");

                    b.HasIndex("RegattaBoatId");

                    b.HasIndex("RegattaId");

                    b.HasIndex("SourceRatingId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaRatings");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaRatingValue", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(12)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1050);

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1040);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(50);

                    b.Property<string>("RegattaId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RegattaRatingId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1020);

                    b.Property<string>("UpdatedById")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(1010);

                    b.Property<decimal>("Value")
                        .HasPrecision(10, 4)
                        .HasColumnType("TEXT")
                        .HasColumnOrder(40);

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RegattaId");

                    b.HasIndex("RegattaRatingId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("RegattaRatingValues");
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.ActionHierarchy", b =>
                {
                    b.Property<string>("ParentActionName")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("ChildActionName")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(20);

                    b.HasKey("ParentActionName", "ChildActionName");

                    b.HasIndex("ChildActionName");

                    b.ToTable("ActionHierarchies");

                    b.HasData(
                        new
                        {
                            ParentActionName = "Admin",
                            ChildActionName = "Del"
                        },
                        new
                        {
                            ParentActionName = "Admin",
                            ChildActionName = "Edit"
                        },
                        new
                        {
                            ParentActionName = "Admin",
                            ChildActionName = "View"
                        },
                        new
                        {
                            ParentActionName = "Del",
                            ChildActionName = "Edit"
                        },
                        new
                        {
                            ParentActionName = "Del",
                            ChildActionName = "View"
                        },
                        new
                        {
                            ParentActionName = "Edit",
                            ChildActionName = "View"
                        },
                        new
                        {
                            ParentActionName = "HMFIC",
                            ChildActionName = "Edit"
                        },
                        new
                        {
                            ParentActionName = "HMFIC",
                            ChildActionName = "Del"
                        },
                        new
                        {
                            ParentActionName = "HMFIC",
                            ChildActionName = "Admin"
                        },
                        new
                        {
                            ParentActionName = "HMFIC",
                            ChildActionName = "View"
                        });
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.AuthAction", b =>
                {
                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Name");

                    b.ToTable("AuthActions");

                    b.HasData(
                        new
                        {
                            Name = "Admin"
                        },
                        new
                        {
                            Name = "Del"
                        },
                        new
                        {
                            Name = "Edit"
                        },
                        new
                        {
                            Name = "View"
                        },
                        new
                        {
                            Name = "HMFIC"
                        });
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.TargetType", b =>
                {
                    b.Property<string>("IdPrefix")
                        .HasMaxLength(5)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("IdPrefix");

                    b.ToTable("TargetTypes");

                    b.HasData(
                        new
                        {
                            IdPrefix = "*",
                            Name = "*"
                        },
                        new
                        {
                            IdPrefix = "O",
                            Name = "OrganizingAuthority"
                        },
                        new
                        {
                            IdPrefix = "G",
                            Name = "Regatta"
                        });
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.UserAuthAction", b =>
                {
                    b.Property<string>("AuthActionName")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(10);

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("TargetId")
                        .HasColumnType("TEXT")
                        .HasColumnOrder(30);

                    b.HasKey("AuthActionName", "UserId", "TargetId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAuthActions");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.ApplicationUser", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Boat", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.CountryCode", "CountryCode")
                        .WithMany()
                        .HasForeignKey("CountryCodeId");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CountryCode");

                    b.Navigation("CreatedBy");

                    b.Navigation("Owner");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Competitor", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.FileRecord", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.OrganizingAuthority", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.FileRecord", "Image")
                        .WithMany()
                        .HasForeignKey("ImageId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Image");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Rating", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.Boat", "Boat")
                        .WithMany("Ratings")
                        .HasForeignKey("BoatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RatingType", "RatingType")
                        .WithMany()
                        .HasForeignKey("RatingTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Boat");

                    b.Navigation("CreatedBy");

                    b.Navigation("RatingType");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingType", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingValue", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.Rating", "Rating")
                        .WithMany("RatingValues")
                        .HasForeignKey("RatingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.HasOne("ProScoring.Domain.Entities.RatingValueType", "ValueType")
                        .WithMany()
                        .HasForeignKey("ValueTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Rating");

                    b.Navigation("UpdatedBy");

                    b.Navigation("ValueType");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RatingValueType", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RatingType", "RatingType")
                        .WithMany()
                        .HasForeignKey("RatingTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("RatingType");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.Regatta", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.FileRecord", "EventLogo")
                        .WithMany()
                        .HasForeignKey("EventLogoId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.OrganizingAuthority", "OrganizingAuthority")
                        .WithMany()
                        .HasForeignKey("OrganizingAuthorityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("EventLogo");

                    b.Navigation("OrganizingAuthority");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaBoat", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.CountryCode", "CountryCode")
                        .WithMany()
                        .HasForeignKey("CountryCodeId");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.Boat", "MasterBoat")
                        .WithMany()
                        .HasForeignKey("MasterBoatId");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaClass", "RegattaClass")
                        .WithMany()
                        .HasForeignKey("RegattaClassId");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaFleet", "RegattaFleet")
                        .WithMany()
                        .HasForeignKey("RegattaFleetId");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaBoats")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", "Skipper2")
                        .WithMany()
                        .HasForeignKey("Skipper2Id");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", "Skipper")
                        .WithMany()
                        .HasForeignKey("SkipperId");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CountryCode");

                    b.Navigation("CreatedBy");

                    b.Navigation("MasterBoat");

                    b.Navigation("Regatta");

                    b.Navigation("RegattaClass");

                    b.Navigation("RegattaFleet");

                    b.Navigation("Skipper");

                    b.Navigation("Skipper2");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaBoatCompetitor", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaBoat", "RegattaBoat")
                        .WithMany("RegattaBoatCompetitors")
                        .HasForeignKey("RegattaBoatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", "RegattaCompetitor")
                        .WithMany("RegattaBoatCompetitors")
                        .HasForeignKey("RegattaCompetitorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RegattaBoat");

                    b.Navigation("RegattaCompetitor");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaClass", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaFleet", "RegattaFleet")
                        .WithMany()
                        .HasForeignKey("RegattaFleetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaClasses")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Regatta");

                    b.Navigation("RegattaFleet");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.Competitor", "Competitor")
                        .WithMany()
                        .HasForeignKey("CompetitorId");

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaCompetitors")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("Competitor");

                    b.Navigation("CreatedBy");

                    b.Navigation("Regatta");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaExternalLink", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.LinkType", "Type")
                        .WithMany()
                        .HasForeignKey("LinkTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("ExternalLinks")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Regatta");

                    b.Navigation("Type");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaFleet", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaFleets")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Regatta");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaRating", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RatingType", "RatingType")
                        .WithMany()
                        .HasForeignKey("RatingTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaBoat", "RegattaBoat")
                        .WithMany("RegattaRatings")
                        .HasForeignKey("RegattaBoatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaRatings")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.Rating", "SourceRating")
                        .WithMany("RegattaRatings")
                        .HasForeignKey("SourceRatingId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("RatingType");

                    b.Navigation("Regatta");

                    b.Navigation("RegattaBoat");

                    b.Navigation("SourceRating");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaRatingValue", b =>
                {
                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.Regatta", "Regatta")
                        .WithMany("RegattaRatingValues")
                        .HasForeignKey("RegattaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ProScoring.Domain.Entities.RegattaEntities.RegattaRating", "RegattaRating")
                        .WithMany("RegattaRatingValues")
                        .HasForeignKey("RegattaRatingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Regatta");

                    b.Navigation("RegattaRating");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.ActionHierarchy", b =>
                {
                    b.HasOne("ProScoring.Infrastructure.Authorization.Entities.AuthAction", "ChildAction")
                        .WithMany()
                        .HasForeignKey("ChildActionName")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ProScoring.Infrastructure.Authorization.Entities.AuthAction", "ParentAction")
                        .WithMany()
                        .HasForeignKey("ParentActionName")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChildAction");

                    b.Navigation("ParentAction");
                });

            modelBuilder.Entity("ProScoring.Infrastructure.Authorization.Entities.UserAuthAction", b =>
                {
                    b.HasOne("ProScoring.Infrastructure.Authorization.Entities.AuthAction", "AuthAction")
                        .WithMany()
                        .HasForeignKey("AuthActionName")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ProScoring.Domain.Entities.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuthAction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Boat", b =>
                {
                    b.Navigation("Ratings");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.Rating", b =>
                {
                    b.Navigation("RatingValues");

                    b.Navigation("RegattaRatings");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.Regatta", b =>
                {
                    b.Navigation("ExternalLinks");

                    b.Navigation("RegattaBoats");

                    b.Navigation("RegattaClasses");

                    b.Navigation("RegattaCompetitors");

                    b.Navigation("RegattaFleets");

                    b.Navigation("RegattaRatingValues");

                    b.Navigation("RegattaRatings");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaBoat", b =>
                {
                    b.Navigation("RegattaBoatCompetitors");

                    b.Navigation("RegattaRatings");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaCompetitor", b =>
                {
                    b.Navigation("RegattaBoatCompetitors");
                });

            modelBuilder.Entity("ProScoring.Domain.Entities.RegattaEntities.RegattaRating", b =>
                {
                    b.Navigation("RegattaRatingValues");
                });
#pragma warning restore 612, 618
        }
    }
}
