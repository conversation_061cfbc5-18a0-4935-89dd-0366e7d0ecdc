using System;
using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Page object model for the login page.
/// </summary>
public class LoginPage : BasePageWithMainLayout
{
    #region Selectors
    public static string EmailInputSelector => "[data-testid='email-input']";
    public static string ErrorMessageSelector => "div.alert.alert-danger";
    public static string LoginSubmitButtonSelector => "form [data-testid='submit-login-button']";

    // Alternative selector if the above doesn't work
    public static string LoginFormSubmitButtonSelector => "form button[type='submit']";
    public static string PasswordInputSelector => "[data-testid='password-input']";
    public static string RememberMeCheckboxSelector => "[data-testid='remember-me-checkbox']";
    public static string ValidationSummarySelector => "[data-testid='validation-summary']";
    #endregion Selectors

    #region Constructors
    public LoginPage(IPage page)
        : base(page, "Account/Login") { }
    #endregion Constructors

    #region Methods
    /// <summary>
    /// Gets any error message displayed after a login attempt.
    /// </summary>
    /// <returns>The error message text, or null if there is no error.</returns>
    public async Task<string?> GetErrorMessageAsync()
    {
        if (await IsVisibleAsync(ErrorMessageSelector))
        {
            return await GetTextContentAsync(ErrorMessageSelector);
        }
        return null;
    }

    /// <summary>
    /// Checks if the login button is visible on the page.
    /// </summary>
    /// <returns>True if the login button is visible, otherwise false.</returns>
    public async Task<bool> IsLoginSubmitButtonVisible()
    {
        return await IsVisibleAsync(LoginSubmitButtonSelector);
    }

    /// <summary>
    /// Performs the login operation.
    /// </summary>
    /// <param name="email">The email to login with.</param>
    /// <param name="password">The password to login with.</param>
    /// <returns>A task representing the login operation.</returns>
    public async Task LoginAsync(string email, string password)
    {
        await FillAsync(EmailInputSelector, email);
        await FillAsync(PasswordInputSelector, password);

        // Make sure the button is visible and enabled before clicking
        await _page.WaitForSelectorAsync(LoginSubmitButtonSelector, new() { State = WaitForSelectorState.Visible });

        // Try using direct Playwright click with force option
        var loginButton = await _page.QuerySelectorAsync(LoginSubmitButtonSelector);
        if (loginButton != null)
        {
            await loginButton.ClickAsync(
                new()
                {
                    Force = true,
                    Timeout = 10000, // Increase timeout to 10 seconds
                }
            );

            // Add a small delay to ensure the click action completes
            await Task.Delay(500);
        }
        else
        {
            throw new InvalidOperationException("Login button not found when attempting to click");
        }
    }

    /// <summary>
    /// Verifies that the login page has loaded correctly.
    /// </summary>
    /// <returns>A task representing the verification operation.</returns>
    public override async Task VerifyPageLoadedAsync()
    {
        await _page.WaitForSelectorAsync(EmailInputSelector);
        await _page.WaitForSelectorAsync(PasswordInputSelector);
        await _page.WaitForSelectorAsync(LoginSubmitButtonSelector);
    }
    #endregion Methods
}
