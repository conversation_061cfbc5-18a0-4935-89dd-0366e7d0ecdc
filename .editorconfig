root = true

[*]
charset = utf-8
end_of_line = crlf
indent_size = 2
indent_style = space
insert_final_newline = true

[*.{cs,cshtml,html,razor}]
indent_size = 4

[*.cs]
# Disable formatting rules because of CSharpier 
dotnet_diagnostic.IDE0055.severity = suggestion # does not seem to make any difference

# from CSharpier docs: https://csharpier.com/docs/Configuration
dotnet_sort_system_directives_first = true
dotnet_separate_import_directives_groups = true

# Use spaces for indentation
indent_style = space

# Set the indentation size to 4 spaces
indent_size = 4

# Ensure that method chaining is properly indented
csharp_indent_method_chain = true

# Continuation indent size
continuation_indent_size = 8

# Other common settings
end_of_line = crlf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

# Code style: Require 'this' for instance members
csharp_style_qualified_field_access = true:suggestion
csharp_style_qualified_method_access = true:suggestion
csharp_style_qualified_property_access = true:suggestion

# Var preferences
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = true:suggestion

# Naming conventions for C#

# Rule: Classes, Structs, and Enums should follow PascalCase

# IDE0290: Use primary constructor
csharp_style_prefer_primary_constructors = true:suggestion

dotnet_naming_rule.classes_structs_enums_should_be_pascal_case.severity = warning
dotnet_naming_rule.classes_structs_enums_should_be_pascal_case.symbols = classes_structs_enums
dotnet_naming_rule.classes_structs_enums_should_be_pascal_case.style = pascal_case

# Symbols definition for Classes, Structs, and Enums
dotnet_naming_symbols.classes_structs_enums.applicable_kinds = class, struct, enum

# PascalCase style definition
dotnet_naming_style.pascal_case.capitalization = pascal_case

# Rule: Variables and Private Fields should follow camelCase
dotnet_naming_rule.variables_and_private_fields_should_be_camel_case.severity = warning
dotnet_naming_rule.variables_and_private_fields_should_be_camel_case.symbols = variables_and_private_fields
dotnet_naming_rule.variables_and_private_fields_should_be_camel_case.style = camel_case

# Rule: Private Variables and Fields should follow _camelCase
dotnet_naming_rule.private_variables_and_fields_should_be_underscore_camel_case.severity = warning
dotnet_naming_rule.private_variables_and_fields_should_be_underscore_camel_case.symbols = private_variables_and_fields
dotnet_naming_rule.private_variables_and_fields_should_be_underscore_camel_case.style = underscore_camel_case

# Symbols definition for Private Variables and Fields
dotnet_naming_symbols.private_variables_and_fields.applicable_kinds = field, variable
dotnet_naming_symbols.private_variables_and_fields.applicable_accessibilities = private

# _camelCase style definition
dotnet_naming_style.underscore_camel_case.capitalization = camel_case
dotnet_naming_style.underscore_camel_case.required_prefix = _

# camelCase style definition
dotnet_naming_style.camel_case.capitalization = camel_case

# Rule: Methods should follow PascalCase
dotnet_naming_rule.methods_should_be_pascal_case.severity = warning
dotnet_naming_rule.methods_should_be_pascal_case.symbols = methods
dotnet_naming_rule.methods_should_be_pascal_case.style = pascal_case

# Symbols definition for Methods
dotnet_naming_symbols.methods.applicable_kinds = method

# Rule: Properties should follow PascalCase
dotnet_naming_rule.properties_should_be_pascal_case.severity = warning
dotnet_naming_rule.properties_should_be_pascal_case.symbols = properties
dotnet_naming_rule.properties_should_be_pascal_case.style = pascal_case

# Symbols definition for Properties
dotnet_naming_symbols.properties.applicable_kinds = property

# Rule: Constants should follow UPPER_CASE
dotnet_naming_rule.constants_should_be_upper_case.severity = warning
dotnet_naming_rule.constants_should_be_upper_case.symbols = constants
dotnet_naming_rule.constants_should_be_upper_case.style = upper_case

# Symbols definition for Constants
dotnet_naming_symbols.constants.applicable_kinds = field
dotnet_naming_symbols.constants.required_modifiers = const

# UPPER_CASE style definition
dotnet_naming_style.upper_case.capitalization = all_upper

# Rule: Local Variables should follow camelCase
dotnet_naming_rule.local_variables_should_be_camel_case.severity = warning
dotnet_naming_rule.local_variables_should_be_camel_case.symbols = local_variables
dotnet_naming_rule.local_variables_should_be_camel_case.style = camel_case

# Symbols definition for Local Variables
dotnet_naming_symbols.local_variables.applicable_kinds = variable
dotnet_naming_symbols.local_variables.applicable_accessibilities = local

# Rule: Interfaces should start with a capital I and then follow PascalCase
dotnet_naming_rule.interfaces_should_start_with_i_and_be_pascal_case.severity = warning
dotnet_naming_rule.interfaces_should_start_with_I_and_be_pascal_case.symbols = interfaces
dotnet_naming_rule.interfaces_should_start_with_i_and_be_pascal_case.style = interface_pascal_case

# Symbols definition for Interfaces
dotnet_naming_symbols.interfaces.applicable_kinds = interface

# Interface PascalCase style definition
dotnet_naming_style.interface_pascal_case.capitalization = pascal_case
dotnet_naming_style.interface_pascal_case.required_prefix = I
# Rule: Namespaces should follow directory structure and PascalCase

dotnet_naming_rule.namespaces_should_follow_directory_structure.severity = warning
dotnet_naming_rule.namespaces_should_follow_directory_structure.symbols = namespaces
dotnet_naming_rule.namespaces_should_follow_directory_structure.style = pascal_case

dotnet_naming_symbols.namespaces.applicable_kinds = namespace

dotnet_naming_style.directory_pascal_case.capitalization = pascal_case
csharp_indent_labels = one_less_than_current
csharp_using_directive_placement = outside_namespace:silent
csharp_prefer_simple_using_statement = true:suggestion
csharp_prefer_braces = true:silent
csharp_style_namespace_declarations = block_scoped:silent
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_prefer_system_threading_lock = true:suggestion
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:silent
csharp_style_expression_bodied_indexers = true:silent
csharp_style_expression_bodied_accessors = true:silent
csharp_style_expression_bodied_lambdas = true:silent
csharp_style_expression_bodied_local_functions = false:silent

# Naming rules for type parameters
dotnet_naming_rule.type_parameters_should_be_pascal_case.severity = none
dotnet_naming_style.type_parameters_should_be_pascal_case.capitalization = pascal_case
dotnet_naming_style.interface_pascal_case.required_prefix = T
# Allow type parameters to start with an uppercase letter
dotnet_diagnostic.IDE1006.severity = none # This was to prevent <T> from being flagged as an error, but I don't want to disable it because I don't know what else it will turn off.

# Disable suggestion to use primary constructor
[*.{cs,vb}]
dotnet_diagnostic.IDE0270.severity = none
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
dotnet_style_prefer_conditional_expression_over_assignment = true:silent
dotnet_style_prefer_conditional_expression_over_return = true:silent
dotnet_style_explicit_tuple_names = true:suggestion

[*.json]
end_of_line = lf
charset = utf-8
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true

[*.yml]

[*.cshtml]

[*.md]
max_line_length = off
