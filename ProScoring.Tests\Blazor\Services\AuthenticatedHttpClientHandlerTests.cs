using Xunit;
using FluentAssertions;
using NSubstitute;
using System.Net.Http;
using System.Net.Http.Headers; // For AuthenticationHeaderValue
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using ProScoring.Blazor.Services; // Assuming AuthenticatedHttpClientHandler is here

namespace ProScoring.Tests.Blazor.Services
{
    public class MockHttpHandler : HttpMessageHandler
    {
        public HttpRequestMessage? LastRequest { get; private set; }
        public HttpResponseMessage Response { get; set; } = new HttpResponseMessage(System.Net.HttpStatusCode.OK);
        public int SendAsyncCount { get; private set; } = 0;

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            LastRequest = request;
            SendAsyncCount++;
            // Create a new HttpResponseMessage for each call to avoid issues if the response is disposed by HttpClient
            var responseToSend = new HttpResponseMessage(Response.StatusCode);
            foreach (var header in Response.Headers)
            {
                responseToSend.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
            if (Response.Content != null)
            {
                // This is tricky. If content is read, it might not be readable again.
                // For most tests of DelegatingHandler, the content of the *response* isn't the focus.
                // If it were, more sophisticated cloning of content would be needed.
                // For now, assuming simple OK or specific status code is enough.
            }
            return Task.FromResult(responseToSend);
        }
    }

    public class AuthenticatedHttpClientHandlerTests
    {
        private readonly IHttpContextAccessor _mockHttpContextAccessor;
        private readonly MockHttpHandler _mockInnerHandler;
        private readonly AuthenticatedHttpClientHandler _handler;
        private readonly HttpClient _httpClient;

        public AuthenticatedHttpClientHandlerTests()
        {
            _mockHttpContextAccessor = Substitute.For<IHttpContextAccessor>();
            _mockInnerHandler = new MockHttpHandler();
            _handler = new AuthenticatedHttpClientHandler(_mockHttpContextAccessor) { InnerHandler = _mockInnerHandler };
            _httpClient = new HttpClient(_handler);
        }

        [Fact]
        public async Task SendAsync_NullHttpContext_DoesNotAddHeaders()
        {
            // Arrange
            var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
            _mockHttpContextAccessor.HttpContext.Returns((HttpContext?)null);

            // Act
            await _httpClient.SendAsync(request);

            // Assert
            _mockInnerHandler.LastRequest.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.Contains("Cookie").Should().BeFalse();
            _mockInnerHandler.LastRequest.Headers.Authorization.Should().BeNull();
            _mockInnerHandler.SendAsyncCount.Should().Be(1);
        }

        [Fact]
        public async Task SendAsync_HttpContextExists_NoRelevantHeaders_DoesNotAddHeaders()
        {
            // Arrange
            var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
            var httpContext = new DefaultHttpContext();
            _mockHttpContextAccessor.HttpContext.Returns(httpContext);

            // Act
            await _httpClient.SendAsync(request);

            // Assert
            _mockInnerHandler.LastRequest.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.Contains("Cookie").Should().BeFalse();
            _mockInnerHandler.LastRequest.Headers.Authorization.Should().BeNull();
            _mockInnerHandler.SendAsyncCount.Should().Be(1);
        }

        [Fact]
        public async Task SendAsync_WithCookieHeader_AddsCookieHeaderToRequest()
        {
            // Arrange
            var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Cookie"] = "TestCookie=Value";
            _mockHttpContextAccessor.HttpContext.Returns(httpContext);

            // Act
            await _httpClient.SendAsync(request);

            // Assert
            _mockInnerHandler.LastRequest.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.GetValues("Cookie").Should().ContainSingle().Which.Should().Be("TestCookie=Value");
            _mockInnerHandler.LastRequest.Headers.Authorization.Should().BeNull();
            _mockInnerHandler.SendAsyncCount.Should().Be(1);
        }

        [Fact]
        public async Task SendAsync_WithAuthorizationHeader_AddsAuthHeaderToRequest()
        {
            // Arrange
            var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Authorization"] = "Bearer TestToken";
            _mockHttpContextAccessor.HttpContext.Returns(httpContext);

            // Act
            await _httpClient.SendAsync(request);

            // Assert
            _mockInnerHandler.LastRequest.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.Contains("Cookie").Should().BeFalse();
            _mockInnerHandler.LastRequest.Headers.Authorization.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.Authorization?.Scheme.Should().Be("Bearer");
            _mockInnerHandler.LastRequest.Headers.Authorization?.Parameter.Should().Be("TestToken");
            _mockInnerHandler.SendAsyncCount.Should().Be(1);
        }

        [Fact]
        public async Task SendAsync_WithBothHeaders_AddsBothHeadersToRequest()
        {
            // Arrange
            var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
            var httpContext = new DefaultHttpContext();
            httpContext.Request.Headers["Cookie"] = "TestCookie=Value";
            httpContext.Request.Headers["Authorization"] = "Bearer TestToken";
            _mockHttpContextAccessor.HttpContext.Returns(httpContext);

            // Act
            await _httpClient.SendAsync(request);

            // Assert
            _mockInnerHandler.LastRequest.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.GetValues("Cookie").Should().ContainSingle().Which.Should().Be("TestCookie=Value");
            _mockInnerHandler.LastRequest.Headers.Authorization.Should().NotBeNull();
            _mockInnerHandler.LastRequest.Headers.Authorization?.Scheme.Should().Be("Bearer");
            _mockInnerHandler.LastRequest.Headers.Authorization?.Parameter.Should().Be("TestToken");
            _mockInnerHandler.SendAsyncCount.Should().Be(1);
        }
    }
}
