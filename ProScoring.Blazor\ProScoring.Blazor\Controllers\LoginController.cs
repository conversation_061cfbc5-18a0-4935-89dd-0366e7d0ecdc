using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using ProScoring.Domain.Entities;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for handling AJAX login requests.
/// </summary>
[ApiController]
[Route("api/login")]
public class LoginController : ControllerBase
{
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly ILogger<LoginController> _logger;
    private readonly IAntiforgery _antiforgery;

    /// <summary>
    /// Initializes a new instance of the <see cref="LoginController"/> class.
    /// </summary>
    /// <param name="signInManager">The sign-in manager.</param>
    /// <param name="logger">The logger.</param>
    /// <param name="antiforgery">The antiforgery service.</param>
    public LoginController(
        SignInManager<ApplicationUser> signIn<PERSON>anager,
        ILogger<LoginController> logger,
        IAntiforgery antiforgery
    )
    {
        _signInManager = signInManager;
        _logger = logger;
        _antiforgery = antiforgery;
    }

    /// <summary>
    /// Logs in a user with the provided credentials.
    /// </summary>
    /// <param name="model">The login model containing the credentials.</param>
    /// <returns>A result indicating whether the login was successful.</returns>
    [HttpPost]
    public async Task<IActionResult> Login([FromBody] LoginModel model)
    {
        if (!ModelState.IsValid)
        {
            _logger.LogWarning("Invalid model state in login request");
            return BadRequest(ModelState);
        }

        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await _signInManager.PasswordSignInAsync(
            model.Username,
            model.Password,
            model.RememberMe,
            lockoutOnFailure: false
        );

        if (result.Succeeded)
        {
            _logger.LogInformation("User {Username} logged in successfully", model.Username);
            return Ok(new { success = true });
        }
        else if (result.RequiresTwoFactor)
        {
            _logger.LogInformation("User {Username} requires two-factor authentication", model.Username);
            return Ok(new { success = false, requiresTwoFactor = true });
        }
        else if (result.IsLockedOut)
        {
            _logger.LogWarning("User account {Username} is locked out", model.Username);
            return Ok(
                new
                {
                    success = false,
                    isLockedOut = true,
                    message = "Account is locked out.",
                }
            );
        }
        else
        {
            _logger.LogWarning("Invalid login attempt for user {Username}", model.Username);
            return Ok(new { success = false, message = "Invalid login attempt." });
        }
    }
}

/// <summary>
/// Model for login requests.
/// </summary>
public class LoginModel
{
    /// <summary>
    /// Gets or sets the username.
    /// </summary>
    [JsonPropertyName("username")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password.
    /// </summary>
    [JsonPropertyName("password")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets a value indicating whether to remember the login.
    /// </summary>
    [JsonPropertyName("rememberMe")]
    public bool RememberMe { get; set; }
}
