using System.Text;
using System.Text.Json;
using Blazored.LocalStorage;
using Microsoft.Extensions.Options;
using ProScoring.Blazor.Options;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Blazor.Services;

public class LocalSettingWithExpiration<TSettingValue>
{
    public TSettingValue Value { get; set; } = default!;
    public DateTimeOffset Expiration { get; set; }
    public TimeSpan? LifeTime{ get; set; }
}
/// <summary>
/// Service for handling local storage operations with expiration support.
/// </summary>
public class LocalStorageServiceWithExpiration : ILocalStorageServiceWithExpiration
{
    private readonly ILocalStorageService _localStorage;
    private readonly LocalStorageOptions _options;
    private readonly IDateTimeOffsetProvider _dateTimeProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="LocalStorageServiceWithExpiration"/> class.
    /// </summary>
    /// <param name="localStorage">The Blazored local storage service.</param>
    /// <param name="options">The local storage options.</param>
    /// <param name="dateTimeProvider">The date time provider.</param>
    public LocalStorageServiceWithExpiration(
        ILocalStorageService localStorage,
        IOptions<LocalStorageOptions> options,
        IDateTimeOffsetProvider dateTimeProvider
    )
    {
        _localStorage = localStorage;
        _options = options.Value;
        _dateTimeProvider = dateTimeProvider;
    }

    /// <summary>
    /// Gets an item from local storage with expiration check.
    /// </summary>
    /// <typeparam name="T">The type of the item.</typeparam>
    /// <param name="key">The key of the item.</param>
    /// <returns>The item if found and not expired, otherwise default.</returns>
    public async ValueTask<T?> GetItemAsync<T>(string key, bool refreshExpiration = true)
    {
        try
        {
            var json = await _localStorage.GetItemAsStringAsync(key);
            if (string.IsNullOrEmpty(json))
            {
                return default;
            }

            var savedValue = JsonSerializer.Deserialize<LocalSettingWithExpiration<T>>(json);
            if (savedValue == null)
            {
                return default;
            }

            var expiration = savedValue.Expiration;

            // Check if the item has expired
            if (expiration < _dateTimeProvider.UtcNow)
            {
                // Item has expired, remove it
                await _localStorage.RemoveItemAsync(key);
                return default;
            }

            // if valid, resave to update the expiration
            if (refreshExpiration)
                await SetItemAsync(key, savedValue.Value, savedValue.LifeTime);

            // Item is valid, return it
            return savedValue.Value;
        }
        catch(Exception ex)
        {
            Console.WriteLine(ex);
            // If any error occurs, return default
            return default;
        }
    }

    /// <summary>
    /// Sets an item in local storage with expiration.
    /// </summary>
    /// <typeparam name="T">The type of the item.</typeparam>
    /// <param name="key">The key of the item.</param>
    /// <param name="value">The value to store.</param>
    /// <param name="lifetime">Optional lifetime for the item; if null, uses default from options.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async ValueTask SetItemAsync<T>(string key, T value, TimeSpan? lifetime = null)
    {
        try
        {
            // Calculate expiration time
            var expiration = _dateTimeProvider.UtcNow.Add(lifetime ?? TimeSpan.FromMinutes(_options.ExpirationTimeoutMinutes));

            // Store the item and its expiration
            await _localStorage.SetItemAsStringAsync(key, JsonSerializer.Serialize(new LocalSettingWithExpiration<T>
            {
                Value = value,
                Expiration = expiration,
                LifeTime = lifetime
            }));
        }
        catch
        {
            // Ignore errors writing to local storage
        }
    }

    /// <summary>
    /// Removes an item from local storage.
    /// </summary>
    /// <param name="key">The key of the item.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async ValueTask RemoveItemAsync(string key)
    {
        try
        {
            await _localStorage.RemoveItemAsync(key);
        }
        catch
        {
            // Ignore errors removing from local storage
        }
    }
}
