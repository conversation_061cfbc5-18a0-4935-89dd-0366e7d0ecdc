﻿@page "/error"
@page "/error/{StatusCode:int}"
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Diagnostics
@attribute [AllowAnonymous]
@inject NavigationManager NavigationManager

<PageTitle>Error</PageTitle>

<div class="container error-container">
    <div class="text-center mt-5">
        <h1 class="display-1">@StatusCode</h1>
        <h2 class="display-4">@ErrorMessage</h2>
        <p class="lead">@DetailedErrorMessage</p>

        <button class="btn btn-primary mt-4" @onclick="NavigateToHome">
            Return to Home
        </button>
    </div>
</div>

@code {
    [Parameter]
    public int StatusCode { get; set; } = 500;

    [Parameter]
    public string? ErrorMessage { get; set; }

    private string DetailedErrorMessage => StatusCode switch
    {
        404 => "The page you're looking for doesn't exist.",
        500 => "An error occurred while processing your request.",
        _ => "An unexpected error has occurred."
    };

    protected override void OnInitialized()
    {
        // For cases where StatusCode is provided via route parameter
        if (string.IsNullOrEmpty(ErrorMessage))
        {
            ErrorMessage = StatusCode switch
            {
                404 => "Page Not Found",
                500 => "Server Error",
                _ => "Error"
            };
        }
    }

    private void NavigateToHome()
    {
        NavigationManager.NavigateTo("/");
    }
}
