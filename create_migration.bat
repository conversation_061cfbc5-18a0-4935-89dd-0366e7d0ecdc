@ set CREATING_MIGRATION=True
@ set PREFIX_SQLITE=SQLite_
@ set PREFIX_POSTGRESQL=PostgreSql_
@ set ARG_SQLITE=%PREFIX_SQLITE%%1
@ set ARG_POSTGRESQL=%PREFIX_POSTGRESQL%%1
dotnet build
dotnet test ProScoring.Tests --no-build --filter "FullyQualifiedName~ProScoring.Tests.Domain.DbMigrationCreationTests"
@if %ERRORLEVEL% NEQ 0 (
@  echo Tests failed. Exiting...
@  exit /b %ERRORLEVEL%
)
@echo CREATING_MIGRATION = %CREATING_MIGRATION%
dotnet ef migrations add %ARG_SQLITE% --no-build --context SqliteCreationApplicationDbContext --startup-project ProScoring.Blazor/ProScoring.Blazor --project ProScoring.Infrastructure\ --output-dir ./Database/SQLite_Migrations
dotnet ef migrations add %ARG_POSTGRESQL% --no-build --context PostgreSqlCreationApplicationDbContext --startup-project ProScoring.Blazor/ProScoring.Blazor --project ProScoring.Infrastructure\ --output-dir ./Database/PostgreSql_Migrations
@ set CREATING_MIGRATION=False
@echo CREATING_MIGRATION = %CREATING_MIGRATION%
