using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for handling file operations like uploading, downloading, and retrieving file records.
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FileController(IFileService fileService, IConfiguration configuration, ILogger<FileController> logger)
    : ControllerBase,
        IFileController
{
    #region fields
    private readonly IConfiguration _configuration = configuration;
    private readonly IFileService _fileService = fileService;
    private readonly ILogger<FileController> _logger = logger;
    #endregion

    #region methods
    /// <summary>
    /// Downloads a file by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a downloadable response or an error message.</returns>
    [HttpGet("download/{id}")]
    [AllowAnonymous]
    public async Task<IActionResult> Download(string id)
    {
        try
        {
            var result = await _fileService.DownloadAsync(id);
            if (result == null)
            {
                _logger.LogError("Error downloading file {Id}, result was null.", id);
                var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");
                var errorMessage =
                    environment == "Development"
                        ? $"Internal server error: DownloadAsync({id}) returned null."
                        : "Internal server error.";
                return StatusCode(500, errorMessage);
            }
            return File(result.Stream, result.ContentType ?? "", result.FileName);
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Downloads a file as a data URI by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a data URI or an error message.</returns>
    [HttpGet("downloadFileData/{id}")]
    [AllowAnonymous]
    public async Task<IActionResult> DownloadFileData(string id)
    {
        try
        {
            var result = await _fileService.DownloadAsDataUriAsync(id);
            if (result == null)
            {
                _logger.LogError("Error downloading file {Id}, result was null.", id);
                var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");
                var errorMessage =
                    environment == "Development"
                        ? $"Internal server error: DownloadAsync({id}) returned null."
                        : "Internal server error.";
                return StatusCode(500, errorMessage);
            }
            return Ok(result);
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Gets a file record by its ID.
    /// </summary>
    /// <param name="id">The ID of the file record to retrieve.</param>
    /// <returns>The file record or an error message.</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<FileRecord>> Get(string id)
    {
        try
        {
            return Ok(await _fileService.GetFileRecordAsync(id));
        }
        catch (FileNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// A test endpoint that returns "Goodbye World".
    /// </summary>
    /// <returns>A string message.</returns>
    [HttpPost("goodbyeworld")]
    [AllowAnonymous]
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<ActionResult<string>> GoodbyeWorld()
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
        return Ok("Goodbye World");
    }

    /// <summary>
    /// A test endpoint that returns "Hello World".
    /// </summary>
    /// <returns>A string message.</returns>
    [HttpGet("helloworld")]
    [AllowAnonymous]
#pragma warning disable CS1998 // Async method lacks 'await' operators and will run synchronously
    public async Task<ActionResult<string>> HelloWorld()
#pragma warning restore CS1998 // Async method lacks 'await' operators and will run synchronously
    {
        return Ok("Hello World");
    }

    /// <summary>
    /// Uploads a file.
    /// </summary>
    /// <param name="file">The file to upload.</param>
    /// <param name="note">A note about the file.</param>
    /// <returns>The result of the upload operation.</returns>
    [HttpPost("upload")]
    public virtual async Task<ActionResult<FileUploadResult>> Upload(IFormFile file, [FromForm] string note)
    {
        try
        {
            var uploadResult = await _fileService.UploadAsync(file, note);

            if (uploadResult.ErrorCode != (int)FileUploadResult.ErrorCodes.None)
            {
                return BadRequest(uploadResult);
            }

            return Ok(uploadResult);
        }
        catch (Exception ex)
        {
            return HandleFileUploadError(file.Name, ex);
        }
    }

    /// <summary>
    /// Uploads a file from a data URI.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <param name="note">A note about the file.</param>
    /// <param name="fileData">The file data as a data URI.</param>
    /// <returns>The result of the upload operation.</returns>
    [HttpPost("uploadFileData")]
    public virtual async Task<ActionResult<FileUploadResult>> UploadFileData(
        string fileName,
        string note,
        string fileData
    )
    {
        try
        {
            var uploadResult = await _fileService.UploadFromDataUriAsync(fileName, note, fileData);

            if (uploadResult.ErrorCode != (int)FileUploadResult.ErrorCodes.None)
            {
                return BadRequest(uploadResult);
            }

            return Ok(uploadResult);
        }
        catch (Exception ex)
        {
            return HandleFileUploadError(fileName, ex);
        }
    }

    /// <summary>
    /// Handles errors that occur during file upload operations.
    /// </summary>
    /// <param name="fileName">The name of the file being uploaded.</param>
    /// <param name="ex">The exception that occurred.</param>
    /// <returns>An appropriate error response.</returns>
    private ActionResult<FileUploadResult> HandleFileUploadError(string fileName, Exception ex)
    {
        _logger.LogError(ex, "Error uploading file {FileName}", fileName);
        var environment = _configuration.GetValue<string>("ASPNETCORE_ENVIRONMENT");
        var errorMessage =
            environment == "Development"
                ? $"Internal server error: {ex.Message}, \r\nStackTrace: {ex.StackTrace}"
                : "Internal server error.";
        return StatusCode(500, errorMessage);
    }
    #endregion
}
