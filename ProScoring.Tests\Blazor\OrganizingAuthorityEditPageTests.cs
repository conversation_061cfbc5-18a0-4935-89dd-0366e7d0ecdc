using System.Security.Claims;
using AngleSharp.Dom;
using Bunit;
using Bunit.TestDoubles;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages;
using ProScoring.Blazor.Extensions;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Tests.Helpers;
using Radzen;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the OrganizingAuthority Edit page.
/// </summary>
public class OrganizingAuthorityEditPageTests : TestContext
{
    private readonly IOrganizingAuthorityService _mockOrganizingAuthorityService;
    private readonly NavigationManager _mockNavigationManager;

    private readonly TestAuthenticationStateProvider _authStateProvider;

    public OrganizingAuthorityEditPageTests()
    {
        _mockOrganizingAuthorityService = Substitute.For<IOrganizingAuthorityService>();
        _mockNavigationManager = Substitute.For<NavigationManager>();
        _authStateProvider = new TestAuthenticationStateProvider();

        Services.AddSingleton(_mockOrganizingAuthorityService);
        Services.AddSingleton(_mockNavigationManager);
        Services.AddScoped<TooltipService>();
        Services.AddScoped<DialogService>();
        Services.AddScoped<NotificationService>();
        Services.AddSingleton<AuthenticationStateProvider>(_authStateProvider);

        // Set up JSInterop for Radzen components
        JSInterop.Mode = JSRuntimeMode.Loose;
        JSInterop.SetupVoid("Radzen.uploads", _ => true);
        JSInterop.SetupVoid("Radzen.toggleClass", _ => true);
        JSInterop.SetupVoid("Radzen.closePopup", _ => true);
        JSInterop.SetupVoid("Radzen.openPopup", _ => true);
        JSInterop.SetupVoid("Radzen.destroyPopup", _ => true);

        // Add authorization services
        Services.AddAuthorization(options =>
        {
            options.AddPolicy(
                EditAuthorizationForPageWithIdHandler.PolicyName,
                policy => policy.RequireAssertion(_ => true)
            );
        });
    }

    [Fact(Skip = "NavigationManager initialization issue with SupplyParameterFromQuery")]
    public void ApprovalToggle_NotVisible_ForRegularUser()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };

        var dto = OrganizingAuthorityUploadDto.FromEntity(organizingAuthority);

        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Set up regular user (not HMFIC)
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Navigate to the page with query parameter
        var uri = _mockNavigationManager.GetUriWithQueryParameter("id", "O1");
        _mockNavigationManager.NavigateTo(uri);

        // Act
        var cut = RenderComponent<Edit>();

        // Assert
        cut.WaitForState(() => cut.FindAll("RadzenSwitch").Count > 0);
        cut.FindAll("RadzenSwitch[class*='oa-approved-checkbox']")
            .Count.Should()
            .Be(0, "Approval toggle should not be visible for regular users");
        cut.FindAll("RadzenSwitch[class*='oa-private-checkbox']")
            .Count.Should()
            .Be(1, "Private toggle should be visible for all users");
    }

    [Fact(Skip = "NavigationManager initialization issue with SupplyParameterFromQuery")]
    public void ApprovalToggle_Visible_ForHmficUser()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };

        var dto = OrganizingAuthorityUploadDto.FromEntity(organizingAuthority);

        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Navigate to the page with query parameter
        var uri = _mockNavigationManager.GetUriWithQueryParameter("id", "O1");
        _mockNavigationManager.NavigateTo(uri);

        // Act
        var cut = RenderComponent<Edit>();

        // Assert
        cut.WaitForState(() => cut.FindAll("RadzenSwitch").Count > 0);
        cut.FindAll("RadzenSwitch[class*='oa-approved-checkbox']")
            .Count.Should()
            .Be(1, "Approval toggle should be visible for HMFIC users");
        cut.FindAll("RadzenSwitch[class*='oa-private-checkbox']")
            .Count.Should()
            .Be(1, "Private toggle should be visible for all users");
    }

    [Fact(Skip = "NavigationManager initialization issue with SupplyParameterFromQuery")]
    public async Task HandleValidSubmit_SavesApprovalStatus()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };

        var dto = OrganizingAuthorityUploadDto.FromEntity(organizingAuthority);

        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Navigate to the page with query parameter
        var uri = _mockNavigationManager.GetUriWithQueryParameter("id", "O1");
        _mockNavigationManager.NavigateTo(uri);

        // Act
        var cut = RenderComponent<Edit>();

        // Find and toggle the approval checkbox
        var approvalCheckbox = cut.Find("RadzenSwitch[class*='oa-approved-checkbox']");
        approvalCheckbox.Change(true);

        // Submit the form
        var form = cut.Find("form");
        form.Submit();

        // Assert
        await _mockOrganizingAuthorityService
            .Received(1)
            .UpdateAsync(Arg.Is<OrganizingAuthorityUploadDto>(dto => dto.Id == "O1" && dto.Approved == true));
    }
}
