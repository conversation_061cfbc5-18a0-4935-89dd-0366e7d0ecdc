namespace ProScoring.Infrastructure.Authorization;

public static class AuthTypes
{
    public const string GOD = HMFIC;
    public const string HMFIC = "HMFIC";
    public const string UNIVERSAL_TARGET = "*";

    // use the following, combined with an entity type to claim ability on that entity type for the
    // IDs in the claim value.
    // i.e. "View_OA" with claim value "1,2,3" would allow viewing of OrganizingAuthority with IDs 1, 2, and 3.
    public static class Actions
    {
        public const string ADMIN = "Admin";

        // can do anything, Incuding assign claims.

        public const string DELETE = "Del";
        public const string EDIT = "Edit";
        public const string VIEW = "View";
    }

    // but I might not need Entities, because the Id number includes this information.
    public static class Entities
    {
        #region constants
        public const string BOAT = "B";
        public const string FILE = "F";
        public const string ORGANIZING_AUTHORITY = "OA";
        public const string RACE = "R";
        public const string REGATTA = "G";
        public const string USER = "U";
        #endregion
    }
}
