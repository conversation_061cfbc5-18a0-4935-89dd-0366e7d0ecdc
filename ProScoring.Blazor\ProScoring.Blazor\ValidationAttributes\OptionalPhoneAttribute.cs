using System.ComponentModel.DataAnnotations;

namespace ProScoring.Blazor.ValidationAttributes;

/// <summary>
/// Validation attribute for phone numbers that allows null or empty values.
/// If a value is provided, it must be a valid phone number.
/// </summary>
[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
public sealed class OptionalPhoneAttribute : ValidationAttribute
{
    #region Fields
    private readonly PhoneAttribute _phoneAttribute = new();
    #endregion

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="OptionalPhoneAttribute"/> class.
    /// </summary>
    public OptionalPhoneAttribute()
        : base("The {0} field is not a valid phone number.") { }
    #endregion

    #region Methods
    /// <summary>
    /// Determines whether the specified value is a valid phone number or is null/empty.
    /// </summary>
    /// <param name="value">The value to validate.</param>
    /// <param name="validationContext">The validation context.</param>
    /// <returns>A validation result if validation fails, otherwise <c>null</c>.</returns>
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
        {
            // Null or empty values are valid for optional phone numbers
            return ValidationResult.Success;
        }

        // Delegate to standard PhoneAttribute for validation
        if (_phoneAttribute.IsValid(value))
        {
            return ValidationResult.Success;
        }

        return new ValidationResult(FormatErrorMessage(validationContext.DisplayName));
    }
    #endregion
}
