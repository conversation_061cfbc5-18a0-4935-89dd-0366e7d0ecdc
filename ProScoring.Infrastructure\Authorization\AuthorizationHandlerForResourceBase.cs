using Microsoft.AspNetCore.Authorization;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Base authorization handler for entity operations.
/// </summary>
/// <typeparam name="TRequirement">The type of requirement being handled.</typeparam>
/// <typeparam name="TResource">The type of resource being authorized, must implement IHasId.</typeparam>
public abstract class AuthorizationHandlerForResourceBase<TRequirement>(
    IAuthorizationProvider authorizationProvider,
    string authType
) : AuthorizationHandler<TRequirement, IHasId>
    where TRequirement : IAuthorizationRequirement
{
    private readonly string _authType = authType;

    /// <summary>
    /// Handles the authorization requirement for operations on entities.
    /// </summary>
    /// <param name="context">The authorization handler context.</param>
    /// <param name="requirement">The requirement being handled.</param>
    /// <param name="resource">The resource being authorized for operations.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        TRequirement requirement,
        IHasId resource
    )
    {
        if (context.User.Identity?.IsAuthenticated != true)
        {
            return;
        }

        // Check if entity is null
        if (resource == null || resource.Id == null)
        {
            context.Fail(new AuthorizationFailureReason(this, "Resource is null or has no ID."));
            return;
        }

        var isAuthorized = await authorizationProvider.IsAuthorizedAsync(context.User, resource.Id, _authType);

        if (isAuthorized)
        {
            context.Succeed(requirement);
        }
    }
}
