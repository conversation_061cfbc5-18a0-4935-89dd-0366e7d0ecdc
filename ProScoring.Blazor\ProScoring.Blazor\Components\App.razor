<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet" href="@Assets["lib/bootstrap/dist/css/bootstrap.min.css"]" />
    <link rel="stylesheet" href="@Assets["css/app.css"]" />
    <link rel="stylesheet" href="@Assets["css/markdown.css"]" />
    <link rel="stylesheet" href="@Assets["css/organizing-authorities.css"]" />
    <link rel="stylesheet" href="@Assets["ProScoring.Blazor.styles.css"]" />
    <ImportMap />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet @rendermode="RenderModeForPage" />
    <RadzenTheme Theme="material" @rendermode="RenderModeForPage" />
</head>

<body>
    <CascadingAuthenticationState>
        <ProScoring.Blazor.Components.Routes @rendermode="RenderModeForPage" />
    </CascadingAuthenticationState>
    <script src="_framework/blazor.web.js"></script>
    <script
        src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script src="js/layout.js"></script>
</body>

</html>

@code {
    #region properties
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    private IComponentRenderMode? RenderModeForPage
    {
        get
        {
            // OLD: return InteractiveServer;
            var result = HttpContext.Request.Path.StartsWithSegments("/Account") ? null : InteractiveServer;
            return result;
        }
    }
    #endregion properties
}
