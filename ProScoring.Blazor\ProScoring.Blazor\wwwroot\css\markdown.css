/* Markdown content styling */
.markdown-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 100%;
    overflow-x: auto;
}

.markdown-content h1 {
    font-size: 2em;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
}

.markdown-content h2 {
    font-size: 1.5em;
    margin-top: 1em;
    margin-bottom: 0.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
}

.markdown-content h3 {
    font-size: 1.25em;
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.markdown-content h4 {
    font-size: 1em;
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.markdown-content p {
    margin-top: 0;
    margin-bottom: 1em;
}

.markdown-content a {
    color: #0366d6;
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

.markdown-content code {
    font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
}

.markdown-content pre {
    font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;
    margin-top: 0;
    margin-bottom: 16px;
}

.markdown-content pre code {
    padding: 0;
    margin: 0;
    font-size: 100%;
    background-color: transparent;
    border-radius: 0;
}

.markdown-content blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin: 0 0 16px 0;
}

.markdown-content ul, .markdown-content ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;
}

.markdown-content table {
    display: block;
    width: 100%;
    overflow: auto;
    margin-top: 0;
    margin-bottom: 16px;
    border-spacing: 0;
    border-collapse: collapse;
}

.markdown-content table th {
    font-weight: 600;
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
}

.markdown-content table td {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
}

.markdown-content table tr {
    background-color: #fff;
    border-top: 1px solid #c6cbd1;
}

.markdown-content table tr:nth-child(2n) {
    background-color: #f6f8fa;
}

.markdown-content img {
    max-width: 100%;
    box-sizing: content-box;
    background-color: #fff;
}

.markdown-content hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: #e1e4e8;
    border: 0;
}
