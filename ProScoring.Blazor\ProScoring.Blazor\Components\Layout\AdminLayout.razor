@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity
@using Microsoft.JSInterop
@using ProScoring.Blazor.Components.Layout
@using ProScoring.Domain.Entities
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services

@inherits LayoutComponentBase
@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NotificationService NotificationService
@inject IJSRuntime JSRuntime
@inject MainLayoutContextService LayoutContext
@inject DialogService DialogService

<RadzenDialog />
<RadzenNotification />
<RadzenTooltip />

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <div class="ms-auto nav-item-container">
                <AuthorizeView>
                    <Authorized>
                        @* <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End">
                            <RadzenSplitButton AlwaysOpenPopup=true Text=@_displayName Icon="settings"
                                IconPosition="IconPosition.Left" ButtonStyle="ButtonStyle.Secondary"
                                Click="@(args => OnUserOptionsButtonClick(args))"
                                @attributes=@("user-options-split-button".AsTestId())>
                                <RadzenSplitButtonItem Text="My Account" Value="MyAccount" Icon="account_circle"
                                    data-testid="my-account-menu-item" />
                                <RadzenSplitButtonItem Text="Logout" Value="Logout" Icon="logout"
                                    Attributes=@("logout-menu-item".AsTestId()) />
                            </RadzenSplitButton>
                        </RadzenStack> *@
                    </Authorized>
                    <NotAuthorized>
                        @* @if (LayoutContext.LoginButtonVisible)
                        {
                            <RadzenButton Text="Login" Click="@(args => OnButtonClick("Login"))"
                                ButtonStyle="ButtonStyle.Secondary" Icon="login" data-testid="login-button" />
                        } *@
                    </NotAuthorized>
                </AuthorizeView>
            </div>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<!-- Hidden logout form -->
<form id="logoutForm" method="post" action="/Account/Logout">
    <AntiforgeryToken />
    <input type="hidden" name="returnUrl" value="" />
</form>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

<script>
    // Function to submit the logout form
    function submitLogoutForm() {
        document.getElementById("logoutForm").submit();
    }
</script>

@code {
    #region Fields
    private string _displayName = string.Empty;
    #endregion Fields

    #region Methods
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        LayoutContext.OnLayoutChanged += StateHasChanged;

        if (user.Identity?.IsAuthenticated == true && user.Identity.Name != null)
        {
            var applicationUser = await UserManager.FindByNameAsync(user.Identity.Name);
            if (applicationUser != null)
            {
                _displayName = GetDisplayName(applicationUser);
            }
            else
            {
                _displayName = user.Identity.Name;
            }
        }
    }

    /// <summary>
    /// Handles the click event for the login button.
    /// </summary>
    private void OnButtonClick(string buttonName)
    {
        NavigationManager.NavigateTo("/Account/Login", forceLoad: true);
    }

    /// <summary>
    /// Handles the click event for user options in the split button.
    /// </summary>
    /// <param name="item">The selected menu item.</param>
    private async void OnUserOptionsButtonClick(RadzenSplitButtonItem item)
    {
        switch (item.Value)
        {
            case "Logout":
                // Submit the logout form with antiforgery token - using async call that works
                // in both Server and WebAssembly
                await JSRuntime.InvokeVoidAsync("submitLogoutForm");
                break;
            case "MyAccount":
                // Navigate to My Account page
                NavigationManager.NavigateTo("/Account/Manage", forceLoad: true);
                break;
            default:
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Warning,
                    Summary = "Unknown option selected",
                    Detail = $"Option '{item.Value}' is not recognized."
                });
                break;
        }
    }

    /// <summary>
    /// Gets the display name for a user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <returns>The user's display name.</returns>
    private string GetDisplayName(ApplicationUser user)
    {
        if (!string.IsNullOrEmpty(user.GivenName))
            return user.GivenName;

        if (!string.IsNullOrEmpty(user.Surname))
            return user.Surname;

        return user.UserName ?? string.Empty;
    }
    public void Dispose()
    {
        LayoutContext.OnLayoutChanged -= StateHasChanged;
    }
    #endregion Methods
}
