using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the login functionality of the ProScoring application.
/// </summary>
public class LoginTests : IClassFixture<PlaywrightFixture>
{
    #region Fields
    private readonly PlaywrightFixture _fixture;
    #endregion Fields

    #region Constructors
    public LoginTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion Constructors

    #region Test Methods
    [Fact]
    public async Task LoginPage_DoesNotHaveLoginButton()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);

            // Act
            await loginPage.NavigateAsync();

            // Assert - we should be on the login page with no login button visible
            var isLoginButtonVisible = await loginPage.IsLoginButtonVisible();
            isLoginButtonVisible.Should().BeFalse();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that a user can log in successfully with valid credentials.
    /// </summary>
    [Fact]
    public async Task Can_Login_With_Valid_Credentials()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);

            // Act
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);

            // Allow time for login process and redirect
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Assert - we should be on the home page with user options button visible
            var homePage = new HomePage(page);
            var isUserOptionsVisible = await homePage.IsUserOptionsButtonVisible();
            isUserOptionsVisible.Should().BeTrue();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that login fails with invalid credentials.
    /// </summary>
    [Fact]
    public async Task Login_Fails_With_Invalid_Credentials()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);

            // Act
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync("<EMAIL>", "wrongpassword");

            // Assert - we should still be on login page with error message
            var errorMessage = await loginPage.GetErrorMessageAsync();
            errorMessage.Should().NotBeNull();
            page.Url.ToLower().Should().Contain("account/login");
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Test Methods
}
