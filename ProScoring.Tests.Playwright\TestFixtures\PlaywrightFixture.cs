using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.UserSecrets;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.Playwright;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Tests.Playwright.Configuration;
using ProScoring.Tests.Playwright.PageObjects;

namespace ProScoring.Tests.Playwright.TestFixtures;

/// <summary>
/// Fixture for managing Playwright browser instances for tests.
/// </summary>
public class PlaywrightFixture : IAsyncDisposable
{
    /// <summary>
    /// The name of the collection for tests that use this fixture.
    /// </summary>
    public const string CollectionName = "PlaywrightCollection";
    #region Fields
    private readonly IPlaywright _playwright;
    private readonly IBrowser _browser;
    private readonly PlaywrightTestSettings _settings;
    #endregion

    #region Properties
    public IBrowser Browser => _browser;
    public IPage? Page { get; private set; }
    public PlaywrightTestSettings Settings => _settings;
    public IServiceProvider ServiceProvider { get; private set; }
    public string BaseUrl => _settings.BaseUrl;
    #endregion

    #region Constructors
    public PlaywrightFixture()
    {
        // Load configuration
        _settings = LoadConfiguration();

        BasePage.BaseUrl = _settings.BaseUrl;

        // Initialize Playwright
        _playwright = Microsoft.Playwright.Playwright.CreateAsync().GetAwaiter().GetResult();

        // Setup browser
        _browser = _playwright
            .Chromium.LaunchAsync(
                new BrowserTypeLaunchOptions { Headless = _settings.Headless, SlowMo = _settings.SlowMoMs }
            )
            .GetAwaiter()
            .GetResult();

        // Setup service provider for tests
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // Add services needed for tests
        services.AddIdentity<ApplicationUser, IdentityRole>().AddEntityFrameworkStores<ApplicationDbContext>();
    }
    #endregion

    #region Methods
    /// <summary>
    /// Creates a new page and navigates to the base URL.
    /// </summary>
    public async Task<IPage> CreatePageAsync()
    {
        Page = await Browser.NewPageAsync(
            new BrowserNewPageOptions
            {
                IgnoreHTTPSErrors = true,
                ViewportSize = new ViewportSize { Width = _settings.ViewportWidth, Height = _settings.ViewportHeight },
            }
        );

        return Page;
    }

    /// <summary>
    /// Navigates to the specified URL, relative to the base URL.
    /// </summary>
    /// <param name="url">The relative URL to navigate to.</param>
    public async Task GotoAsync(string url = "")
    {
        Page ??= await CreatePageAsync();

        await Page.GotoAsync($"{_settings.BaseUrl}/{url.TrimStart('/')}");
    }

    private static PlaywrightTestSettings LoadConfiguration()
    {
        var configBuilder = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddJsonFile("appsettings.Test.json", optional: false)
            .AddUserSecrets<PlaywrightFixture>()
            .AddEnvironmentVariables();

        var configuration = configBuilder.Build();

        var settings = new PlaywrightTestSettings();
        configuration.GetSection("TestSettings").Bind(settings);

        return settings;
    }

    public async ValueTask DisposeAsync()
    {
        if (Page != null)
        {
            await Page.CloseAsync();
        }

        await Browser.CloseAsync();
        _playwright.Dispose();
    }
    #endregion
}
