using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Tests.Helpers;

/// <summary>
/// Because the <see cref="ApplicationDbContext"/> OnModelCreating method is only called once
/// and then Cached, we need to call tests that depend on mocking the <see cref="IIdGenerationUtilService"/>
/// serially, and use this wrapper for the <see cref="IIdGenerationUtilService"/> to ensure that the
/// they get the proper underlying IIdGenerationUtilService or mock.
/// </summary>
public class StaticIdGenerationUtilServiceForTesting : IIdGenerationUtilService
{
    public IIdGenerationUtilService? WrappedIdGenService { get; private set; }

    private static readonly StaticIdGenerationUtilServiceForTesting _instance =
        new StaticIdGenerationUtilServiceForTesting();

    private StaticIdGenerationUtilServiceForTesting() { }

    #region methods

    public static void Configure(IIdGenerationUtilService wrappedIdGenService)
    {
        if (wrappedIdGenService == null)
            throw new ArgumentNullException(nameof(wrappedIdGenService));
        _instance.WrappedIdGenService = wrappedIdGenService;
    }

    public static void Configure(Func<IIdGenerationUtilService> wrappedIdGenServiceFactory)
    {
        if (wrappedIdGenServiceFactory == null)
            throw new ArgumentNullException(nameof(wrappedIdGenServiceFactory));
        _instance.WrappedIdGenService = wrappedIdGenServiceFactory.Invoke();
    }

    public string GenerateId(IHasAutoInsertedId entity)
    {
        return WrappedIdGenService!.GenerateId(entity);
    }

    public static StaticIdGenerationUtilServiceForTesting GetInstance()
    {
        return _instance;
    }

    public static StaticIdGenerationUtilServiceForTesting GetInstanceAndConfigure(
        IIdGenerationUtilService wrappedIdGenService
    )
    {
        Configure(wrappedIdGenService);
        return _instance;
    }

    #endregion
}
