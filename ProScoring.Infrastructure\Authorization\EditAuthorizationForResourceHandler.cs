using Microsoft.AspNetCore.Authorization;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler for edit operations on entities.
/// </summary>
public class EditAuthorizationForResourceHandler(IAuthorizationProvider authorizationProvider)
    : AuthorizationHandlerForResourceBase<EditAuthorizationForResourceHandler.Requirement>(
        authorizationProvider,
        AuthTypes.Actions.EDIT
    )
{
    public const string PolicyName = "EditResourcePolicy";

    /// <summary>
    /// The authorization requirement for edit operations.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
