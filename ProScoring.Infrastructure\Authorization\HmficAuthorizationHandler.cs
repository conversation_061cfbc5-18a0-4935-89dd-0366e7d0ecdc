using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler that authorizes users with the HMFIC claim.
/// This handler is used to restrict access to administrative features.
/// </summary>
public class HmficAuthorizationHandler : AuthorizationHandler<HmficAuthorizationHandler.Requirement>
{
    private readonly ILogger<HmficAuthorizationHandler> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="HmficAuthorizationHandler"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for this handler.</param>
    public HmficAuthorizationHandler(ILogger<HmficAuthorizationHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// The policy name used for the HMFIC requirement.
    /// </summary>
    public static readonly string PolicyName = nameof(HmficAuthorizationHandler).Replace("Handler", "Policy");

    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, Requirement requirement)
    {
        if (context.User.Identity?.IsAuthenticated != true)
        {
            _logger.LogDebug("User is not authenticated when checking HMFIC authorization");
            return Task.CompletedTask;
        }

        // Check if the user has the HMFIC claim
        bool isHmfic = context.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");

        if (isHmfic)
        {
            _logger.LogInformation(
                "User {UserId} has HMFIC claim - authorization granted",
                context.User.Identity?.Name ?? "unknown"
            );
            context.Succeed(requirement);
        }
        else
        {
            _logger.LogInformation(
                "User {UserId} does not have HMFIC claim - authorization denied",
                context.User.Identity?.Name ?? "unknown"
            );
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// The authorization requirement for the HMFIC policy.
    /// This class is used to identify the requirement type for the handler.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
