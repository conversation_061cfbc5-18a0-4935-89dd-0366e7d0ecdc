using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Page object model for error pages.
/// </summary>
public class ErrorPage : BasePage
{
    #region Selectors
    public static string ErrorMessageSelector => "h2.display-4";
    public static string ErrorCodeSelector => "h1.display-1";
    public static string ReturnButtonSelector => "button.btn-primary";
    #endregion

    #region Constructors
    public ErrorPage(IPage page)
        : base(page, "/error") { }
    #endregion

    #region Methods
    /// <summary>
    /// Verifies that the error page has loaded correctly.
    /// </summary>
    /// <returns>A task representing the verification operation.</returns>
    public override async Task VerifyPageLoadedAsync()
    {
        await _page.WaitForSelectorAsync(ErrorCodeSelector);
        await _page.WaitForSelectorAsync(ReturnButtonSelector);
    }

    /// <summary>
    /// Gets the error message text.
    /// </summary>
    /// <returns>The error message text.</returns>
    public async Task<string> GetErrorMessageAsync()
    {
        return await _page.TextContentAsync(ErrorMessageSelector) ?? string.Empty;
    }

    /// <summary>
    /// Gets the error code.
    /// </summary>
    /// <returns>The error code.</returns>
    public async Task<string> GetErrorCodeAsync()
    {
        return await _page.TextContentAsync(ErrorCodeSelector) ?? string.Empty;
    }

    /// <summary>
    /// Clicks the return to home button.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickReturnToHomeAsync()
    {
        await _page.ClickAsync(ReturnButtonSelector);
    }
    #endregion
}
