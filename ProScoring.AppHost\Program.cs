using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace ProScoring.AppHost;

internal static class Program
{
    private static void Main(string[] args)
    {
        var builder = DistributedApplication.CreateBuilder(args);

#pragma warning disable S125
        //var cache = builder.AddRedis("cache");
#pragma warning restore S125

        var apiService = builder.AddProject<Projects.ProScoring_ApiService>("apiservice");

        var useSqLite = builder.Configuration.GetSection("database").GetValue<bool>("UseSqLite");

        if (useSqLite)
        {
            builder
                .AddProject<Projects.ProScoring_Blazor>("proscoring-blazor")
                .WithExternalHttpEndpoints()
                //.WithReference(cache)
                //.WaitFor(cache)
                .WithReference(apiService)
                .WaitFor(apiService);
        }
        else
        {
            var postgres = builder.AddPostgres("postgres").WithPgAdmin();
            if (builder.Environment.IsDevelopment()) // only in development, in production you should use a persistent volume or a managed database (e.g. Azure Database for PostgreSQL
            {
                postgres.WithDataVolume();
            }
            var proscoringDb = postgres.AddDatabase("ProScoringDb");
            builder
                .AddProject<Projects.ProScoring_Blazor>("proscoring-blazor")
                .WithExternalHttpEndpoints()
                //.WithReference(cache)
                //.WaitFor(cache)
                .WithReference(proscoringDb)
                .WaitFor(proscoringDb)
                .WithReference(apiService)
                .WaitFor(apiService);
        }
        builder.Build().Run();
    }
}
