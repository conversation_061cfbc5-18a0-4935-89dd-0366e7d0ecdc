using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Bunit;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Blazor.Pages;
using Xunit;

namespace ProScoring.Tests.Blazor;

public class DocumentationTests : TestContext
{
    public DocumentationTests()
    {
        // Create a mock HttpClient
        var httpClient = new HttpClient(new MockHttpMessageHandler());
        Services.AddSingleton(httpClient);

        // Create and register a mock IHttpClientFactory
        var mockHttpClientFactory = Substitute.For<IHttpClientFactory>();
        mockHttpClientFactory.CreateClient("DocumentationClient").Returns(httpClient);
        Services.AddSingleton(mockHttpClientFactory);

        // NavigationManager is already registered by BUnit
    }

    // Mock HttpMessageHandler for testing
    private class MockHttpMessageHandler : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken
        )
        {
            return Task.FromResult(
                new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent("# Test Content"),
                }
            );
        }
    }

    // Mock HttpMessageHandler that returns an error
    private class MockErrorHttpMessageHandler : HttpMessageHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken
        )
        {
            return Task.FromResult(
                new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.NotFound,
                    Content = new StringContent("Not Found"),
                }
            );
        }
    }

    [Fact]
    public void Documentation_RendersCorrectly_WithDefaultPath()
    {
        // Act
        var cut = RenderComponent<Documentation>();

        // Assert - Check that the navigation links are rendered correctly
        var navLinks = cut.FindAll(".nav-link");
        Assert.Equal(2, navLinks.Count);
        Assert.Contains(navLinks, link => link.TextContent.Contains("Home"));
        Assert.Contains(navLinks, link => link.TextContent.Contains("OData API"));

        // Check that the active link is the Home link
        var activeLink = cut.Find(".nav-link.active");
        Assert.Equal("Home", activeLink.TextContent.Trim());
    }

    [Fact]
    public void Documentation_ShowsErrorMessage_WhenLoadingFails()
    {
        // Arrange - Create a custom mock handler that returns an error
        var errorHandler = new MockErrorHttpMessageHandler();
        var httpClient = new HttpClient(errorHandler);

        // Replace the existing HttpClient
        Services.AddSingleton(httpClient);

        // Update the HttpClientFactory to return the error client
        var mockHttpClientFactory = Substitute.For<IHttpClientFactory>();
        mockHttpClientFactory.CreateClient("DocumentationClient").Returns(httpClient);
        Services.AddSingleton(mockHttpClientFactory);

        // Act
        var cut = RenderComponent<Documentation>();

        // Use reflection to set private fields for testing
        var errorMessageField = typeof(Documentation).GetField(
            "errorMessage",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance
        );
        var isLoadingField = typeof(Documentation).GetField(
            "isLoading",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance
        );

        errorMessageField?.SetValue(cut.Instance, "Documentation not found");
        isLoadingField?.SetValue(cut.Instance, false);
        cut.Render();

        // Assert
        var alertElement = cut.Find(".alert-danger");
        Assert.NotNull(alertElement);
        Assert.Contains("Error loading documentation", alertElement.TextContent);
    }
}
