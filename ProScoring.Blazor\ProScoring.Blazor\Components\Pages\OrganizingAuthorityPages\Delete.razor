@page "/organizingauthorities/delete"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@attribute [Authorize(Policy = DeleteAuthorizationForPageWithIdHandler.PolicyName)]
@inject IOrganizingAuthorityService OrganizingAuthorityService
@inject NavigationManager NavigationManager

<PageTitle>Delete: @(organizingAuthority?.Name ?? "Loading...")</PageTitle>

<div class="d-flex justify-content-center align-items-start" style="min-height: 100vh; padding: 2rem;">
    <RadzenCard Style="width: 100%; max-width: 800px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;">
        <RadzenText TextStyle="TextStyle.H3">Delete Organizing Authority</RadzenText>

        @if (organizingAuthority != null)
        {
            <RadzenText>Are you sure you want to delete <strong>@organizingAuthority.Name</strong>?</RadzenText>

            <div class="d-flex justify-content-end align-items-center gap-3 mt-4">
                <RadzenButton Text="Cancel" ButtonStyle="ButtonStyle.Light" Click="Cancel"
                    data-testid="cancel-delete-button" />
                <RadzenButton Text="Delete" ButtonStyle="ButtonStyle.Danger" Click="HandleDelete"
                    data-testid="confirm-delete-button" />
            </div>
        }
        else
        {
            <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
        }
    </RadzenCard>
</div>

@code {
    [SupplyParameterFromQuery(Name = "id")]
    public string? Id { get; set; }
    private OrganizingAuthority? organizingAuthority;

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(Id))
        {
            organizingAuthority = await OrganizingAuthorityService.GetByIdAsync(Id);
        }
    }

    /// <summary>
    /// Handles the deletion of the organizing authority.
    /// </summary>
    private async Task HandleDelete()
    {
        if (organizingAuthority != null)
        {
            await OrganizingAuthorityService.DeleteAsync(organizingAuthority.Id!);
            NavigationManager.NavigateTo("/organizingauthorities");
        }
    }

    /// <summary>
    /// Cancels the deletion and navigates back to the list.
    /// </summary>
    private void Cancel()
    {
        NavigationManager.NavigateTo("/organizingauthorities");
    }
}
