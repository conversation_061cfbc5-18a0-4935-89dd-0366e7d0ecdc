using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Base class for tests that verify functionality common to pages using the main layout.
/// </summary>
public abstract class BasePageWithMainLayoutTests : IClassFixture<PlaywrightFixture>
{
    #region Fields
    protected readonly PlaywrightFixture _fixture;
    #endregion Fields

    #region Constructors
    protected BasePageWithMainLayoutTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion Constructors

    #region Methods
    /// <summary>
    /// Gets the test page to use for the current test.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <returns>An instance of a page that uses the main layout.</returns>
    protected abstract BasePageWithMainLayout GetTestPage(IPage page);

    /// <summary>
    /// Helper method to login using the popup login dialog from MainLayout.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <returns>A task representing the login operation.</returns>
    protected async Task LoginViaPopupAsync(IPage page)
    {
        // Navigate directly to the test page
        var testPage = GetTestPage(page);
        await testPage.NavigateAsync();

        // Click the login button to open the popup dialog
        await testPage.ClickLoginButton();

        // Wait for the login dialog to appear
        await page.WaitForSelectorAsync(".rz-login input[name='Username']");

        // Fill in login credentials and submit
        await page.FillAsync(".rz-login input[name='Username']", _fixture.Settings.TestUserEmail);
        await page.FillAsync(".rz-login input[name='Password']", _fixture.Settings.TestUserPassword);

        // Click the Login button
        await page.ClickAsync(".rz-login button[type='submit']");

        // Wait for the page to reload after login
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Verify that we're logged in by checking for the user options button
        await page.WaitForSelectorAsync("[data-testid=user-options-split-button]");
    }

    /// <summary>
    /// Helper method to verify that the user options menu items are visible when a part of the split button is clicked.
    /// </summary>
    /// <param name="clickAction">The action to perform on the button (click menu part or main part).</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected async Task VerifyUserOptionsMenuItemsAsync(Func<SplitButtonTestObject, Task> clickAction)
    {
        // Arrange - Log in using the popup login dialog from MainLayout
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login via popup - this also navigates to the test page
            await LoginViaPopupAsync(page);

            // Get the test page instance (already navigated to in LoginViaPopupAsync)
            var testPage = GetTestPage(page);

            // need to delay before the click action for it to run correctly
            await Task.Delay(10);
            // Act - Click the specified part of the split button
            await clickAction(testPage.UserOptionsButton);

            var menuItems = await testPage.UserOptionsButton.GetMenuItemsAsync();

            // Assert
            menuItems.Count().Should().Be(2, "There should be two menu items: 'My Account' and 'Logout'.");

            var menuItemTexts = new List<string>();
            foreach (var menuItem in menuItems)
            {
                var isVisible = await menuItem.IsVisibleAsync();

                // Find the span with class "rz-menuitem-text" which contains only the menu text (not the icon)
                var textSpan = await menuItem.QuerySelectorAsync(".rz-menuitem-text");
                if (textSpan != null)
                {
                    var text = await textSpan.TextContentAsync();
                    if (text is not null)
                        menuItemTexts.Add(text);
                }

                isVisible.Should().BeTrue("All menu items should be visible when the split button is clicked");
            }

            // Verify option text is correct
            menuItemTexts.Should().ContainMatch("*My Account");
            menuItemTexts.Should().ContainMatch("*Logout");
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Methods

    #region Test Methods
    /// <summary>
    /// Tests that the user options menu items are visible when the dropdown part of the split button is clicked.
    /// </summary>
    [Fact]
    public virtual async Task UserOptions_MenuOptions_AreVisible_WhenDropDownPartOfSplitButtonClicked()
    {
        // Arrange & Act & Assert
        await VerifyUserOptionsMenuItemsAsync(button => button.ClickMenuPartAsync());
    }

    /// <summary>
    /// Tests that the user options menu items are visible when the main part of the split button is clicked.
    /// </summary>
    [Fact]
    public virtual async Task UserOptions_MenuOptions_AreVisible_WhenMainPartOfSplitButtonClicked()
    {
        // Arrange & Act & Assert
        await VerifyUserOptionsMenuItemsAsync(button => button.ClickButtonPartAsync());
    }

    /// <summary>
    /// Tests that the user options menu contains the expected number of options.
    /// </summary>
    [Fact]
    public async Task UserOptions_Menu_Contains_Two_Options()
    {
        // Arrange - Log in using the popup login dialog from MainLayout
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login via popup - this also navigates to the test page
            await LoginViaPopupAsync(page);

            // Act - Open user options menu
            var testPage = GetTestPage(page);
            await testPage.ClickMyAccountMenuItem();

            // Assert - Verify there are 2 options
            var optionsCount = await testPage.GetUserMenuOptionsCount();
            optionsCount.Should().Be(2);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking the logout option logs the user out.
    /// </summary>
    [Fact]
    public virtual async Task Clicking_Logout_Option_Logs_User_Out()
    {
        // Arrange - Log in using the popup login dialog from MainLayout
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login via popup - this also navigates to the test page
            await LoginViaPopupAsync(page);

            // Act - Open user options and click logout
            var testPage = GetTestPage(page);
            await testPage.ClickMyAccountMenuItem();
            await testPage.ClickLogoutOption();

            // Wait for redirect after logout
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Assert - Login button should be visible again
            var isLoginButtonVisible = await testPage.IsLoginButtonVisible();
            isLoginButtonVisible.Should().BeTrue();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking the account info option navigates to the account page.
    /// </summary>
    [Fact]
    public virtual async Task Clicking_AccountInfo_Option_Navigates_To_Account_Page()
    {
        // Arrange - Log in using the popup login dialog from MainLayout
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Login via popup - this also navigates to the test page
            await LoginViaPopupAsync(page);

            // Act - Open user options and click account info
            var testPage = GetTestPage(page);
            await testPage.ClickMyAccountMenuItem();
            await testPage.ClickAccountInfoOption();

            // Wait for redirect to account page
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(1000); // Sleep for 1 second

            // Assert - We should be on the account page
            page.Url.ToLower().Should().Contain("/account");

            // Verify account page elements
            var manageProfilePage = new ManageProfilePage(page);
            await manageProfilePage.VerifyPageLoadedAsync();

            var username = await manageProfilePage.GetUsernameAsync();
            username.Should().NotBeNullOrEmpty();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar menu is initially hidden on mobile devices.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Is_Initially_Hidden_On_Mobile()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Set viewport to mobile size
            await page.SetViewportSizeAsync(375, 667); // iPhone 8 size

            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Act - Check if sidebar is visible
            var isSidebarVisible = await testPage.IsSidebarVisible();

            // Assert
            isSidebarVisible.Should().BeFalse("Sidebar should be hidden on mobile devices initially");

            // Also verify the sidebar is responsive
            var isSidebarResponsive = await testPage.IsSidebarResponsive();
            isSidebarResponsive.Should().BeTrue("Sidebar should be responsive on mobile devices");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar menu can be toggled on and off.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Can_Be_Toggled()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Get initial state
            var initiallyVisible = await testPage.IsSidebarVisible();

            // Act - Toggle sidebar
            await testPage.ClickSidebarToggle();

            // Assert - Sidebar visibility should be toggled
            var afterToggleVisible = await testPage.IsSidebarVisible();
            afterToggleVisible
                .Should()
                .NotBe(initiallyVisible, "Sidebar visibility should toggle when the toggle button is clicked");

            // Toggle back
            await testPage.ClickSidebarToggle();

            // Assert - Should be back to initial state
            var afterSecondToggleVisible = await testPage.IsSidebarVisible();
            afterSecondToggleVisible
                .Should()
                .Be(initiallyVisible, "Sidebar visibility should return to initial state after toggling twice");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar menu contains the expected navigation items.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Contains_Expected_Menu_Items()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Make sure sidebar is visible
            if (!await testPage.IsSidebarVisible())
            {
                await testPage.ClickSidebarToggle();
            }

            // Act - Get menu items
            var menuItemTexts = await testPage.GetSidebarMenuItemTexts();

            // Assert - Should have expected menu items
            menuItemTexts.Should().Contain("Home", "Sidebar should contain Home menu item");
            menuItemTexts.Should().Contain("About", "Sidebar should contain About menu item");
            menuItemTexts.Should().Contain("Contact", "Sidebar should contain Contact menu item");
            menuItemTexts.Should().Contain("Settings", "Sidebar should contain Settings menu item");
            menuItemTexts.Should().Contain("Logout", "Sidebar should contain Logout menu item");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking a sidebar menu item navigates to the correct page.
    /// </summary>
    [Fact(Skip = "We haven't implemented the pages that are linked by the sidebar.")]
    public virtual async Task Clicking_Sidebar_Menu_Item_Navigates_To_Correct_Page()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Make sure sidebar is visible
            if (!await testPage.IsSidebarVisible())
            {
                await testPage.ClickSidebarToggle();
            }

            // Act - Click the About menu item
            await testPage.ClickSidebarMenuItem("About");

            // Wait for navigation
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Assert - URL should contain /about
            page.Url.ToLower()
                .Should()
                .Contain("/about", "Clicking the About menu item should navigate to the About page");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar is responsive to screen size changes.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Is_Responsive_To_Screen_Size()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Start with desktop size
            await page.SetViewportSizeAsync(1200, 800);

            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Check if sidebar is responsive on desktop
            var isResponsiveOnDesktop = await testPage.IsSidebarResponsive();
            isResponsiveOnDesktop.Should().BeFalse("Sidebar should not be responsive on desktop");

            // Act - Change to mobile size
            await page.SetViewportSizeAsync(375, 667);

            // Wait a moment for the resize event to be processed
            await Task.Delay(1000);

            // Assert - Sidebar should be responsive on mobile
            var isResponsiveOnMobile = await testPage.IsSidebarResponsive();
            isResponsiveOnMobile.Should().BeTrue("Sidebar should be responsive on mobile devices");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar visibility state is saved to local storage when toggled.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Visibility_State_Is_Saved_To_LocalStorage_When_Toggled()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Use desktop size to ensure local storage is used
            await page.SetViewportSizeAsync(1200, 800);

            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Clear any existing state
            await testPage.ClearMenuVisibilityFromLocalStorage();

            // Get initial state
            var initiallyVisible = await testPage.IsSidebarVisible();

            // Act - Toggle sidebar
            await testPage.ClickSidebarToggle();

            // Wait a moment for the local storage to be updated
            await Task.Delay(500);

            // Assert - Local storage should have the menu visibility state
            var hasMenuVisibility = await testPage.HasMenuVisibilityInLocalStorage();
            hasMenuVisibility.Should().BeTrue("Menu visibility state should be saved to local storage");

            // The value in local storage should match the current sidebar visibility
            var menuVisibilityInStorage = await testPage.GetMenuVisibilityFromLocalStorage();
            var currentVisibility = await testPage.IsSidebarVisible();
            menuVisibilityInStorage
                .Should()
                .Be(currentVisibility, "Menu visibility in local storage should match current sidebar visibility");

            // Toggle again
            await testPage.ClickSidebarToggle();

            // Wait a moment for the local storage to be updated
            await Task.Delay(500);

            // The value in local storage should be updated
            var updatedMenuVisibilityInStorage = await testPage.GetMenuVisibilityFromLocalStorage();
            var updatedVisibility = await testPage.IsSidebarVisible();
            updatedMenuVisibilityInStorage
                .Should()
                .Be(updatedVisibility, "Menu visibility in local storage should be updated when sidebar is toggled");
            updatedVisibility
                .Should()
                .Be(initiallyVisible, "Sidebar visibility should return to initial state after toggling twice");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the sidebar visibility state is loaded from local storage on page load.
    /// </summary>
    [Fact]
    public virtual async Task Sidebar_Visibility_State_Is_Loaded_From_LocalStorage_On_Page_Load()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Use desktop size to ensure local storage is used
            await page.SetViewportSizeAsync(1200, 800);

            var testPage = GetTestPage(page);
            await testPage.NavigateAsync();

            // Clear any existing state
            await testPage.ClearMenuVisibilityFromLocalStorage();

            // Toggle sidebar to save state
            await testPage.ClickSidebarToggle();

            // Wait a moment for the local storage to be updated
            await Task.Delay(500);

            // Get the current visibility state
            var visibilityBeforeReload = await testPage.IsSidebarVisible();

            // Act - Reload the page
            await page.ReloadAsync();
            await testPage.VerifyPageLoadedAsync();

            // Wait a moment for the local storage to be loaded
            await Task.Delay(500);

            // Assert - Sidebar visibility should match the state before reload
            var visibilityAfterReload = await testPage.IsSidebarVisible();
            visibilityAfterReload
                .Should()
                .Be(visibilityBeforeReload, "Sidebar visibility should be loaded from local storage on page load");
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Test Methods
}
