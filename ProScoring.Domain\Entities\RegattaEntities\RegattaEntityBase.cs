using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// This is the base class for all regatta entities.
/// It contains the info on the regatta for the relationships.
/// </summary>
public abstract class RegattaEntityBase<T> : LastChangeTrackingWithAutoInsertedIdBase
    where T : RegattaEntityBase<T>
{
    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for this entity.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets or sets the regatta this entity belongs to.
    /// </summary>
    public virtual Regatta? Regatta { get; set; }

    #endregion
}
