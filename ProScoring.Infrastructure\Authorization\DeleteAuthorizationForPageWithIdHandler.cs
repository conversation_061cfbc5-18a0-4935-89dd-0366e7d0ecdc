using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler for delete operations on pages with IDs.
/// Determines if the current user has permission to delete specific pages in the system.
/// </summary>
/// <param name="authorizationProvider">The provider that determines authorization status.</param>
/// <param name="navigationManager">The navigation manager to access the current URL.</param>
public class DeleteAuthorizationForPageWithIdHandler(
    IAuthorizationProvider authorizationProvider,
    NavigationManager navigationManager
)
    : AuthorizationHandlerForPageWithIdBase<DeleteAuthorizationForPageWithIdHandler.Requirement>(
        authorizationProvider,
        navigationManager,
        AuthTypes.Actions.DELETE
    )
{
    /// <summary>
    /// The policy name used for delete authorization requirements on pages with IDs.
    /// </summary>
    public const string PolicyName = "DeletePageWithIdPolicy";

    /// <summary>
    /// The authorization requirement for delete operations on pages with IDs.
    /// This class is used to identify the requirement type for the handler.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
