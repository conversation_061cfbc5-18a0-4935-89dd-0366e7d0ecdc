@page "/"
@using Microsoft.AspNetCore.Components.Authorization
@using ProScoring.Blazor.Components.Layout
@using ProScoring.Blazor.Components.Shared
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@attribute [AllowAnonymous]
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject DialogService DialogService
@inject ILocalStorageServiceWithExpiration LocalStorage
@inject MainLayoutContextService LayoutContext
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService

<HeadContent>
    <title>ProScoring.Online</title>
    <meta name="description"
        content="Welcome to ProScoring.Online, your one-stop solution for all your scoring needs." />
    <meta name="keywords" content="ProScoring, scoring, online, solution" />
</HeadContent>
<div class="landing-page">
    <div class="max-width rz-mx-auto">
        <RadzenRow JustifyContent="JustifyContent.Center">
            <RadzenSelectBar Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center"
                @bind-Value="@selectedCard" @attributes="@("index-select-bar".AsTestId())">
                <Items>
                    <RadzenSelectBarItem Text="About" Value=CardType.About
                        @attributes="@("index-about-tab".AsTestId())" />
                    <RadzenSelectBarItem Text="Regattas" Value=CardType.Regattas
                        @attributes="@("index-regattas-tab".AsTestId())" />
                    <RadzenSelectBarItem Text="Organizers" Value=CardType.Organizers
                        @attributes="@("index-organizers-tab".AsTestId())" />
                </Items>
            </RadzenSelectBar>
        </RadzenRow>

        <RadzenRow JustifyContent="JustifyContent.Center" class="rz-mt-2">
            <RadzenCard Visible=@(selectedCard == CardType.About) class="w-100"
                @attributes="@("index-about-card".AsTestId())">
                <div class="rz-card-header">
                    <strong>About ProScoring</strong>
                </div>
                <div class="rz-card-body">
                    <p>ProScoring is a comprehensive online scoring solution designed for various sports and events.</p>
                    <p>Our platform offers real-time scoring, detailed analytics, and user-friendly interfaces.</p>
                </div>
            </RadzenCard>
            <RadzenCard Visible=@(selectedCard == CardType.Regattas) class="w-100"
                @attributes="@("index-regattas-card".AsTestId())">
                <div class="rz-card-header">
                    <strong>Regattas</strong>
                </div>
                <div class="rz-card-body">
                    <p>List Regattas here.</p>
                </div>
            </RadzenCard>
            <RadzenCard Visible=@(selectedCard == CardType.Organizers) class="w-100"
                @attributes="@("index-organizers-card".AsTestId())">
                <div class="rz-card-header d-flex justify-content-between align-items-center">
                    <strong>Organizers</strong>
                    <div class="ms-3">
                        <RadzenButton Icon="add" Text="Add Organizing Authority" ButtonStyle="ButtonStyle.Primary"
                            Click="@AddOrganizingAuthority" Size="ButtonSize.Small"
                            @attributes="@("add-organizing-authority-button".AsTestId())"
                            title="Authorized Users. Click here to create an organizing authority." />
                    </div>
                </div>
                <div class="rz-card-body">
                    <ProScoring.Blazor.Components.Shared.OrganizingAuthoritiesList />
                </div>
            </RadzenCard>
        </RadzenRow>
    </div>
    @* <h1>Welcome to ProScoring.Online</h1>
    <p>Your one-stop solution for all your scoring needs.</p>
    <p>Explore our features and get started today!</p> *@
</div>

@code {
    #region fields
    private const string SelectedCardKey = "ProScoring_SelectedCard";
    private CardType _selectedCard = CardType.About;
    #endregion fields

    #region lifecycle methods
    /// <summary>
    /// Initializes the component by loading the saved card selection from local storage.
    /// Also checks if we need to navigate to the organizing authority create page after login.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected override async Task OnInitializedAsync()
    {
        await LoadSelectedCardAsync();

        await base.OnInitializedAsync();
    }
    #endregion lifecycle methods

    #region properties
    /// <summary>
    /// Gets or sets the currently selected card type.
    /// </summary>
    private CardType selectedCard
    {
        get => _selectedCard;
        set
        {
            if (_selectedCard != value)
            {
                _selectedCard = value;
                SaveSelectedCardAsync().ConfigureAwait(false);
            }
        }
    }
    #endregion properties

    #region methods
    /// <summary>
    /// Handles the click event for the "Add Organizing Authority" button.
    /// If the user is not logged in, shows the login dialog.
    /// If the user is logged in, navigates to the Create Organizing Authority page.
    /// </summary>
    private async Task AddOrganizingAuthority()
    {
        // Check if the user is authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated != true)
        {
            // User is not logged in, use the shared login dialog from MainLayout
            try
            {
                await LayoutContext.OpenLoginDialogAsync(() =>
                {
                    NavigationManager.NavigateTo("/organizingauthorities/create", true);
                });
            }
            catch (Exception)
            {
                // If there's an error opening the login dialog or the user cancels
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Warning,
                    Summary = "Login Required",
                    Detail = "Only logged-in users may create organizing authorities.",
                    Duration = 5000
                });
            }
        }
        else
        {
            // User is already logged in, navigate to the create page
            NavigationManager.NavigateTo("/organizingauthorities/create");
        }
    }

    /// <summary>
    /// Loads the selected card type from local storage.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task LoadSelectedCardAsync()
    {
        try
        {
            // TODO: this throws an error on prerendering because we can't get localstorage
            var savedCard = await LocalStorage.GetItemAsync<CardType?>(SelectedCardKey);
            if (savedCard.HasValue)
            {
                // Use the backing field directly to avoid triggering a save operation
                _selectedCard = savedCard.Value;
            }
        }
        catch
        {
            // Ignore errors reading from local storage
        }
    }

    /// <summary>
    /// Saves the selected card type to local storage.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task SaveSelectedCardAsync()
    {
        try
        {
            await LocalStorage.SetItemAsync(SelectedCardKey, _selectedCard);
        }
        catch
        {
            // Ignore errors writing to local storage
        }
    }
    #endregion methods

    #region nested types
    private enum CardType
    {
        About,
        Regattas,
        Organizers
    }
    #endregion nested types
}
