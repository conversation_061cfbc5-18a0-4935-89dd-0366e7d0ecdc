﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace ProScoring.Infrastructure.Database.SQLite_Migrations
{
    /// <inheritdoc />
    public partial class SQLite_InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    GivenName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Surname = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    UserName = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    PhoneNumber = table.Column<string>(type: "TEXT", nullable: true),
                    AccessFailedCount = table.Column<int>(type: "INTEGER", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    NormalizedUserName = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "TEXT", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "INTEGER", nullable: false),
                    PasswordHash = table.Column<string>(type: "TEXT", nullable: true),
                    SecurityStamp = table.Column<string>(type: "TEXT", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "TEXT", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "INTEGER", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "INTEGER", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "TEXT", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUsers_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AspNetUsers_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AuthActions",
                columns: table => new
                {
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuthActions", x => x.Name);
                });

            migrationBuilder.CreateTable(
                name: "CountryCode",
                columns: table => new
                {
                    Code = table.Column<string>(type: "TEXT", maxLength: 3, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CountryCode", x => x.Code);
                });

            migrationBuilder.CreateTable(
                name: "LinkType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 25, nullable: false),
                    Name = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LinkType", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TargetTypes",
                columns: table => new
                {
                    IdPrefix = table.Column<string>(type: "TEXT", maxLength: 5, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TargetTypes", x => x.IdPrefix);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    RoleId = table.Column<string>(type: "TEXT", nullable: false),
                    ClaimType = table.Column<string>(type: "TEXT", nullable: true),
                    ClaimValue = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    UserId = table.Column<string>(type: "TEXT", nullable: false),
                    ClaimType = table.Column<string>(type: "TEXT", nullable: true),
                    ClaimValue = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "TEXT", nullable: false),
                    ProviderKey = table.Column<string>(type: "TEXT", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "TEXT", nullable: true),
                    UserId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "TEXT", nullable: false),
                    RoleId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "TEXT", nullable: false),
                    LoginProvider = table.Column<string>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", nullable: false),
                    Value = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Competitor",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: true),
                    Name = table.Column<string>(type: "TEXT", nullable: true),
                    Email = table.Column<string>(type: "TEXT", nullable: true),
                    Phone = table.Column<string>(type: "TEXT", nullable: true),
                    Address = table.Column<string>(type: "TEXT", nullable: true),
                    Address2 = table.Column<string>(type: "TEXT", nullable: true),
                    City = table.Column<string>(type: "TEXT", nullable: true),
                    State = table.Column<string>(type: "TEXT", nullable: true),
                    ZipCode = table.Column<string>(type: "TEXT", nullable: true),
                    Country = table.Column<string>(type: "TEXT", nullable: true),
                    BirthDate = table.Column<DateOnly>(type: "TEXT", nullable: true),
                    WorldSailingNumber = table.Column<string>(type: "TEXT", nullable: true),
                    MemberNationalAuthority = table.Column<string>(type: "TEXT", nullable: true),
                    MnaNumber = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactName1 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactPhone1 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactName2 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactPhone2 = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Competitor", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Competitor_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Competitor_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Competitor_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Files",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    UntrustedName = table.Column<string>(type: "TEXT", nullable: false),
                    Size = table.Column<long>(type: "INTEGER", nullable: false),
                    ContentType = table.Column<string>(type: "TEXT", nullable: false),
                    Note = table.Column<string>(type: "TEXT", nullable: true),
                    TrustedFileNameForDisplay = table.Column<string>(type: "TEXT", nullable: false),
                    Path = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    UploadDate = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Files", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Files_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Files_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RatingType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RatingType", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RatingType_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RatingType_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ActionHierarchies",
                columns: table => new
                {
                    ParentActionName = table.Column<string>(type: "TEXT", nullable: false),
                    ChildActionName = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ActionHierarchies", x => new { x.ParentActionName, x.ChildActionName });
                    table.ForeignKey(
                        name: "FK_ActionHierarchies_AuthActions_ChildActionName",
                        column: x => x.ChildActionName,
                        principalTable: "AuthActions",
                        principalColumn: "Name",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ActionHierarchies_AuthActions_ParentActionName",
                        column: x => x.ParentActionName,
                        principalTable: "AuthActions",
                        principalColumn: "Name",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserAuthActions",
                columns: table => new
                {
                    AuthActionName = table.Column<string>(type: "TEXT", nullable: false),
                    TargetId = table.Column<string>(type: "TEXT", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserAuthActions", x => new { x.AuthActionName, x.UserId, x.TargetId });
                    table.ForeignKey(
                        name: "FK_UserAuthActions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserAuthActions_AuthActions_AuthActionName",
                        column: x => x.AuthActionName,
                        principalTable: "AuthActions",
                        principalColumn: "Name",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Boat",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    CountryCodeId = table.Column<string>(type: "TEXT", nullable: true),
                    SailNumber = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BoatType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Length = table.Column<float>(type: "REAL", nullable: true),
                    LengthUnit = table.Column<int>(type: "INTEGER", nullable: true),
                    OwnerId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Boat", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Boat_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Boat_AspNetUsers_OwnerId",
                        column: x => x.OwnerId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Boat_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Boat_CountryCode_CountryCodeId",
                        column: x => x.CountryCodeId,
                        principalTable: "CountryCode",
                        principalColumn: "Code");
                });

            migrationBuilder.CreateTable(
                name: "OrganizingAuthorities",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "TEXT", nullable: true),
                    Phone = table.Column<string>(type: "TEXT", nullable: true),
                    Website = table.Column<string>(type: "TEXT", nullable: true),
                    Private = table.Column<bool>(type: "INTEGER", nullable: false),
                    AddressLine1 = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    AddressLine2 = table.Column<string>(type: "TEXT", nullable: true),
                    City = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    State = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    PostalCode = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true),
                    Country = table.Column<string>(type: "TEXT", maxLength: 60, nullable: true),
                    ImageId = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrganizingAuthorities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrganizingAuthorities_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizingAuthorities_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OrganizingAuthorities_Files_ImageId",
                        column: x => x.ImageId,
                        principalTable: "Files",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RatingValueType",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    RatingTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RatingValueType", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RatingValueType_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RatingValueType_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RatingValueType_RatingType_RatingTypeId",
                        column: x => x.RatingTypeId,
                        principalTable: "RatingType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Rating",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    BoatId = table.Column<string>(type: "TEXT", nullable: false),
                    RatingTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    RatingNote = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Rating", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Rating_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Rating_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Rating_Boat_BoatId",
                        column: x => x.BoatId,
                        principalTable: "Boat",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Rating_RatingType_RatingTypeId",
                        column: x => x.RatingTypeId,
                        principalTable: "RatingType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Regattas",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    OrganizingAuthorityId = table.Column<string>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    StartDate = table.Column<DateOnly>(type: "TEXT", nullable: false),
                    CompetitionDays = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RegistrationDeadline = table.Column<DateTimeOffset>(type: "TEXT", nullable: true),
                    RegistrationOpening = table.Column<DateTimeOffset>(type: "TEXT", nullable: true),
                    AddressLine1 = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    AddressLine2 = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    City = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    EventLogoId = table.Column<string>(type: "TEXT", nullable: true),
                    EndDate = table.Column<DateOnly>(type: "TEXT", nullable: false),
                    Location = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    Private = table.Column<bool>(type: "INTEGER", nullable: false),
                    State = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Website = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Regattas", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Regattas_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Regattas_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Regattas_Files_EventLogoId",
                        column: x => x.EventLogoId,
                        principalTable: "Files",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Regattas_OrganizingAuthorities_OrganizingAuthorityId",
                        column: x => x.OrganizingAuthorityId,
                        principalTable: "OrganizingAuthorities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RatingValue",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    RatingId = table.Column<string>(type: "TEXT", nullable: false),
                    ValueTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    Value = table.Column<float>(type: "REAL", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RatingValue", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RatingValue_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RatingValue_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RatingValue_RatingValueType_ValueTypeId",
                        column: x => x.ValueTypeId,
                        principalTable: "RatingValueType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RatingValue_Rating_RatingId",
                        column: x => x.RatingId,
                        principalTable: "Rating",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RegattaCompetitors",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    CompetitorId = table.Column<string>(type: "TEXT", nullable: true),
                    Name = table.Column<string>(type: "TEXT", nullable: true),
                    Email = table.Column<string>(type: "TEXT", nullable: true),
                    Phone = table.Column<string>(type: "TEXT", nullable: true),
                    Address = table.Column<string>(type: "TEXT", nullable: true),
                    Address2 = table.Column<string>(type: "TEXT", nullable: true),
                    City = table.Column<string>(type: "TEXT", nullable: true),
                    State = table.Column<string>(type: "TEXT", nullable: true),
                    ZipCode = table.Column<string>(type: "TEXT", nullable: true),
                    Country = table.Column<string>(type: "TEXT", nullable: true),
                    BirthDate = table.Column<DateOnly>(type: "TEXT", nullable: true),
                    WorldSailingNumber = table.Column<string>(type: "TEXT", nullable: true),
                    MemberNationalAuthority = table.Column<string>(type: "TEXT", nullable: true),
                    MnaNumber = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactName1 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactPhone1 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactName2 = table.Column<string>(type: "TEXT", nullable: true),
                    EmergencyContactPhone2 = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaCompetitors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaCompetitors_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaCompetitors_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaCompetitors_Competitor_CompetitorId",
                        column: x => x.CompetitorId,
                        principalTable: "Competitor",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaCompetitors_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RegattaExternalLinks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false),
                    LinkTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    Url = table.Column<string>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaExternalLinks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaExternalLinks_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaExternalLinks_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaExternalLinks_LinkType_LinkTypeId",
                        column: x => x.LinkTypeId,
                        principalTable: "LinkType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RegattaExternalLinks_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RegattaFleets",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RatingRequired = table.Column<bool>(type: "INTEGER", nullable: false),
                    CrewRequired = table.Column<bool>(type: "INTEGER", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaFleets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaFleets_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaFleets_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaFleets_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RegattaClasses",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RegattaFleetId = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaClasses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaClasses_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaClasses_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaClasses_RegattaFleets_RegattaFleetId",
                        column: x => x.RegattaFleetId,
                        principalTable: "RegattaFleets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RegattaClasses_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RegattaBoats",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    CountryCodeId = table.Column<string>(type: "TEXT", nullable: true),
                    SailNumber = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BoatType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Length = table.Column<float>(type: "REAL", nullable: true),
                    LengthUnit = table.Column<int>(type: "INTEGER", nullable: true),
                    MasterBoatId = table.Column<string>(type: "TEXT", nullable: true),
                    RegattaFleetId = table.Column<string>(type: "TEXT", nullable: true),
                    RegattaClassId = table.Column<string>(type: "TEXT", nullable: true),
                    SkipperId = table.Column<string>(type: "TEXT", nullable: true),
                    Skipper2Id = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaBoats", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaBoats_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_Boat_MasterBoatId",
                        column: x => x.MasterBoatId,
                        principalTable: "Boat",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_CountryCode_CountryCodeId",
                        column: x => x.CountryCodeId,
                        principalTable: "CountryCode",
                        principalColumn: "Code");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_RegattaClasses_RegattaClassId",
                        column: x => x.RegattaClassId,
                        principalTable: "RegattaClasses",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_RegattaCompetitors_Skipper2Id",
                        column: x => x.Skipper2Id,
                        principalTable: "RegattaCompetitors",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_RegattaCompetitors_SkipperId",
                        column: x => x.SkipperId,
                        principalTable: "RegattaCompetitors",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_RegattaFleets_RegattaFleetId",
                        column: x => x.RegattaFleetId,
                        principalTable: "RegattaFleets",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaBoats_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RegattaBoatCompetitor",
                columns: table => new
                {
                    RegattaBoatId = table.Column<string>(type: "TEXT", nullable: false),
                    RegattaCompetitorId = table.Column<string>(type: "TEXT", nullable: false),
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaBoatCompetitor", x => new { x.RegattaBoatId, x.RegattaCompetitorId });
                    table.ForeignKey(
                        name: "FK_RegattaBoatCompetitor_RegattaBoats_RegattaBoatId",
                        column: x => x.RegattaBoatId,
                        principalTable: "RegattaBoats",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RegattaBoatCompetitor_RegattaCompetitors_RegattaCompetitorId",
                        column: x => x.RegattaCompetitorId,
                        principalTable: "RegattaCompetitors",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RegattaRatings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    RegattaBoatId = table.Column<string>(type: "TEXT", nullable: false),
                    SourceRatingId = table.Column<string>(type: "TEXT", nullable: true),
                    RatingTypeId = table.Column<string>(type: "TEXT", nullable: false),
                    RatingNote = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    KeepSynced = table.Column<bool>(type: "INTEGER", nullable: false),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaRatings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaRatings_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaRatings_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaRatings_RatingType_RatingTypeId",
                        column: x => x.RatingTypeId,
                        principalTable: "RatingType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RegattaRatings_Rating_SourceRatingId",
                        column: x => x.SourceRatingId,
                        principalTable: "Rating",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_RegattaRatings_RegattaBoats_RegattaBoatId",
                        column: x => x.RegattaBoatId,
                        principalTable: "RegattaBoats",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RegattaRatings_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RegattaRatingValues",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", maxLength: 12, nullable: false),
                    RegattaRatingId = table.Column<string>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Value = table.Column<decimal>(type: "TEXT", precision: 10, scale: 4, nullable: false),
                    Note = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    UpdatedById = table.Column<string>(type: "TEXT", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    CreatedById = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
                    RegattaId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RegattaRatingValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RegattaRatingValues_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaRatingValues_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RegattaRatingValues_RegattaRatings_RegattaRatingId",
                        column: x => x.RegattaRatingId,
                        principalTable: "RegattaRatings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RegattaRatingValues_Regattas_RegattaId",
                        column: x => x.RegattaId,
                        principalTable: "Regattas",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "AspNetUsers",
                columns: new[] { "Id", "AccessFailedCount", "ConcurrencyStamp", "CreatedAt", "CreatedById", "Email", "EmailConfirmed", "GivenName", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PhoneNumber", "PhoneNumberConfirmed", "SecurityStamp", "Surname", "TwoFactorEnabled", "UpdatedAt", "UpdatedById", "UserName" },
                values: new object[] { "U000000000", 0, "00000000-0000-0000-0000-000000000000", new DateTimeOffset(new DateTime(2025, 1, 1, 1, 1, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), "U000000000", "<EMAIL>", true, "<EMAIL>", true, null, "<EMAIL>", "<EMAIL>", "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", null, false, "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", "<EMAIL>", false, new DateTimeOffset(new DateTime(2025, 1, 1, 1, 1, 0, 0, DateTimeKind.Unspecified), new TimeSpan(0, 0, 0, 0, 0)), "U000000000", "<EMAIL>" });

            migrationBuilder.InsertData(
                table: "AuthActions",
                column: "Name",
                values: new object[]
                {
                    "Admin",
                    "Del",
                    "Edit",
                    "HMFIC",
                    "View"
                });

            migrationBuilder.InsertData(
                table: "CountryCode",
                columns: new[] { "Code", "Name" },
                values: new object[,]
                {
                    { "AHO", "Netherlands Antilles" },
                    { "ALG", "Algeria" },
                    { "AND", "Andorra" },
                    { "ANG", "Angola" },
                    { "ANT", "Antigua and Barbuda" },
                    { "ARG", "Argentina" },
                    { "ARM", "Armenia" },
                    { "ARU", "Aruba" },
                    { "ASA", "American Samoa" },
                    { "AUS", "Australia" },
                    { "AUT", "Austria" },
                    { "AZE", "Azerbaijan" },
                    { "BAH", "Bahamas" },
                    { "BAR", "Barbados" },
                    { "BEL", "Belgium" },
                    { "BER", "Bermuda" },
                    { "BIZ", "Belize" },
                    { "BLR", "Belarus" },
                    { "BOL", "Bolivia" },
                    { "BOT", "Botswana" },
                    { "BRA", "Brazil" },
                    { "BRN", "Bahrain" },
                    { "BRU", "Brunei" },
                    { "BUL", "Bulgaria" },
                    { "CAM", "Cambodia" },
                    { "CAN", "Canada" },
                    { "CAY", "Cayman Islands" },
                    { "CHI", "Chile" },
                    { "CHN", "China" },
                    { "COK", "Cook Islands" },
                    { "COL", "Colombia" },
                    { "CRO", "Croatia" },
                    { "CUB", "Cuba" },
                    { "CYP", "Cyprus" },
                    { "CZE", "Czech Republic" },
                    { "DEN", "Denmark" },
                    { "DJI", "Djibouti" },
                    { "DOM", "Dominican Republic" },
                    { "ECU", "Ecuador" },
                    { "EGY", "Egypt" },
                    { "ESA", "El Salvador" },
                    { "ESP", "Spain" },
                    { "EST", "Estonia" },
                    { "FIJ", "Fiji" },
                    { "FIN", "Finland" },
                    { "FRA", "France" },
                    { "GBR", "Great Britain" },
                    { "GEO", "Georgia" },
                    { "GER", "Germany" },
                    { "GRE", "Greece" },
                    { "GRN", "Grenada" },
                    { "GUA", "Guatemala" },
                    { "GUM", "Guam" },
                    { "HKG", "Hong Kong" },
                    { "HUN", "Hungary" },
                    { "INA", "Indonesia" },
                    { "IND", "India" },
                    { "IRI", "Iran" },
                    { "IRL", "Ireland" },
                    { "IRQ", "Iraq" },
                    { "ISL", "Iceland" },
                    { "ISR", "Israel" },
                    { "ISV", "U.S. Virgin Islands" },
                    { "ITA", "Italy" },
                    { "IVB", "British Virgin Islands" },
                    { "JAM", "Jamaica" },
                    { "JOR", "Jordan" },
                    { "JPN", "Japan" },
                    { "KAZ", "Kazakhstan" },
                    { "KEN", "Kenya" },
                    { "KGZ", "Kyrgyzstan" },
                    { "KOR", "South Korea" },
                    { "KOS", "Kosovo" },
                    { "KSA", "Saudi Arabia" },
                    { "KUW", "Kuwait" },
                    { "LAT", "Latvia" },
                    { "LBA", "Libya" },
                    { "LCA", "Saint Lucia" },
                    { "LIB", "Lebanon" },
                    { "LIE", "Liechtenstein" },
                    { "LTU", "Lithuania" },
                    { "LUX", "Luxembourg" },
                    { "MAC", "Macau" },
                    { "MAD", "Madagascar" },
                    { "MAR", "Morocco" },
                    { "MAS", "Malaysia" },
                    { "MDA", "Moldova" },
                    { "MEX", "Mexico" },
                    { "MKD", "North Macedonia" },
                    { "MLT", "Malta" },
                    { "MNE", "Montenegro" },
                    { "MNT", "Montserrat" },
                    { "MON", "Monaco" },
                    { "MOZ", "Mozambique" },
                    { "MRI", "Mauritius" },
                    { "MYA", "Myanmar" },
                    { "NAM", "Namibia" },
                    { "NCA", "Nicaragua" },
                    { "NED", "Netherlands" },
                    { "NGR", "Nigeria" },
                    { "NOR", "Norway" },
                    { "NZL", "New Zealand" },
                    { "OMA", "Oman" },
                    { "PAK", "Pakistan" },
                    { "PAN", "Panama" },
                    { "PAR", "Paraguay" },
                    { "PER", "Peru" },
                    { "PHI", "Philippines" },
                    { "PLE", "Palestine" },
                    { "PNG", "Papua New Guinea" },
                    { "POL", "Poland" },
                    { "POR", "Portugal" },
                    { "PRK", "North Korea" },
                    { "PUR", "Puerto Rico" },
                    { "QAT", "Qatar" },
                    { "ROU", "Romania" },
                    { "RSA", "South Africa" },
                    { "RUS", "Russia" },
                    { "SAM", "Samoa" },
                    { "SEN", "Senegal" },
                    { "SEY", "Seychelles" },
                    { "SGP", "Singapore" },
                    { "SKN", "Saint Kitts and Nevis" },
                    { "SLO", "Slovenia" },
                    { "SMR", "San Marino" },
                    { "SOL", "Solomon Islands" },
                    { "SRB", "Serbia" },
                    { "SRI", "Sri Lanka" },
                    { "SUD", "Sudan" },
                    { "SUI", "Switzerland" },
                    { "SVK", "Slovakia" },
                    { "SWE", "Sweden" },
                    { "TAH", "French Polynesia" },
                    { "TAN", "Tanzania" },
                    { "TCA", "Turks and Caicos Islands" },
                    { "TGA", "Tonga" },
                    { "THA", "Thailand" },
                    { "TJK", "Tajikistan" },
                    { "TLS", "Timor-Leste" },
                    { "TPE", "Chinese Taipei" },
                    { "TTO", "Trinidad and Tobago" },
                    { "TUN", "Tunisia" },
                    { "TUR", "Turkey" },
                    { "UAE", "United Arab Emirates" },
                    { "UGA", "Uganda" },
                    { "UKR", "Ukraine" },
                    { "URU", "Uruguay" },
                    { "USA", "United States" },
                    { "VAN", "Vanuatu" },
                    { "VEN", "Venezuela" },
                    { "VIE", "Vietnam" },
                    { "VIN", "Saint Vincent and the Grenadines" },
                    { "ZIM", "Zimbabwe" }
                });

            migrationBuilder.InsertData(
                table: "LinkType",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { "bluesky", "BlueSky Link" },
                    { "facebook", "Facebook Link" },
                    { "instagram", "Instagram Link" },
                    { "tracker", "Tracker Link" },
                    { "twitter", "X/Twitter Link" },
                    { "whatsapp", "WhatsApp Link" }
                });

            migrationBuilder.InsertData(
                table: "TargetTypes",
                columns: new[] { "IdPrefix", "Name" },
                values: new object[,]
                {
                    { "*", "*" },
                    { "G", "Regatta" },
                    { "O", "OrganizingAuthority" }
                });

            migrationBuilder.InsertData(
                table: "ActionHierarchies",
                columns: new[] { "ChildActionName", "ParentActionName" },
                values: new object[,]
                {
                    { "Del", "Admin" },
                    { "Edit", "Admin" },
                    { "View", "Admin" },
                    { "Edit", "Del" },
                    { "View", "Del" },
                    { "View", "Edit" },
                    { "Admin", "HMFIC" },
                    { "Del", "HMFIC" },
                    { "Edit", "HMFIC" },
                    { "View", "HMFIC" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_ActionHierarchies_ChildActionName",
                table: "ActionHierarchies",
                column: "ChildActionName");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_CreatedById",
                table: "AspNetUsers",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UpdatedById",
                table: "AspNetUsers",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Boat_CountryCodeId",
                table: "Boat",
                column: "CountryCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_Boat_CreatedById",
                table: "Boat",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Boat_OwnerId",
                table: "Boat",
                column: "OwnerId");

            migrationBuilder.CreateIndex(
                name: "IX_Boat_UpdatedById",
                table: "Boat",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Competitor_CreatedById",
                table: "Competitor",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Competitor_UpdatedById",
                table: "Competitor",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Competitor_UserId",
                table: "Competitor",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Files_CreatedById",
                table: "Files",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Files_UpdatedById",
                table: "Files",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizingAuthorities_CreatedById",
                table: "OrganizingAuthorities",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizingAuthorities_ImageId",
                table: "OrganizingAuthorities",
                column: "ImageId");

            migrationBuilder.CreateIndex(
                name: "IX_OrganizingAuthorities_UpdatedById",
                table: "OrganizingAuthorities",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Rating_BoatId",
                table: "Rating",
                column: "BoatId");

            migrationBuilder.CreateIndex(
                name: "IX_Rating_CreatedById",
                table: "Rating",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Rating_RatingTypeId",
                table: "Rating",
                column: "RatingTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Rating_UpdatedById",
                table: "Rating",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingType_CreatedById",
                table: "RatingType",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingType_UpdatedById",
                table: "RatingType",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValue_CreatedById",
                table: "RatingValue",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValue_RatingId",
                table: "RatingValue",
                column: "RatingId");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValue_UpdatedById",
                table: "RatingValue",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValue_ValueTypeId",
                table: "RatingValue",
                column: "ValueTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValueType_CreatedById",
                table: "RatingValueType",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValueType_RatingTypeId",
                table: "RatingValueType",
                column: "RatingTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RatingValueType_UpdatedById",
                table: "RatingValueType",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoatCompetitor_RegattaCompetitorId",
                table: "RegattaBoatCompetitor",
                column: "RegattaCompetitorId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_CountryCodeId",
                table: "RegattaBoats",
                column: "CountryCodeId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_CreatedById",
                table: "RegattaBoats",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_MasterBoatId",
                table: "RegattaBoats",
                column: "MasterBoatId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_RegattaClassId",
                table: "RegattaBoats",
                column: "RegattaClassId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_RegattaFleetId",
                table: "RegattaBoats",
                column: "RegattaFleetId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_RegattaId",
                table: "RegattaBoats",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_Skipper2Id",
                table: "RegattaBoats",
                column: "Skipper2Id");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_SkipperId",
                table: "RegattaBoats",
                column: "SkipperId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaBoats_UpdatedById",
                table: "RegattaBoats",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaClasses_CreatedById",
                table: "RegattaClasses",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaClasses_RegattaFleetId",
                table: "RegattaClasses",
                column: "RegattaFleetId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaClasses_RegattaId",
                table: "RegattaClasses",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaClasses_UpdatedById",
                table: "RegattaClasses",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaCompetitors_CompetitorId",
                table: "RegattaCompetitors",
                column: "CompetitorId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaCompetitors_CreatedById",
                table: "RegattaCompetitors",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaCompetitors_RegattaId",
                table: "RegattaCompetitors",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaCompetitors_UpdatedById",
                table: "RegattaCompetitors",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaExternalLinks_CreatedById",
                table: "RegattaExternalLinks",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaExternalLinks_LinkTypeId",
                table: "RegattaExternalLinks",
                column: "LinkTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaExternalLinks_RegattaId",
                table: "RegattaExternalLinks",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaExternalLinks_UpdatedById",
                table: "RegattaExternalLinks",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaFleets_CreatedById",
                table: "RegattaFleets",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaFleets_RegattaId",
                table: "RegattaFleets",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaFleets_UpdatedById",
                table: "RegattaFleets",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_CreatedById",
                table: "RegattaRatings",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_RatingTypeId",
                table: "RegattaRatings",
                column: "RatingTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_RegattaBoatId",
                table: "RegattaRatings",
                column: "RegattaBoatId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_RegattaId",
                table: "RegattaRatings",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_SourceRatingId",
                table: "RegattaRatings",
                column: "SourceRatingId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatings_UpdatedById",
                table: "RegattaRatings",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatingValues_CreatedById",
                table: "RegattaRatingValues",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatingValues_RegattaId",
                table: "RegattaRatingValues",
                column: "RegattaId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatingValues_RegattaRatingId",
                table: "RegattaRatingValues",
                column: "RegattaRatingId");

            migrationBuilder.CreateIndex(
                name: "IX_RegattaRatingValues_UpdatedById",
                table: "RegattaRatingValues",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Regattas_CreatedById",
                table: "Regattas",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Regattas_EventLogoId",
                table: "Regattas",
                column: "EventLogoId");

            migrationBuilder.CreateIndex(
                name: "IX_Regattas_OrganizingAuthorityId",
                table: "Regattas",
                column: "OrganizingAuthorityId");

            migrationBuilder.CreateIndex(
                name: "IX_Regattas_UpdatedById",
                table: "Regattas",
                column: "UpdatedById");

            migrationBuilder.CreateIndex(
                name: "IX_UserAuthActions_UserId",
                table: "UserAuthActions",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ActionHierarchies");

            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "RatingValue");

            migrationBuilder.DropTable(
                name: "RegattaBoatCompetitor");

            migrationBuilder.DropTable(
                name: "RegattaExternalLinks");

            migrationBuilder.DropTable(
                name: "RegattaRatingValues");

            migrationBuilder.DropTable(
                name: "TargetTypes");

            migrationBuilder.DropTable(
                name: "UserAuthActions");

            migrationBuilder.DropTable(
                name: "AspNetRoles");

            migrationBuilder.DropTable(
                name: "RatingValueType");

            migrationBuilder.DropTable(
                name: "LinkType");

            migrationBuilder.DropTable(
                name: "RegattaRatings");

            migrationBuilder.DropTable(
                name: "AuthActions");

            migrationBuilder.DropTable(
                name: "Rating");

            migrationBuilder.DropTable(
                name: "RegattaBoats");

            migrationBuilder.DropTable(
                name: "RatingType");

            migrationBuilder.DropTable(
                name: "Boat");

            migrationBuilder.DropTable(
                name: "RegattaClasses");

            migrationBuilder.DropTable(
                name: "RegattaCompetitors");

            migrationBuilder.DropTable(
                name: "CountryCode");

            migrationBuilder.DropTable(
                name: "RegattaFleets");

            migrationBuilder.DropTable(
                name: "Competitor");

            migrationBuilder.DropTable(
                name: "Regattas");

            migrationBuilder.DropTable(
                name: "OrganizingAuthorities");

            migrationBuilder.DropTable(
                name: "Files");

            migrationBuilder.DropTable(
                name: "AspNetUsers");
        }
    }
}
