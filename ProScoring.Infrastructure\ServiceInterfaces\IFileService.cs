﻿using Microsoft.AspNetCore.Http;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;

namespace ProScoring.Infrastructure.ServiceInterfaces;

public interface IFileService
{
    Task<string> DownloadAsDataUriAsync(string id);
    Task<FileDownloadResult> DownloadAsync(string id);
    Task<FileRecord> GetFileRecordAsync(string id);
    Task<FileUploadResult> UploadAsync(IFormFile file, string note);
    Task<FileUploadResult> UploadFromDataUriAsync(string fileName, string note, string dataUri);
}
