using ProScoring.Infrastructure.Database;

namespace ProScoring.Tests.Infrastructure;

public class DbMigrationCreationTests
{
    [Fact]
    public void MigrationDbContexts_Match_ApplicationDbContext()
    {
        // This test reads the properties of the ApplicationDbContext and confirms that
        // PostgreSqlCreationApplicationDbContext and SqliteCreationApplicationDbContext
        // have the same properties.
        // Arrange
        var applicationDbContextProperties = typeof(ApplicationDbContext).GetProperties();
        var postgreSqlDbContextProperties = typeof(PostgreSqlCreationApplicationDbContext).GetProperties();
        var sqliteDbContextProperties = typeof(SqliteCreationApplicationDbContext).GetProperties();

        // Act
        var applicationDbContextPropertyNames = applicationDbContextProperties
            .Select(p => p.Name)
            .OrderBy(n => n)
            .ToList();
        var postgreSqlDbContextPropertyNames = postgreSqlDbContextProperties
            .Select(p => p.Name)
            .OrderBy(n => n)
            .ToList();
        var sqliteDbContextPropertyNames = sqliteDbContextProperties.Select(p => p.Name).OrderBy(n => n).ToList();

        // Assert
        Assert.Equal(applicationDbContextPropertyNames, postgreSqlDbContextPropertyNames);
        Assert.Equal(applicationDbContextPropertyNames, sqliteDbContextPropertyNames);
    }
}
