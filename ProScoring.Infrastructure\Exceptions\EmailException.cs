namespace ProScoring.Infrastructure.Exceptions;

/// <summary>
/// Exception thrown when email operations fail.
/// </summary>
public class EmailException : InvalidOperationException
{
    /// <summary>
    /// Initializes a new instance of the <see cref="EmailException"/> class.
    /// </summary>
    public EmailException() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="EmailException"/> class with a specified error message.
    /// </summary>
    /// <param name="message">The message that describes the error.</param>
    public EmailException(string message)
        : base(message) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="EmailException"/> class with a specified error message
    /// and a reference to the inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="message">The message that describes the error.</param>
    /// <param name="inner">The exception that is the cause of the current exception.</param>
    public EmailException(string message, Exception inner)
        : base(message, inner) { }
}
