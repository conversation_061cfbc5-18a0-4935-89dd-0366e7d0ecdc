using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents a specific rating value for a RegattaRating.
/// </summary>
public class RegattaRatingValue : RegattaEntityBase<RegattaRatingValue>, IHasForeignKeyConfiguration<RegattaRatingValue>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaRatingValue IDs.
    /// </summary>
    public const string ID_PREFIX = "RRV";

    #endregion Constants

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaRatingValue.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the ID of the regatta rating this value belongs to.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [ForeignKey(nameof(RegattaRating))]
    public required string RegattaRatingId { get; set; }

    /// <summary>
    /// Gets or sets the name of the rating value.
    /// </summary>
    [Required]
    [Column(Order = 30)]
    [StringLength(50)]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the value of the rating.
    /// </summary>
    [Required]
    [Column(Order = 40)]
    [Precision(10, 4)]
    public decimal Value { get; set; }

    /// <summary>
    /// Gets or sets notes about this rating value.
    /// </summary>
    [Column(Order = 50)]
    [StringLength(500)]
    public string? Note { get; set; }

    #endregion Properties

    #region Navigation Properties

    /// <summary>
    /// Gets or sets the regatta rating this value belongs to.
    /// </summary>
    public virtual RegattaRating RegattaRating { get; set; } = null!;

    #endregion Navigation Properties

    #region DB Configuration

    /// <summary>
    /// Configures foreign key relationships for the RegattaRatingValue entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the RegattaRatingValue entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaRatingValue> entity)
    {
        entity
            .HasOne(rrv => rrv.RegattaRating)
            .WithMany(rr => rr.RegattaRatingValues)
            .HasForeignKey(rrv => rrv.RegattaRatingId)
            .OnDelete(DeleteBehavior.Cascade);
    }

    #endregion DB Configuration
}
