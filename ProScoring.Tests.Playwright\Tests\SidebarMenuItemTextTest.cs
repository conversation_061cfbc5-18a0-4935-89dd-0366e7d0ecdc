using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Test to verify the GetSidebarMenuItemTexts method returns the correct text
/// </summary>
public class SidebarMenuItemTextTest : IClassFixture<PlaywrightFixture>
{
    #region Fields
    private readonly PlaywrightFixture _fixture;
    #endregion Fields

    #region Constructors
    public SidebarMenuItemTextTest(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion Constructors

    #region Test Methods
    /// <summary>
    /// Tests that the GetSidebarMenuItemTexts method returns the correct text.
    /// </summary>
    [Fact]
    public async Task GetSidebarMenuItemTexts_Returns_Correct_Text()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Navigate to the home page
            await page.GotoAsync(_fixture.Settings.BaseUrl);

            // Wait for the sidebar to be visible
            await page.WaitForSelectorAsync("[data-testid=main-sidebar]");

            // Create a BasePageWithMainLayout instance
            var testPage = new BasePageWithMainLayout(page);

            // Make sure sidebar is visible
            var sidebar = await page.QuerySelectorAsync("[data-testid=main-sidebar]");
            if (sidebar == null)
                throw new Exception("Sidebar not found");

            var sidebarClass = await sidebar.GetAttributeAsync("class");
            if (sidebarClass == null)
                throw new Exception("Sidebar class not found");

            if (sidebarClass == null || !sidebarClass.Contains("rz-sidebar-expanded"))
            {
                // Click the sidebar toggle to expand it
                await testPage.ClickSidebarToggle();
                await Task.Delay(500); // Wait for animation
            }

            // Act - Get menu items using our fixed method
            var menuItemTexts = await testPage.GetSidebarMenuItemTexts();

            // Print the menu item texts to the console
            Console.WriteLine("Menu Item Texts:");
            foreach (var text in menuItemTexts)
            {
                Console.WriteLine($"  - {text}");
            }

            // Assert - Should have expected menu items without icon text
            menuItemTexts.Should().Contain("Home", "Sidebar should contain Home menu item");
            menuItemTexts.Should().Contain("About", "Sidebar should contain About menu item");
            menuItemTexts.Should().Contain("Contact", "Sidebar should contain Contact menu item");
            menuItemTexts.Should().Contain("Settings", "Sidebar should contain Settings menu item");
            menuItemTexts.Should().Contain("Logout", "Sidebar should contain Logout menu item");

            // Verify that none of the menu items contain the icon text
            foreach (var text in menuItemTexts)
            {
                text.Should().NotContain("home", "Menu item text should not contain icon text");
                text.Should().NotContain("info", "Menu item text should not contain icon text");
                text.Should().NotContain("contact_phone", "Menu item text should not contain icon text");
                text.Should().NotContain("settings", "Menu item text should not contain icon text");
                text.Should().NotContain("logout", "Menu item text should not contain icon text");
            }
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Test Methods
}
