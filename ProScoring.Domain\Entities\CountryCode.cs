using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a country code using the Olympic country code standard.
/// Contains the three-letter code and full country name.
/// </summary>
public class CountryCode : IHasInitialSeedData<CountryCode>
{
    #region Properties

    /// <summary>
    /// Gets or sets the three-letter Olympic country code.
    /// </summary>
    [Key]
    [Column(Order = 10)]
    [StringLength(3, MinimumLength = 3)]
    [Required]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the full name of the country.
    /// </summary>
    [Column(Order = 20)]
    [StringLength(50)]
    [Required]
    public string Name { get; set; } = string.Empty;

    #endregion Properties

    #region SeedData

    /// <summary>
    /// Gets the initial seed data for populating the CountryCode database table.
    /// </summary>
    public static CountryCode[] SeedData =>
        [
            new() { Code = "AHO", Name = "Netherlands Antilles" },
            new() { Code = "ALG", Name = "Algeria" },
            new() { Code = "AND", Name = "Andorra" },
            new() { Code = "ANG", Name = "Angola" },
            new() { Code = "ANT", Name = "Antigua and Barbuda" },
            new() { Code = "ARG", Name = "Argentina" },
            new() { Code = "ARM", Name = "Armenia" },
            new() { Code = "ARU", Name = "Aruba" },
            new() { Code = "ASA", Name = "American Samoa" },
            new() { Code = "AUS", Name = "Australia" },
            new() { Code = "AUT", Name = "Austria" },
            new() { Code = "AZE", Name = "Azerbaijan" },
            new() { Code = "BAH", Name = "Bahamas" },
            new() { Code = "BAR", Name = "Barbados" },
            new() { Code = "BEL", Name = "Belgium" },
            new() { Code = "BER", Name = "Bermuda" },
            new() { Code = "BIZ", Name = "Belize" },
            new() { Code = "BLR", Name = "Belarus" },
            new() { Code = "BOL", Name = "Bolivia" },
            new() { Code = "BOT", Name = "Botswana" },
            new() { Code = "BRA", Name = "Brazil" },
            new() { Code = "BRN", Name = "Bahrain" },
            new() { Code = "BRU", Name = "Brunei" },
            new() { Code = "BUL", Name = "Bulgaria" },
            new() { Code = "CAM", Name = "Cambodia" },
            new() { Code = "CAN", Name = "Canada" },
            new() { Code = "CAY", Name = "Cayman Islands" },
            new() { Code = "CHI", Name = "Chile" },
            new() { Code = "CHN", Name = "China" },
            new() { Code = "COK", Name = "Cook Islands" },
            new() { Code = "COL", Name = "Colombia" },
            new() { Code = "CRO", Name = "Croatia" },
            new() { Code = "CUB", Name = "Cuba" },
            new() { Code = "CYP", Name = "Cyprus" },
            new() { Code = "CZE", Name = "Czech Republic" },
            new() { Code = "DEN", Name = "Denmark" },
            new() { Code = "DJI", Name = "Djibouti" },
            new() { Code = "DOM", Name = "Dominican Republic" },
            new() { Code = "ECU", Name = "Ecuador" },
            new() { Code = "EGY", Name = "Egypt" },
            new() { Code = "ESA", Name = "El Salvador" },
            new() { Code = "ESP", Name = "Spain" },
            new() { Code = "EST", Name = "Estonia" },
            new() { Code = "FIJ", Name = "Fiji" },
            new() { Code = "FIN", Name = "Finland" },
            new() { Code = "FRA", Name = "France" },
            new() { Code = "GBR", Name = "Great Britain" },
            new() { Code = "GEO", Name = "Georgia" },
            new() { Code = "GER", Name = "Germany" },
            new() { Code = "GRE", Name = "Greece" },
            new() { Code = "GRN", Name = "Grenada" },
            new() { Code = "GUA", Name = "Guatemala" },
            new() { Code = "GUM", Name = "Guam" },
            new() { Code = "HKG", Name = "Hong Kong" },
            new() { Code = "HUN", Name = "Hungary" },
            new() { Code = "INA", Name = "Indonesia" },
            new() { Code = "IND", Name = "India" },
            new() { Code = "IRI", Name = "Iran" },
            new() { Code = "IRL", Name = "Ireland" },
            new() { Code = "IRQ", Name = "Iraq" },
            new() { Code = "ISL", Name = "Iceland" },
            new() { Code = "ISR", Name = "Israel" },
            new() { Code = "ISV", Name = "U.S. Virgin Islands" },
            new() { Code = "ITA", Name = "Italy" },
            new() { Code = "IVB", Name = "British Virgin Islands" },
            new() { Code = "JAM", Name = "Jamaica" },
            new() { Code = "JOR", Name = "Jordan" },
            new() { Code = "JPN", Name = "Japan" },
            new() { Code = "KAZ", Name = "Kazakhstan" },
            new() { Code = "KEN", Name = "Kenya" },
            new() { Code = "KGZ", Name = "Kyrgyzstan" },
            new() { Code = "KOR", Name = "South Korea" },
            new() { Code = "KOS", Name = "Kosovo" },
            new() { Code = "KSA", Name = "Saudi Arabia" },
            new() { Code = "KUW", Name = "Kuwait" },
            new() { Code = "LAT", Name = "Latvia" },
            new() { Code = "LBA", Name = "Libya" },
            new() { Code = "LCA", Name = "Saint Lucia" },
            new() { Code = "LIB", Name = "Lebanon" },
            new() { Code = "LIE", Name = "Liechtenstein" },
            new() { Code = "LTU", Name = "Lithuania" },
            new() { Code = "LUX", Name = "Luxembourg" },
            new() { Code = "MAC", Name = "Macau" },
            new() { Code = "MAD", Name = "Madagascar" },
            new() { Code = "MAR", Name = "Morocco" },
            new() { Code = "MAS", Name = "Malaysia" },
            new() { Code = "MDA", Name = "Moldova" },
            new() { Code = "MEX", Name = "Mexico" },
            new() { Code = "MKD", Name = "North Macedonia" },
            new() { Code = "MLT", Name = "Malta" },
            new() { Code = "MNE", Name = "Montenegro" },
            new() { Code = "MNT", Name = "Montserrat" },
            new() { Code = "MON", Name = "Monaco" },
            new() { Code = "MOZ", Name = "Mozambique" },
            new() { Code = "MRI", Name = "Mauritius" },
            new() { Code = "MYA", Name = "Myanmar" },
            new() { Code = "NAM", Name = "Namibia" },
            new() { Code = "NCA", Name = "Nicaragua" },
            new() { Code = "NED", Name = "Netherlands" },
            new() { Code = "NGR", Name = "Nigeria" },
            new() { Code = "NOR", Name = "Norway" },
            new() { Code = "NZL", Name = "New Zealand" },
            new() { Code = "OMA", Name = "Oman" },
            new() { Code = "PAK", Name = "Pakistan" },
            new() { Code = "PAN", Name = "Panama" },
            new() { Code = "PAR", Name = "Paraguay" },
            new() { Code = "PER", Name = "Peru" },
            new() { Code = "PHI", Name = "Philippines" },
            new() { Code = "PLE", Name = "Palestine" },
            new() { Code = "PNG", Name = "Papua New Guinea" },
            new() { Code = "POL", Name = "Poland" },
            new() { Code = "POR", Name = "Portugal" },
            new() { Code = "PRK", Name = "North Korea" },
            new() { Code = "PUR", Name = "Puerto Rico" },
            new() { Code = "QAT", Name = "Qatar" },
            new() { Code = "ROU", Name = "Romania" },
            new() { Code = "RSA", Name = "South Africa" },
            new() { Code = "RUS", Name = "Russia" },
            new() { Code = "SAM", Name = "Samoa" },
            new() { Code = "SEN", Name = "Senegal" },
            new() { Code = "SEY", Name = "Seychelles" },
            new() { Code = "SGP", Name = "Singapore" },
            new() { Code = "SKN", Name = "Saint Kitts and Nevis" },
            new() { Code = "SLO", Name = "Slovenia" },
            new() { Code = "SMR", Name = "San Marino" },
            new() { Code = "SOL", Name = "Solomon Islands" },
            new() { Code = "SRB", Name = "Serbia" },
            new() { Code = "SRI", Name = "Sri Lanka" },
            new() { Code = "SUD", Name = "Sudan" },
            new() { Code = "SUI", Name = "Switzerland" },
            new() { Code = "SVK", Name = "Slovakia" },
            new() { Code = "SWE", Name = "Sweden" },
            new() { Code = "TAH", Name = "French Polynesia" },
            new() { Code = "TAN", Name = "Tanzania" },
            new() { Code = "TCA", Name = "Turks and Caicos Islands" },
            new() { Code = "TGA", Name = "Tonga" },
            new() { Code = "THA", Name = "Thailand" },
            new() { Code = "TJK", Name = "Tajikistan" },
            new() { Code = "TLS", Name = "Timor-Leste" },
            new() { Code = "TPE", Name = "Chinese Taipei" },
            new() { Code = "TTO", Name = "Trinidad and Tobago" },
            new() { Code = "TUN", Name = "Tunisia" },
            new() { Code = "TUR", Name = "Turkey" },
            new() { Code = "UAE", Name = "United Arab Emirates" },
            new() { Code = "UGA", Name = "Uganda" },
            new() { Code = "UKR", Name = "Ukraine" },
            new() { Code = "URU", Name = "Uruguay" },
            new() { Code = "USA", Name = "United States" },
            new() { Code = "VAN", Name = "Vanuatu" },
            new() { Code = "VEN", Name = "Venezuela" },
            new() { Code = "VIE", Name = "Vietnam" },
            new() { Code = "VIN", Name = "Saint Vincent and the Grenadines" },
            new() { Code = "ZIM", Name = "Zimbabwe" },
        ];

    #endregion SeedData
}
