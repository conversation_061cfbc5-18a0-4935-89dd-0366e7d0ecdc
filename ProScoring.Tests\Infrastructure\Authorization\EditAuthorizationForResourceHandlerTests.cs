using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using NSubstitute;
using ProScoring.Domain.Entities.EntityInterfaces; // For IHasId
using ProScoring.Infrastructure.Authorization; // For EditAuthorizationForResourceHandler, Requirement, and IAuthorizationProvider
using Xunit;
using System.Collections.Generic; // For IEnumerable

namespace ProScoring.Tests.Infrastructure.Authorization
{
    public class EditAuthorizationForResourceHandlerTests
    {
        private readonly EditAuthorizationForResourceHandler _handler;
        private readonly IAuthorizationProvider _mockAuthProvider;
        private readonly EditAuthorizationForResourceHandler.Requirement _requirement;

        public EditAuthorizationForResourceHandlerTests()
        {
            _mockAuthProvider = Substitute.For<IAuthorizationProvider>();
            _handler = new EditAuthorizationForResourceHandler(_mockAuthProvider);
            _requirement = new EditAuthorizationForResourceHandler.Requirement();
        }

        private ClaimsPrincipal CreateUser(bool isAuthenticated, IEnumerable<Claim>? claims = null)
        {
            var identity = new ClaimsIdentity(claims, isAuthenticated ? "TestAuthType" : null);
            return new ClaimsPrincipal(identity);
        }

        private AuthorizationHandlerContext CreateContext(ClaimsPrincipal user, object? resource = null)
        {
            return new AuthorizationHandlerContext(
                new[] { _requirement },
                user,
                resource);
        }

        [Fact]
        public async Task HandleRequirementAsync_UserNotAuthenticated_DoesNotSucceed()
        {
            // Arrange
            var user = CreateUser(false); // Not authenticated
            var resource = Substitute.For<IHasId>();
            resource.Id.Returns("test-id");
            var context = CreateContext(user, resource);

            // Act
            await _handler.HandleAsync(context);

            // Assert
            context.HasSucceeded.Should().BeFalse();
            await _mockAuthProvider.DidNotReceive().IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task HandleRequirementAsync_NullResource_FailsRequirement()
        {
            // Arrange
            var user = CreateUser(true); // Authenticated
            var context = CreateContext(user, null); // Null resource

            // Act
            await _handler.HandleAsync(context);

            // Assert
            context.HasSucceeded.Should().BeFalse();
            if (context.HasFailed)
            {
                context.FailureReasons.Should().Contain(reason => reason.Message.Contains("Resource is null or has no ID."));
            }
            await _mockAuthProvider.DidNotReceive().IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task HandleRequirementAsync_ResourceWithNullId_FailsRequirement()
        {
            // Arrange
            var user = CreateUser(true); // Authenticated
            var resource = Substitute.For<IHasId>();
            resource.Id.Returns((string?)null); // Null ID
            var context = CreateContext(user, resource);

            // Act
            await _handler.HandleAsync(context);

            // Assert
            context.HasSucceeded.Should().BeFalse();
            if (context.HasFailed)
            {
                context.FailureReasons.Should().Contain(reason => reason.Message.Contains("Resource is null or has no ID."));
            }
            await _mockAuthProvider.DidNotReceive().IsAuthorizedAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task HandleRequirementAsync_ProviderReturnsTrue_SucceedsRequirement()
        {
            // Arrange
            var user = CreateUser(true); // Authenticated
            var resource = Substitute.For<IHasId>();
            var resourceId = "valid-id";
            resource.Id.Returns(resourceId);
            var context = CreateContext(user, resource);

            _mockAuthProvider.IsAuthorizedAsync(user, resourceId, AuthTypes.Actions.EDIT).Returns(Task.FromResult(true));

            // Act
            await _handler.HandleAsync(context);

            // Assert
            context.HasSucceeded.Should().BeTrue();
            await _mockAuthProvider.Received(1).IsAuthorizedAsync(user, resourceId, AuthTypes.Actions.EDIT);
        }

        [Fact]
        public async Task HandleRequirementAsync_ProviderReturnsFalse_DoesNotSucceedRequirement()
        {
            // Arrange
            var user = CreateUser(true); // Authenticated
            var resource = Substitute.For<IHasId>();
            var resourceId = "valid-id";
            resource.Id.Returns(resourceId);
            var context = CreateContext(user, resource);

            _mockAuthProvider.IsAuthorizedAsync(user, resourceId, AuthTypes.Actions.EDIT).Returns(Task.FromResult(false));

            // Act
            await _handler.HandleAsync(context);

            // Assert
            context.HasSucceeded.Should().BeFalse();
            await _mockAuthProvider.Received(1).IsAuthorizedAsync(user, resourceId, AuthTypes.Actions.EDIT);
        }
    }
}
