using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Proscoring.TestData.Playwright.DataLoaders;
using Proscoring.TestData.Playwright.Helpers;
using Proscoring.TestData.Playwright.Operations;
using Serilog;

namespace Proscoring.TestData.Playwright;

/// <summary>
/// Entry point for the Playwright-based test data application.
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point for the application.
    /// </summary>
    /// <param name="args">Command line arguments.</param>
    public static async Task Main(string[] args)
    {
        var stopwatch = Stopwatch.StartNew();

        // Setup initial bootstrap logger
        Log.Logger = new LoggerConfiguration().MinimumLevel.Debug().WriteTo.Console().CreateLogger();

        try
        {
            // Setup configuration
            var configuration = BuildConfiguration();

            // Configure proper Serilog with full configuration
            ConfigureSerilog(configuration);

            // Process command line arguments
            var operationFlags = ParseCommandLineArguments(args);

            // Now it's safe to start logging
            LogApplicationStart(args, operationFlags, stopwatch);

            // Setup DI
            var serviceProvider = ConfigureServices(configuration);
            var logger = serviceProvider.GetRequiredService<ILoggerFactory>().CreateLogger<Program>();

            logger.LogInformation(
                "Playwright test automation initialized in {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );

            // Install Playwright browsers if needed
            if (!InstallPlaywrightBrowsersIfNeeded(logger, stopwatch))
            {
                return;
            }

            // Execute the test operations
            await ExecuteTestOperationsAsync(serviceProvider, operationFlags, logger, stopwatch);
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Application terminated unexpectedly");
        }
        finally
        {
            // Ensure log buffer is flushed
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// Builds the application configuration.
    /// </summary>
    /// <returns>The configured IConfiguration instance.</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();
    }

    /// <summary>
    /// Configures Serilog with the provided configuration.
    /// </summary>
    /// <param name="configuration">The application configuration.</param>
    private static void ConfigureSerilog(IConfiguration configuration)
    {
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .WriteTo.File("logs/log.txt")
            .CreateLogger();
    }

    /// <summary>
    /// Parses command line arguments and returns operation flags.
    /// </summary>
    /// <param name="args">Command line arguments.</param>
    /// <returns>A dictionary of operation flags.</returns>
    private static Dictionary<string, bool> ParseCommandLineArguments(string[] args)
    {
        var operationFlags = new Dictionary<string, bool>
        {
            ["generateUsers"] = true,
            ["generateOrganizingAuthorities"] = true,
        };

        // Override defaults based on arguments
        if (args.Length > 0)
        {
            // Reset defaults if arguments are provided
            operationFlags["generateUsers"] = false;
            operationFlags["generateOrganizingAuthorities"] = false;

            foreach (var arg in args)
            {
                if (arg.Equals("--users", StringComparison.OrdinalIgnoreCase))
                {
                    operationFlags["generateUsers"] = true;
                }
                else if (arg.Equals("--authorities", StringComparison.OrdinalIgnoreCase))
                {
                    operationFlags["generateOrganizingAuthorities"] = true;
                }
            }
        }

        return operationFlags;
    }

    /// <summary>
    /// Logs application startup information.
    /// </summary>
    /// <param="args">Command line arguments.</param>
    /// <param="operationFlags">Dictionary of operation flags.</param>
    /// <param="stopwatch">Stopwatch for timing operations.</param>
    private static void LogApplicationStart(string[] args, Dictionary<string, bool> operationFlags, Stopwatch stopwatch)
    {
        Log.Information("=============================================");
        Log.Information("*********************************************");
        Log.Information("=============================================");
        Log.Information("Starting Playwright test automation with args: {@Args}", args);
        Log.Information(
            "Operation flags - Generate Users: {GenerateUsers}, Generate Authorities: {GenerateAuthorities}",
            operationFlags["generateUsers"],
            operationFlags["generateOrganizingAuthorities"]
        );
        Log.Debug("Configuration loaded successfully in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        Log.Debug("Serilog configured successfully");
    }

    /// <summary>
    /// Configures the dependency injection container.
    /// </summary>
    /// <param="configuration">The application configuration.</param>
    /// <returns>The configured service provider.</returns>
    private static ServiceProvider ConfigureServices(IConfiguration configuration)
    {
        Log.Debug("Setting up dependency injection container");
        var stopwatch = Stopwatch.StartNew();

        var serviceProvider = new ServiceCollection()
            .AddSingleton<IConfiguration>(configuration)
            .AddLogging(configure => configure.AddSerilog())
            .AddSingleton<AspireAppHelper>()
            .AddSingleton<JsonDataLoader>()
            .AddSingleton<UserRegistration>()
            .AddSingleton<OrganizingAuthorityCreation>()
            .BuildServiceProvider();

        Log.Debug("Dependency injection container built in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        return serviceProvider;
    }

    /// <summary>
    /// Installs Playwright browsers if needed.
    /// </summary>
    /// <param="logger">Logger instance.</param>
    /// <param="stopwatch">Stopwatch for timing operations.</param>
    /// <returns>True if installation was successful, otherwise false.</returns>
    private static bool InstallPlaywrightBrowsersIfNeeded(ILogger<Program> logger, Stopwatch stopwatch)
    {
        logger.LogInformation("Installing Playwright browsers if needed");
        stopwatch.Restart();

        var exitCode = Microsoft.Playwright.Program.Main(new[] { "install" });

        logger.LogDebug(
            "Playwright browser installation completed in {ElapsedMs}ms with exit code: {ExitCode}",
            stopwatch.ElapsedMilliseconds,
            exitCode
        );

        if (exitCode != 0)
        {
            logger.LogError("Failed to install Playwright browsers, exit code: {ExitCode}", exitCode);
            return false;
        }

        return true;
    }

    /// <summary>
    /// Executes the test operations based on the provided flags.
    /// </summary>
    /// <param="serviceProvider">The service provider.</param>
    /// <param="operationFlags">Dictionary of operation flags.</param>
    /// <param="logger">Logger instance.</param>
    /// <param="stopwatch">Stopwatch for timing operations.</param>
    private static async Task ExecuteTestOperationsAsync(
        ServiceProvider serviceProvider,
        Dictionary<string, bool> operationFlags,
        ILogger<Program> logger,
        Stopwatch stopwatch
    )
    {
        // Get the necessary services
        logger.LogDebug("Retrieving required services from DI container");
        var aspireHelper = serviceProvider.GetRequiredService<AspireAppHelper>();
        var userRegistration = serviceProvider.GetRequiredService<UserRegistration>();
        var organizingAuthorityCreation = serviceProvider.GetRequiredService<OrganizingAuthorityCreation>();
        logger.LogDebug("Services retrieved successfully");

        try
        {
            // Start Aspire app
            logger.LogInformation("Starting Aspire application");
            stopwatch.Restart();
            await aspireHelper.StartAspireAppAsync();
            logger.LogInformation(
                "Aspire application started successfully in {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds
            );

            // Execute user registration operation if enabled
            await ExecuteOperationIfEnabledAsync(
                operationFlags["generateUsers"],
                "user registration",
                async () => await userRegistration.RegisterUsersFromJsonAsync(),
                logger,
                stopwatch
            );

            // Execute organizing authority creation if enabled
            await ExecuteOperationIfEnabledAsync(
                operationFlags["generateOrganizingAuthorities"],
                "organizing authority creation",
                async () => await organizingAuthorityCreation.CreateOrganizingAuthoritiesAsync(),
                logger,
                stopwatch
            );

            logger.LogInformation("Test automation completed successfully");
        }
        catch (Exception ex)
        {
            LogOperationError(ex, logger);
        }
        finally
        {
            // Cleanup
            await CleanupAsync(aspireHelper, logger, stopwatch);
        }
    }

    /// <summary>
    /// Executes an operation if it is enabled by the corresponding flag.
    /// </summary>
    /// <param="isEnabled">Whether the operation is enabled.</param>
    /// <param="operationName">Name of the operation for logging.</param>
    /// <param="operation">The async operation to execute.</param>
    /// <param="logger">Logger instance.</param>
    /// <param="stopwatch">Stopwatch for timing operations.</param>
    private static async Task ExecuteOperationIfEnabledAsync(
        bool isEnabled,
        string operationName,
        Func<Task> operation,
        ILogger<Program> logger,
        Stopwatch stopwatch
    )
    {
        if (isEnabled)
        {
            logger.LogInformation($"Beginning {operationName} process");
            stopwatch.Restart();
            await operation();
            logger.LogInformation(
                $"{char.ToUpper(operationName[0])}{operationName.Substring(1)} completed successfully in {{ElapsedMs}}ms",
                stopwatch.ElapsedMilliseconds
            );
        }
        else
        {
            logger.LogInformation($"Skipping {operationName} based on command line arguments");
        }
    }

    /// <summary>
    /// Logs detailed information about an operation error.
    /// </summary>
    /// <param="ex">The exception that occurred.</param>
    /// <param="logger">Logger instance.</param>
    private static void LogOperationError(Exception ex, ILogger<Program> logger)
    {
        logger.LogError(ex, "An error occurred during test automation: {ErrorMessage}", ex.Message);

        if (ex.InnerException != null)
        {
            logger.LogDebug("Inner exception: {InnerException}", ex.InnerException.Message);
        }

        // Additional error context if available
        if (ex.StackTrace != null)
        {
            logger.LogDebug("Stack trace: {StackTrace}", ex.StackTrace);
        }
    }

    /// <summary>
    /// Performs cleanup operations before application exit.
    /// </summary>
    /// <param="aspireHelper">The AspireAppHelper instance.</param>
    /// <param="logger">Logger instance.</param>
    /// <param="stopwatch">Stopwatch for timing operations.</param>
    private static async Task CleanupAsync(AspireAppHelper aspireHelper, ILogger<Program> logger, Stopwatch stopwatch)
    {
        logger.LogInformation("Performing cleanup operations");
        stopwatch.Restart();
        await aspireHelper.ShutdownAsync();
        logger.LogDebug("Cleanup completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

        stopwatch.Stop();
        logger.LogInformation(
            "Application exiting. Total runtime: {TotalSeconds:N1} seconds",
            stopwatch.Elapsed.TotalSeconds
        );
    }
}
