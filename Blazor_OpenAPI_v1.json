{"openapi": "3.0.1", "info": {"title": "ProScoring.Blazor | v1", "version": "1.0.0"}, "servers": [{"url": "https://localhost:7292"}, {"url": "http://localhost:5103"}], "paths": {"/Account/PerformExternalLogin": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["provider", "returnUrl"], "type": "object", "allOf": [{"type": "object", "properties": {"provider": {"type": "string"}}}, {"type": "object", "properties": {"returnUrl": {"type": "string"}}}]}}, "application/x-www-form-urlencoded": {"schema": {"required": ["provider", "returnUrl"], "type": "object", "allOf": [{"type": "object", "properties": {"provider": {"type": "string"}}}, {"type": "object", "properties": {"returnUrl": {"type": "string"}}}]}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/Account/Logout": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["returnUrl"], "type": "object", "properties": {"returnUrl": {"type": "string"}}}}, "application/x-www-form-urlencoded": {"schema": {"required": ["returnUrl"], "type": "object", "properties": {"returnUrl": {"type": "string"}}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/Account/Manage/LinkExternalLogin": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["provider"], "type": "object", "properties": {"provider": {"type": "string"}}}}, "application/x-www-form-urlencoded": {"schema": {"required": ["provider"], "type": "object", "properties": {"provider": {"type": "string"}}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/Account/Manage/DownloadPersonalData": {"post": {"tags": ["ProScoring.Blazor"], "responses": {"200": {"description": "OK"}}}}, "/register": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}}}}, "/login": {"post": {"tags": ["ProScoring.Blazor"], "parameters": [{"name": "useCookies", "in": "query", "schema": {"type": "boolean"}}, {"name": "useSessionCookies", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}}}}, "/refresh": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessTokenResponse"}}}}}}}, "/confirmEmail": {"get": {"tags": ["ProScoring.Blazor"], "operationId": "MapIdentityApi-/confirmEmail", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "changedEmail", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/resendConfirmationEmail": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendConfirmationEmailRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/forgotPassword": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}}}}, "/resetPassword": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}}}}, "/manage/2fa": {"post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}, "404": {"description": "Not Found"}}}}, "/manage/info": {"get": {"tags": ["ProScoring.Blazor"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}, "404": {"description": "Not Found"}}}, "post": {"tags": ["ProScoring.Blazor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfoRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfoResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/problem+json": {"schema": {"$ref": "#/components/schemas/HttpValidationProblemDetails"}}}}, "404": {"description": "Not Found"}}}}, "/api/File/helloworld": {"get": {"tags": ["File"], "responses": {"200": {"description": "OK"}}}}, "/api/File/upload": {"post": {"tags": ["File"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "allOf": [{"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, {"type": "object", "properties": {"description": {"type": "string"}}}]}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/File/download/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AccessTokenResponse": {"required": ["accessToken", "expiresIn", "refreshToken"], "type": "object", "properties": {"tokenType": {"type": "string", "nullable": true}, "accessToken": {"type": "string"}, "expiresIn": {"type": "integer", "format": "int64"}, "refreshToken": {"type": "string"}}}, "ForgotPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}}, "HttpValidationProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}, "InfoRequest": {"type": "object", "properties": {"newEmail": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "oldPassword": {"type": "string", "nullable": true}}}, "InfoResponse": {"required": ["email", "isEmailConfirmed"], "type": "object", "properties": {"email": {"type": "string"}, "isEmailConfirmed": {"type": "boolean"}}}, "LoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}, "twoFactorCode": {"type": "string", "nullable": true}, "twoFactorRecoveryCode": {"type": "string", "nullable": true}}}, "RefreshRequest": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"type": "string"}}}, "RegisterRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "ResendConfirmationEmailRequest": {"required": ["email"], "type": "object", "properties": {"email": {"type": "string"}}}, "ResetPasswordRequest": {"required": ["email", "resetCode", "newPassword"], "type": "object", "properties": {"email": {"type": "string"}, "resetCode": {"type": "string"}, "newPassword": {"type": "string"}}}, "TwoFactorRequest": {"type": "object", "properties": {"enable": {"type": "boolean", "nullable": true}, "twoFactorCode": {"type": "string", "nullable": true}, "resetSharedKey": {"type": "boolean"}, "resetRecoveryCodes": {"type": "boolean"}, "forgetMachine": {"type": "boolean"}}}, "TwoFactorResponse": {"required": ["sharedKey", "recoveryCodesLeft", "isTwoFactorEnabled", "isMachineRemembered"], "type": "object", "properties": {"sharedKey": {"type": "string"}, "recoveryCodesLeft": {"type": "integer", "format": "int32"}, "recoveryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isTwoFactorEnabled": {"type": "boolean"}, "isMachineRemembered": {"type": "boolean"}}}}}, "tags": [{"name": "ProScoring.Blazor"}, {"name": "File"}]}