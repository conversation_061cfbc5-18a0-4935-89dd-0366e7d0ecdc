using Microsoft.AspNetCore.Components;

namespace ProScoring.Tests.Infrastructure.Authorization
{
    public class FakeNavigationManager : NavigationManager
    {
        public FakeNavigationManager(string baseUri, string currentUri)
        {
            // This will call our overridden NavigateToCore, 
            // which in turn sets the base Uri property.
            Initialize(baseUri, currentUri);
        }

        protected override void NavigateToCore(string uri, bool forceLoad)
        {
            // Set the protected Uri property of the base class.
            // This makes the Uri accessible via the inherited Uri getter.
            Uri = uri; 
        }
    }
}
