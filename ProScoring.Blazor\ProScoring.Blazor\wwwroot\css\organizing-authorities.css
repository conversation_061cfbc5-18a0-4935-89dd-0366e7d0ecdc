/* Styles for the Organizing Authorities List component */

/* Fixed height card styles */
.fixed-height-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.fixed-height-card .rz-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.fixed-height-card .rz-stack {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Ensure content is properly aligned */
.fixed-height-card .rz-stack > .rz-text {
    text-align: center;
}

/* Ensure the image container has a fixed height */
.fixed-height-card .rz-image {
    height: 100px;
    width: 100px;
    object-fit: contain;
}

/* Ensure the location info has a fixed height */
.fixed-height-card .rz-stack:nth-child(3) {
    min-height: 24px;
}

/* Ensure the additional info has a fixed height */
.fixed-height-card .rz-stack:nth-child(4) {
    flex: 1;
    justify-content: flex-start;
}

/* Responsive styles */
@media (max-width: 576px) {
    .organizing-authorities-list .rz-column {
        margin-bottom: 1rem;
    }
}
