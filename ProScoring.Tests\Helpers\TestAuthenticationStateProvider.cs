using Microsoft.AspNetCore.Components.Authorization;

namespace ProScoring.Tests.Helpers;

/// <summary>
/// A test implementation of <see cref="AuthenticationStateProvider"/> that can be used to set the authentication state.
/// </summary>
public class TestAuthenticationStateProvider : AuthenticationStateProvider
{
    private Task<AuthenticationState>? _authenticationStateTask;

    /// <summary>
    /// Gets the current authentication state.
    /// </summary>
    /// <returns>A task that resolves to the current <see cref="AuthenticationState"/>.</returns>
    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        return _authenticationStateTask
            ?? Task.FromResult(new AuthenticationState(new System.Security.Claims.ClaimsPrincipal()));
    }

    /// <summary>
    /// Sets the authentication state to the specified value.
    /// </summary>
    /// <param name="authenticationStateTask">The authentication state task to set.</param>
    public void SetAuthenticationState(Task<AuthenticationState> authenticationStateTask)
    {
        _authenticationStateTask = authenticationStateTask;
        NotifyAuthenticationStateChanged(authenticationStateTask);
    }
}
