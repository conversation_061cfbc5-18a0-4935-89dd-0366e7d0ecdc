using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization.Entities;

/// <summary>
/// Represents the hierarchy of authorization actions.
/// </summary>
public class ActionHierarchy
    : IHasCompoundKey<ActionHierarchy>,
        IHasForeignKeyConfiguration<ActionHierarchy>,
        IHasInitialSeedData<ActionHierarchy>
{
    #region Properties

    /// <summary>
    /// Gets or sets the name of the parent action. This is part of the compound primary key.
    /// </summary>
    [Key]
    [Column(Order = 10)]
    [ForeignKey(nameof(ParentAction))]
    public string ParentActionName { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the child action. This is part of the compound primary key.
    /// </summary>
    [Key]
    [Column(Order = 20)]
    [ForeignKey(nameof(ChildAction))]
    public string ChildActionName { get; set; } = null!;

    /// <summary>
    /// Gets or sets the parent action navigation property.
    /// </summary>
    public virtual AuthAction ParentAction { get; set; } = null!;

    /// <summary>
    /// Gets or sets the child action navigation property.
    /// </summary>
    public virtual AuthAction ChildAction { get; set; } = null!;

    #endregion Properties

    #region DB Configuration

    #region IHasForeignKeyConfiguration

    /// <summary>
    /// Configures the foreign key relationships for the ActionHierarchy entity.
    /// </summary>
    /// <param name="entity">The entity type builder for ActionHierarchy.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<ActionHierarchy> entity)
    {
        entity
            .HasOne(item => item.ParentAction)
            .WithMany()
            .HasForeignKey(item => item.ParentActionName)
            .OnDelete(DeleteBehavior.Restrict);
        entity
            .HasOne(item => item.ChildAction)
            .WithMany()
            .HasForeignKey(item => item.ChildActionName)
            .OnDelete(DeleteBehavior.Restrict);
    }

    #endregion IHasForeignKeyConfiguration

    #region IHasCompoundKey

    /// <summary>
    /// Defines the compound key for the ActionHierarchy entity.
    /// </summary>
    /// <returns>An expression that defines the compound key.</returns>
    public static Expression<Func<ActionHierarchy, object?>> DefineCompoundKey()
    {
        return item => new { item.ParentActionName, item.ChildActionName };
    }

    #endregion IHasCompoundKey

    #endregion DB Configuration

    #region SeedData

    /// <summary>
    /// Gets the seed data for ActionHierarchy entities.
    /// </summary>
    public static ActionHierarchy[] SeedData =>
        [
            new ActionHierarchy
            {
                ParentActionName = AuthTypes.Actions.ADMIN,
                ChildActionName = AuthTypes.Actions.DELETE,
            },
            new ActionHierarchy
            {
                ParentActionName = AuthTypes.Actions.ADMIN,
                ChildActionName = AuthTypes.Actions.EDIT,
            },
            new ActionHierarchy
            {
                ParentActionName = AuthTypes.Actions.ADMIN,
                ChildActionName = AuthTypes.Actions.VIEW,
            },
            new ActionHierarchy
            {
                ParentActionName = AuthTypes.Actions.DELETE,
                ChildActionName = AuthTypes.Actions.EDIT,
            },
            new ActionHierarchy
            {
                ParentActionName = AuthTypes.Actions.DELETE,
                ChildActionName = AuthTypes.Actions.VIEW,
            },
            new ActionHierarchy { ParentActionName = AuthTypes.Actions.EDIT, ChildActionName = AuthTypes.Actions.VIEW },
            new ActionHierarchy { ParentActionName = AuthTypes.GOD, ChildActionName = AuthTypes.Actions.EDIT },
            new ActionHierarchy { ParentActionName = AuthTypes.GOD, ChildActionName = AuthTypes.Actions.DELETE },
            new ActionHierarchy { ParentActionName = AuthTypes.GOD, ChildActionName = AuthTypes.Actions.ADMIN },
            new ActionHierarchy { ParentActionName = AuthTypes.GOD, ChildActionName = AuthTypes.Actions.VIEW },
        ];

    #endregion SeedData
}
