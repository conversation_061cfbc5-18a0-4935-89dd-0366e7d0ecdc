﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35521.163
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.AppHost", "ProScoring.AppHost\ProScoring.AppHost.csproj", "{D83A276D-8B5F-4EBA-A4DC-C884FE6FFB6E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.ServiceDefaults", "ProScoring.ServiceDefaults\ProScoring.ServiceDefaults.csproj", "{DB844C83-D2C4-4272-94AD-951F687B173B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.ApiService", "ProScoring.ApiService\ProScoring.ApiService.csproj", "{2EA29E7C-4D2D-4A87-8BA9-3C608C64C9A1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Tests", "ProScoring.Tests\ProScoring.Tests.csproj", "{B502C61F-FC45-41BD-AB92-8F5AD3721ABB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Blazor", "ProScoring.Blazor\ProScoring.Blazor\ProScoring.Blazor.csproj", "{63FC4E94-F0D4-4D16-AE32-4624867664C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Blazor.Client", "ProScoring.Blazor\ProScoring.Blazor.Client\ProScoring.Blazor.Client.csproj", "{1A79952D-DADC-4EF1-8577-E23F3AC71CF3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{3AA5CF8A-A423-4D09-A709-E190A6D57E71}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		create_migration.bat = create_migration.bat
		list_migrations.bat = list_migrations.bat
		remove_migration.bat = remove_migration.bat
		update_postgresql.bat = update_postgresql.bat
		update_sqlite.bat = update_sqlite.bat
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Domain", "ProScoring.Domain\ProScoring.Domain.csproj", "{EE0D9DF3-FB12-4628-A1C3-A8158F6A98E9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Infrastructure", "ProScoring.Infrastructure\ProScoring.Infrastructure.csproj", "{B493E218-1C67-49D5-A8B0-9D332E106310}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.BusinessLogic", "ProScoring.BusinessLogic\ProScoring.BusinessLogic.csproj", "{7DA550C9-4E1E-450C-BCAB-33DB7F2B3D0F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.Tests.Playwright", "ProScoring.Tests.Playwright\ProScoring.Tests.Playwright.csproj", "{04D7512A-64A2-4F49-AA0C-775740188E30}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ProScoring.TestData.Playwright", "Proscoring.TestData.Playwright\ProScoring.TestData.Playwright.csproj", "{E3C2187D-F38C-2F1F-9FF1-62DC59767E7A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D83A276D-8B5F-4EBA-A4DC-C884FE6FFB6E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D83A276D-8B5F-4EBA-A4DC-C884FE6FFB6E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D83A276D-8B5F-4EBA-A4DC-C884FE6FFB6E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D83A276D-8B5F-4EBA-A4DC-C884FE6FFB6E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB844C83-D2C4-4272-94AD-951F687B173B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB844C83-D2C4-4272-94AD-951F687B173B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB844C83-D2C4-4272-94AD-951F687B173B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB844C83-D2C4-4272-94AD-951F687B173B}.Release|Any CPU.Build.0 = Release|Any CPU
		{2EA29E7C-4D2D-4A87-8BA9-3C608C64C9A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EA29E7C-4D2D-4A87-8BA9-3C608C64C9A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2EA29E7C-4D2D-4A87-8BA9-3C608C64C9A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2EA29E7C-4D2D-4A87-8BA9-3C608C64C9A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{B502C61F-FC45-41BD-AB92-8F5AD3721ABB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B502C61F-FC45-41BD-AB92-8F5AD3721ABB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B502C61F-FC45-41BD-AB92-8F5AD3721ABB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B502C61F-FC45-41BD-AB92-8F5AD3721ABB}.Release|Any CPU.Build.0 = Release|Any CPU
		{63FC4E94-F0D4-4D16-AE32-4624867664C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63FC4E94-F0D4-4D16-AE32-4624867664C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63FC4E94-F0D4-4D16-AE32-4624867664C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63FC4E94-F0D4-4D16-AE32-4624867664C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{1A79952D-DADC-4EF1-8577-E23F3AC71CF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1A79952D-DADC-4EF1-8577-E23F3AC71CF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1A79952D-DADC-4EF1-8577-E23F3AC71CF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1A79952D-DADC-4EF1-8577-E23F3AC71CF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE0D9DF3-FB12-4628-A1C3-A8158F6A98E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE0D9DF3-FB12-4628-A1C3-A8158F6A98E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE0D9DF3-FB12-4628-A1C3-A8158F6A98E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE0D9DF3-FB12-4628-A1C3-A8158F6A98E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{B493E218-1C67-49D5-A8B0-9D332E106310}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B493E218-1C67-49D5-A8B0-9D332E106310}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B493E218-1C67-49D5-A8B0-9D332E106310}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B493E218-1C67-49D5-A8B0-9D332E106310}.Release|Any CPU.Build.0 = Release|Any CPU
		{7DA550C9-4E1E-450C-BCAB-33DB7F2B3D0F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DA550C9-4E1E-450C-BCAB-33DB7F2B3D0F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DA550C9-4E1E-450C-BCAB-33DB7F2B3D0F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7DA550C9-4E1E-450C-BCAB-33DB7F2B3D0F}.Release|Any CPU.Build.0 = Release|Any CPU
		{04D7512A-64A2-4F49-AA0C-775740188E30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04D7512A-64A2-4F49-AA0C-775740188E30}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04D7512A-64A2-4F49-AA0C-775740188E30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04D7512A-64A2-4F49-AA0C-775740188E30}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3C2187D-F38C-2F1F-9FF1-62DC59767E7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3C2187D-F38C-2F1F-9FF1-62DC59767E7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3C2187D-F38C-2F1F-9FF1-62DC59767E7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3C2187D-F38C-2F1F-9FF1-62DC59767E7A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3D67EC4A-F1A0-4519-ADA1-B10F5CF217AE}
	EndGlobalSection
EndGlobal
