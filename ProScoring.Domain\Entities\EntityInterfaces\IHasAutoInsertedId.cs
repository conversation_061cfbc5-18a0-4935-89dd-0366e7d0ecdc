namespace ProScoring.Domain.Entities.EntityInterfaces;

/// <summary>
/// Interface for entities with automatically inserted IDs.
/// </summary>
public interface IHasAutoInsertedId : IHasId
{
    /// <summary>
    /// Gets or sets the unique identifier for this entity.
    /// <note> This must be nullable for the CustomIdValueGenerator to work.</note>
    /// </summary>
    // string? Id { get; set; }

    /// <summary>
    /// Gets the desired length of the ID.
    /// </summary>
    public int IdLength { get; }

    /// <summary>
    /// Gets a value indicating whether the ID should be padded to the specified length.
    /// </summary>
    public abstract bool IdPadToLength { get; }

    /// <summary>
    /// Gets the prefix to be used for the ID.
    /// </summary>
    public abstract string IdPrefix { get; }
}
