// use copilot-prompt.txt for context

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

public class OrganizingAuthority
    : LastChangeTrackingWithAutoInsertedIdBase,
        IHasForeignKeyConfiguration<OrganizingAuthority>
{
    public const string ID_PREFIX = "O";

    #region properties

    [Key]
    [MaxLength(10)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    [Required]
    [StringLength(100)]
    [Column(Order = 20)]
    public string Name { get; set; } = string.Empty;

    [EmailAddress]
    [ProtectedPersonalData]
    [Column(Order = 30)]
    public string? Email { get; set; }

    [Phone]
    [ProtectedPersonalData]
    [Column(Order = 40)]
    public string? Phone { get; set; }

    [Url]
    [Column(Order = 50)]
    public string? Website { get; set; }

    [Column(Order = 60)]
    public bool Private { get; set; }

    [Column(Order = 70)]
    [StringLength(100)]
    public string? AddressLine1 { get; set; }

    [Column(Order = 80)]
    public string? AddressLine2 { get; set; }

    [Column(Order = 90)]
    [StringLength(100)]
    public string? City { get; set; }

    [Column(Order = 100)]
    [StringLength(50)]
    public string? State { get; set; }

    [Column(Order = 110)]
    [StringLength(20)]
    public string? PostalCode { get; set; }

    [Column(Order = 120)]
    [StringLength(60)]
    public string? Country { get; set; }

    // Navigation property for the related FileRecord
    public virtual FileRecord? Image { get; set; }

    // New property for ImageId
    [ForeignKey(nameof(Image))]
    [Column(Order = 130)]
    public string? ImageId { get; set; }

    [Column(Order = 5)]
    public bool Approved { get; set; } = false;

    public override string IdPrefix => ID_PREFIX;

    public static void ConfigureForeignKeys(EntityTypeBuilder<OrganizingAuthority> entity)
    {
        entity.HasOne(e => e.Image).WithMany().HasForeignKey(e => e.ImageId).OnDelete(DeleteBehavior.Restrict);
    }

    #endregion
}
