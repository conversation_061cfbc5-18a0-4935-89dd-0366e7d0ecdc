namespace ProScoring.Domain.Entities.EntityInterfaces;

/// <summary>
/// Interface for entities that track creation and update information.
/// </summary>
public interface IHasLastChangeTracking
{
    /// <summary>
    /// Gets or sets the date and time when the entity was created.
    /// </summary>
    DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the ID of the user who created the entity.
    /// </summary>
    string? CreatedById { get; set; }

    /// <summary>
    /// Gets or sets the date and time when the entity was last updated.
    /// </summary>
    DateTimeOffset UpdatedAt { get; set; }

    /// <summary>
    /// Gets or sets the ID of the user who last updated the entity.
    /// </summary>
    string? UpdatedById { get; set; }
}
