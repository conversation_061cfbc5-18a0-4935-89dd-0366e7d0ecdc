@page "/organizingauthorities"
@rendermode InteractiveServer
@implements IAsyncDisposable
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.QuickGrid
@using ProScoring.Blazor.Extensions
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using ProScoring.Infrastructure.ServiceInterfaces
@using System.Security.Claims

@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IAuthorizationService AuthorizationService
@inject IOrganizingAuthorityService OrganizingAuthorityService
@inject NavigationManager navigationManager

<PageTitle>Organizing Authorities</PageTitle>

<h1>Organizing Authorities</h1>

<div class="d-flex justify-content-between align-items-center mb-3">
    <div></div>
    <RadzenButton 
        Icon="add" 
        Text="Add Organizing Authority" 
        ButtonStyle="ButtonStyle.Primary"
        Click="@(() => navigationManager.NavigateTo("organizingauthorities/create"))"
        @attributes="@("create-new-oa-link".AsTestId())" />
</div>

<RadzenDataGrid 
    @ref="grid" 
    Data="@organizingAuthorities" 
    TItem="OrganizingAuthority" 
    AllowFiltering="true"
    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" 
    LogicalFilterOperator="LogicalFilterOperator.Or"
    FilterPopupRenderMode="PopupRenderMode.OnDemand" 
    AllowPagination="true" 
    AllowSorting="true" 
    AllowColumnResize="true"
    CellRender="@OnCellRender" 
    @attributes="@("oa-data-grid".AsTestId())">
    <Columns>
        @if (IsHmfic)
        {
            <RadzenDataGridColumn 
                TItem="OrganizingAuthority" 
                Property="Approved" 
                Title="Approved" 
                Width="50px"
                TextAlign="TextAlign.Center" 
                Filterable="true" 
                Sortable="true">
                <Template Context="organizingAuthority">
                    @if (organizingAuthority.Approved)
                    {
                        <RadzenIcon 
                            Icon="check_circle" 
                            Style="color: green;" 
                            @attributes="@("oa-approved-icon".AsTestId())" />
                    }
                    else
                    {
                        <RadzenIcon 
                            Icon="cancel" 
                            Style="color: red;" 
                            @attributes="@("oa-not-approved-icon".AsTestId())" />
                    }
                </Template>
            </RadzenDataGridColumn>
        }

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Width="50px" 
            Filterable="false" 
            Sortable="false"
            Resizable="false" 
            HeaderCssClass="@BURGEE_COLUMN_CSS_CLASS">
            <Template Context="organizingAuthority">
                @if (!string.IsNullOrEmpty(organizingAuthority?.ImageId))
                {
                    <RadzenImage 
                        Path="@($"/api/file/download/{organizingAuthority.ImageId}")"
                        AlternateText="@($"{organizingAuthority.Name} Burgee")"
                        Style="width: 50px; height: 50px; object-fit: contain;"
                        @attributes="@("oa-burgee-image".AsTestId())" />
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn TItem="OrganizingAuthority" Property="Name" Title="Name" Sortable="true">
            <Template Context="organizingAuthority">
                <a href="@($"organizingauthorities/details?id={organizingAuthority.Id}")"
                    title="View details for @organizingAuthority.Name" 
                    @attributes="@("oa-details-link".AsTestId())">
                    @organizingAuthority.Name
                </a>
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="Website" 
            Title="Website" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @if (!string.IsNullOrEmpty(organizingAuthority.Website))
                {
                    <a href="@organizingAuthority.Website" 
                        target="_blank"
                        title="Visit @organizingAuthority.Name's website in a new tab">
                        @organizingAuthority.Website
                    </a>
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="Phone" 
            Title="Phone" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @if (!string.IsNullOrEmpty(organizingAuthority.Phone))
                {
                    <a href="tel:@organizingAuthority.Phone" 
                        title="Call @organizingAuthority.Name">
                        @organizingAuthority.Phone
                    </a>
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="Email" 
            Title="Email" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @if (!string.IsNullOrEmpty(organizingAuthority.Email))
                {
                    <a href="mailto:@organizingAuthority.Email" 
                        title="Email @organizingAuthority.Name">
                        @organizingAuthority.Email
                    </a>
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="AddressLine1" 
            Title="Address Line 1" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.AddressLine1
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="AddressLine2" 
            Title="Address Line 2" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.AddressLine2
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="City" 
            Title="City" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.City
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="State" 
            Title="State" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.State
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="PostalCode" 
            Title="Postal Code" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.PostalCode
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Property="Country" 
            Title="Country" 
            Sortable="true"
            Filterable="false">
            <Template Context="organizingAuthority">
                @organizingAuthority.Country
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn 
            TItem="OrganizingAuthority" 
            Context="organizingAuthority" 
            Width="100px"
            TextAlign="TextAlign.Right" 
            Filterable="false">
            <Template Context="organizingAuthority">
                <div style="display: flex; align-items: center; justify-content: flex-end; white-space: nowrap;">
                    @if (IsAuthorizedToEdit(organizingAuthority).Result)
                    {
                        <a href="@($"organizingauthorities/edit?id={organizingAuthority.Id}")"
                            title="Edit @organizingAuthority.Name" 
                            @attributes="@("oa-edit-link".AsTestId())">
                            <RadzenIcon Icon="edit" Style="cursor: pointer;" />
                        </a>
                    }
                    @if (IsAuthorizedToDelete(organizingAuthority).Result)
                    {
                        <a href="@($"organizingauthorities/delete?id={organizingAuthority.Id}")"
                            title="Delete @organizingAuthority.Name" 
                            @attributes="@("oa-delete-link".AsTestId())">
                            <RadzenIcon Icon="delete" Style="cursor: pointer;" />
                        </a>
                    }
                </div>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>

@code {
    #region Fields

    private const string BURGEE_COLUMN_CSS_CLASS = "burgee_column";
    private RadzenDataGrid<OrganizingAuthority>? grid;
    private List<OrganizingAuthority>? organizingAuthorities;

    #endregion

    #region Properties

    private bool IsHmfic { get; set; }

    #endregion

    #region Methods

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// Also checks if the current user has HMFIC (highest level) permissions.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        IsHmfic = authState.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");

        organizingAuthorities = (await OrganizingAuthorityService.GetAllAsync()).ToList();
    }

    /// <summary>
    /// Checks if the current user is authorized to edit the specified organizing authority.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority to check authorization for.</param>
    /// <returns>True if the user can edit the organizing authority, otherwise false.</returns>
    private async Task<bool> IsAuthorizedToEditAsync(OrganizingAuthority organizingAuthority)
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        return (await AuthorizationService.AuthorizeAsync(user, organizingAuthority,
            EditAuthorizationForResourceHandler.PolicyName)).Succeeded;
    }

    /// <summary>
    /// Wrapper method for IsAuthorizedToEditAsync to be used in view context.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority to check authorization for.</param>
    /// <returns>True if the user can edit the organizing authority, otherwise false.</returns>
    private Task<bool> IsAuthorizedToEdit(OrganizingAuthority organizingAuthority)
    {
        return IsAuthorizedToEditAsync(organizingAuthority);
    }

    /// <summary>
    /// Checks if the current user is authorized to delete the specified organizing authority.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority to check authorization for.</param>
    /// <returns>True if the user can delete the organizing authority, otherwise false.</returns>
    private async Task<bool> IsAuthorizedToDeleteAsync(OrganizingAuthority organizingAuthority)
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        return (await AuthorizationService.AuthorizeAsync(user, organizingAuthority,
            EditAuthorizationForResourceHandler.PolicyName)).Succeeded;
    }

    /// <summary>
    /// Wrapper method for IsAuthorizedToDeleteAsync to be used in view context.
    /// </summary>
    /// <param name="organizingAuthority">The organizing authority to check authorization for.</param>
    /// <returns>True if the user can delete the organizing authority, otherwise false.</returns>
    private Task<bool> IsAuthorizedToDelete(OrganizingAuthority organizingAuthority)
    {
        return IsAuthorizedToDeleteAsync(organizingAuthority);
    }

    /// <summary>
    /// Customizes the rendering of cells in the data grid.
    /// Specifically handles special styling for the burgee column.
    /// </summary>
    /// <param name="args">The cell render event arguments.</param>
    private void OnCellRender(DataGridCellRenderEventArgs<OrganizingAuthority> args)
    {
        if (args.Column.HeaderCssClass == BURGEE_COLUMN_CSS_CLASS)
        {
            args.Attributes.Add("style", "padding: 0;");
        }
    }

    /// <summary>
    /// Implements the IAsyncDisposable interface.
    /// Currently returns a completed task as no async cleanup is needed.
    /// </summary>
    public ValueTask DisposeAsync()
    {
        return ValueTask.CompletedTask;
    }

    #endregion
}
