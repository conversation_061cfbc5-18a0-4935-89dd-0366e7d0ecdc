using Microsoft.Extensions.Logging;
using Neovolve.Logging.Xunit;
using Xunit.Abstractions;

namespace ProScoring.Tests.Helpers;

/// <summary>
/// The <see cref="SafeTestOutputHelperLoggerCreator" /> class provides a safe way to create loggers for test output helpers.
/// that will not break the test if the test context has gone away.
/// See: https://stackoverflow.com/questions/69436194/there-is-no-currently-active-test-exception-in-xunit-c-sharp
/// </summary>
public static class SafeTestOutputHelperLoggerCreator
{
    public static IServiceCollection AddTransientSafeLogger<T>(
        this IServiceCollection services,
        ITestOutputHelper output
    )
    {
        return services.AddTransient<ILogger<T>>(_ => output.BuildSafeLoggerFor<T>());
    }

    public static IServiceCollection AddScopedSafeLogger<T>(this IServiceCollection services, ITestOutputHelper output)
    {
        return services.AddScoped<ILogger<T>>(_ => output.BuildSafeLoggerFor<T>());
    }

    public static ICacheLogger<T> BuildSafeLoggerFor<T>(this ITestOutputHelper output)
    {
        return new SafeTestOutputHelper(output).BuildLoggerFor<T>();
    }

    private class SafeTestOutputHelper(ITestOutputHelper output) : ITestOutputHelper
    {
        public void WriteLine(string message)
        {
            try
            {
                output.WriteLine(message);
            }
            catch
            {
                // ignored
            }
        }

        public void WriteLine(string format, params object[] args)
        {
            try
            {
                output.WriteLine(format, args);
            }
            catch
            {
                // ignored
            }
        }
    }
}
