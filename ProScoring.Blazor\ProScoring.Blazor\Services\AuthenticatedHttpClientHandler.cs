using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace ProScoring.Blazor.Services;

/// <summary>
/// HTTP message handler that adds authentication cookies from the current HttpContext to outgoing requests.
/// This ensures that API calls made by HTTP clients include the user's authentication information.
/// </summary>
public class AuthenticatedHttpClientHandler : DelegatingHandler
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    /// Initializes a new instance of the <see cref="AuthenticatedHttpClientHandler"/> class.
    /// </summary>
    /// <param name="httpContextAccessor">The HTTP context accessor to get the current user's authentication cookies.</param>
    public AuthenticatedHttpClientHandler(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// Sends an HTTP request with authentication cookies from the current HttpContext.
    /// </summary>
    /// <param name="request">The HTTP request message to send.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>The HTTP response message.</returns>
    protected override Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken
    )
    {
        var httpContext = _httpContextAccessor.HttpContext;

        // Only add cookies if we have an HttpContext (we're in a server-side context)
        if (httpContext != null)
        {
            // Copy cookies from the current request to the outgoing request
            if (httpContext.Request.Headers.TryGetValue("Cookie", out var cookies))
            {
                request.Headers.Add("Cookie", cookies.ToArray());
            }

            // Copy authorization header if present
            if (httpContext.Request.Headers.TryGetValue("Authorization", out var auth))
            {
                request.Headers.Add("Authorization", auth.ToArray());
            }
        }

        return base.SendAsync(request, cancellationToken);
    }
}
