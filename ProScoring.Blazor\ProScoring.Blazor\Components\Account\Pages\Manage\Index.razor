﻿@page "/Account/Manage"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.ValidationAttributes
@using ProScoring.Domain.Entities

@inject IdentityRedirectManager RedirectManager
@inject IdentityUserAccessor UserAccessor
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Profile</PageTitle>

<div class="profile-container">
    <h3>Profile</h3>
    <StatusMessage />

    <div class="row">
        <div class="col-xl-6">
            <EditForm Model="Input" FormName="profile" OnValidSubmit="OnValidSubmitAsync" method="post">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" role="alert" />

                <!-- Username field (read-only) -->
                <div class="form-floating mb-3">
                    <input type="text" value="@username" id="username" class="form-control"
                        placeholder="Choose your username." disabled data-testid="username-input" />
                    <label for="username" class="form-label">Username</label>
                </div>

                <!-- First Name field -->
                <div class="form-floating mb-3">
                    <InputText @bind-Value="Input.GivenName" id="Input.GivenName" class="form-control"
                        autocomplete="given-name" aria-required="true" placeholder="First name"
                        data-testid="given-name-input" />
                    <label for="Input.GivenName" class="form-label">First Name</label>
                    <ValidationMessage For="() => Input.GivenName" class="text-danger" />
                </div>

                <!-- Last Name field -->
                <div class="form-floating mb-3">
                    <InputText @bind-Value="Input.Surname" id="Input.Surname" class="form-control"
                        autocomplete="family-name" aria-required="true" placeholder="Last name"
                        data-testid="surname-input" />
                    <label for="Input.Surname" class="form-label">Last Name</label>
                    <ValidationMessage For="() => Input.Surname" class="text-danger" />
                </div>

                <!-- Phone Number field -->
                <div class="form-floating mb-3">
                    <InputText @bind-Value="Input.PhoneNumber" id="Input.PhoneNumber" class="form-control"
                        placeholder="Enter your phone number" data-testid="phone-number-input" />
                    <label for="Input.PhoneNumber" class="form-label">Phone number</label>
                    <ValidationMessage For="() => Input.PhoneNumber" class="text-danger" />
                </div>

                <button type="submit" class="w-100 btn btn-lg btn-primary" data-testid="save-profile-button">
                    Save
                </button>
            </EditForm>
        </div>
    </div>
</div>

@code {
    #region Fields
    private ApplicationUser user = default!;
    private string? phoneNumber;
    private string? username;
    #endregion Fields

    #region Properties
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();
    #endregion Properties

    #region Lifecycle Methods
    /// <summary>
    /// Initializes the user profile page with data from the current user.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected override async Task OnInitializedAsync()
    {
        // Get user data
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        username = await UserManager.GetUserNameAsync(user);
        phoneNumber = await UserManager.GetPhoneNumberAsync(user);

        // Initialize form with current user data
        Input.PhoneNumber ??= phoneNumber;
        Input.GivenName ??= user.GivenName;
        Input.Surname ??= user.Surname;
    }
    #endregion Lifecycle Methods

    #region Form Handling
    /// <summary>
    /// Handles form submission to update the user's profile information.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task OnValidSubmitAsync()
    {
        bool needsUpdate = false;

        // Update phone number if changed
        if (Input.PhoneNumber != phoneNumber)
        {
            var setPhoneResult = await UserManager.SetPhoneNumberAsync(user, Input.PhoneNumber);
            if (!setPhoneResult.Succeeded)
            {
                RedirectManager.RedirectToCurrentPageWithStatus(
                "Error: Failed to set phone number.", HttpContext);
                return;
            }
            needsUpdate = true;
        }

        // Update first name if changed
        if (Input.GivenName != user.GivenName)
        {
            user.GivenName = Input.GivenName;
            needsUpdate = true;
        }

        // Update last name if changed
        if (Input.Surname != user.Surname)
        {
            user.Surname = Input.Surname;
            needsUpdate = true;
        }

        // Save changes if any updates were made
        if (needsUpdate)
        {
            var updateResult = await UserManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                RedirectManager.RedirectToCurrentPageWithStatus(
                "Error: Failed to update profile information.", HttpContext);
                return;
            }

            await SignInManager.RefreshSignInAsync(user);
            RedirectManager.RedirectToCurrentPageWithStatus(
            "Your profile has been updated", HttpContext);
        }
    }
    #endregion Form Handling

    #region Models
    /// <summary>
    /// Model for capturing user profile input data.
    /// </summary>
    private sealed class InputModel
    {
        [StringLength(50, ErrorMessage = "The {0} must be at most {1} characters long.")]
        [Display(Name = "First Name")]
        public string? GivenName { get; set; }

        [StringLength(50, ErrorMessage = "The {0} must be at most {1} characters long.")]
        [Display(Name = "Last Name")]
        public string? Surname { get; set; }

        [OptionalPhone]
        [Display(Name = "Phone number")]
        public string? PhoneNumber { get; set; }
    }
    #endregion Models
}
