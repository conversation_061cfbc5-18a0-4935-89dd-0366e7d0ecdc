using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.BusinessLogic.Services;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.BusinessLogic;

public class OrganizingAuthorityServiceTests : IDisposable
{
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly IProScoringAuthorizationService _mockProScoringAuthorizationService;
    private readonly IHttpContextAccessor _mockHttpContextAccessor;
    private readonly ServiceAuthorizationHelper _mockServiceAuthorizationHelper;
    private readonly ApplicationDbContext _dbContext;
    private readonly IFileService _mockFileService;
    private readonly OrganizingAuthorityService _service;
    private readonly ITestOutputHelper _output;
    private readonly string _userId = "U-test-user-id";
    private readonly string _dbName;
    private readonly ILogger<ApplicationDbContext> _logger;
    private readonly CustomIdValueGenerator _idGenerator;
    private readonly FixedDateTimeOffsetProvider _dateTimeProvider;

    public OrganizingAuthorityServiceTests(ITestOutputHelper output)
    {
        _output = output;
        _authStateProvider = Substitute.For<AuthenticationStateProvider>();
        _dbName = Guid.NewGuid().ToString();
        _logger = Substitute.For<ILogger<ApplicationDbContext>>();
        _idGenerator = new CustomIdValueGenerator(
            Substitute.For<IIdGenerationUtilService>(),
            Substitute.For<ILogger<CustomIdValueGenerator>>()
        );
        _dateTimeProvider = new FixedDateTimeOffsetProvider(1776, 7, 4, 12, 0, 0);
        SetupAuthenticationState();
        _dbContext = CreateNewContext();
        _mockFileService = Substitute.For<IFileService>();
        _mockProScoringAuthorizationService = Substitute.For<IProScoringAuthorizationService>();
        _mockHttpContextAccessor = Substitute.For<IHttpContextAccessor>();
        var mockAuthService = Substitute.For<IAuthorizationService>();
        mockAuthService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));
        _mockServiceAuthorizationHelper = Substitute.For<ServiceAuthorizationHelper>(
            mockAuthService,
            _mockHttpContextAccessor,
            Substitute.For<ILogger<ServiceAuthorizationHelper>>()
        );
        _service = new OrganizingAuthorityService(
            _dbContext,
            _mockFileService,
            _mockProScoringAuthorizationService,
            _mockHttpContextAccessor,
            _mockServiceAuthorizationHelper,
            output.BuildSafeLoggerFor<OrganizingAuthorityService>()
        );
    }

    private void SetupAuthenticationState(string? userId = null)
    {
        userId ??= _userId;
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, userId) };
        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var authState = new AuthenticationState(principal);

        _authStateProvider.GetAuthenticationStateAsync().Returns(Task.FromResult(authState));
    }

    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        GC.SuppressFinalize(this);
    }

    private ApplicationDbContext CreateNewContext()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>().UseInMemoryDatabase(_dbName).Options;

        return new ApplicationDbContext(options, _authStateProvider, _idGenerator, _logger, _dateTimeProvider);
    }

    [Fact]
    public async Task CreateAsync_FromOA_AddsOrganizingAuthority()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority { Name = "Authority 1" };

        // Act
        var result = await _service.CreateAsync(organizingAuthority);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeNull();
        result.CreatedAt.Should().NotBe(default);
        result.CreatedById.Should().Be(_userId);
        result.UpdatedAt.Should().NotBe(default);
        result.UpdatedById.Should().Be(_userId);

        // Verify in fresh context
        var saved = await _dbContext.OrganizingAuthorities.FindAsync(result.Id);
        saved.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateAsync_FromDto_AddsOrganizingAuthority()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Authority 1" };

        // Act
        var result = await _service.CreateAsync(dto);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeNull();
        result.CreatedAt.Should().NotBe(default);
        result.UpdatedAt.Should().NotBe(default);

        // Verify in fresh context
        var saved = await _dbContext.OrganizingAuthorities.FindAsync(result.Id);
        saved.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateAsync_FromDto_AddsUserAuthAdminRightsForCreator()
    {
        // Arrange
        var dto = new OrganizingAuthorityUploadDto { Name = "Authority 1" };

        // Act
        var oa = await _service.CreateAsync(dto);

        // Assert
        oa.Should().NotBeNull();
        await _mockProScoringAuthorizationService
            .Received(1)
            .CreateUserAuthActionAsync(
                Arg.Any<string>(), // ownerId
                Arg.Any<string>(), // ao.Id
                AuthTypes.Actions.ADMIN
            );
    }

    [Fact]
    public async Task CreateAsync_FromOA_ThrowsException_WhenOrganizingAuthorityIsNull()
    {
        // Act
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
        Func<Task> act = () => _service.CreateAsync((OrganizingAuthority)null);
#pragma warning restore CS8625 // Cannot convert null literal to non-nullable reference type.
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.

        // Assert
        await act.Should().ThrowAsync<ArgumentNullException>();
    }

    [Fact]
    public async Task CreateAsync_FromOA_AddsUserAuthAdminRightsForCreator()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "1",
            Name = "Authority 1",
            CreatedById = "U1",
        };

        // Act
        await _service.CreateAsync(organizingAuthority);

        // Assert
        organizingAuthority.CreatedById.Should().NotBeNullOrEmpty();
        await _mockProScoringAuthorizationService
            .Received(1)
            .CreateUserAuthActionAsync(
                organizingAuthority.CreatedById,
                organizingAuthority.Id,
                AuthTypes.Actions.ADMIN
            );
    }

    [Fact]
    public async Task DeleteAsync_RemovesOrganizingAuthority()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority { Id = "1", Name = "Authority 1" };
        _dbContext.OrganizingAuthorities.Add(organizingAuthority);
        await _dbContext.SaveChangesAsync();

        // Act
        await _service.DeleteAsync("1");

        // Assert
        var deleted = await _dbContext.OrganizingAuthorities.FindAsync("1");
        deleted.Should().BeNull();
    }

    [Fact]
    public async Task GetAllAsync_ReturnsAllOrganizingAuthorities()
    {
        // Arrange
        var organizingAuthorities = new List<OrganizingAuthority>
        {
            new() { Id = "1", Name = "Authority 1" },
            new() { Id = "2", Name = "Authority 2" },
        };
        _dbContext.OrganizingAuthorities.AddRange(organizingAuthorities);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Count().Should().Be(2);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsOrganizingAuthority()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority { Id = "1", Name = "Authority 1" };
        _dbContext.OrganizingAuthorities.Add(organizingAuthority);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _service.GetByIdAsync("1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("1", result.Id);
    }

    [Fact]
    public async Task UpdateAsync_UpdatesOrganizingAuthority()
    {
        // Arrange
        var organizingAuthority = new OrganizingAuthority { Id = "1", Name = "Authority 1" };
        _dbContext.OrganizingAuthorities.Add(organizingAuthority);
        await _dbContext.SaveChangesAsync();

        // Act
        organizingAuthority.Name = "Updated Authority 1";
        await _service.UpdateAsync(organizingAuthority);

        // Assert
        var updated = await _dbContext.OrganizingAuthorities.FindAsync("1");
        updated.Should().NotBeNull();
        updated!.Name.Should().Be("Updated Authority 1");
    }
}
