using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// One or more races that are scored together as a single event.
/// </summary>
public class Regatta : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<Regatta>
{
    #region constants

    public const string ID_PREFIX = "G";

    #endregion

    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for the regatta.
    /// </summary>
    [Key]
    [MaxLength(10)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the regatta.
    /// </summary>
    [Column(Order = 20)]
    [StringLength(255, MinimumLength = 3)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the ID of the organizing authority.
    /// </summary>
    [Column(Order = 30)]
    [ForeignKey(nameof(OrganizingAuthority))]
    public required string OrganizingAuthorityId { get; set; }

    /// <summary>
    /// Gets or sets the organizing authority for this regatta.
    /// </summary>
    public virtual OrganizingAuthority? OrganizingAuthority { get; set; }

    /// <summary>
    /// Gets or sets the description of the regatta.
    /// </summary>
    [Column(Order = 40)]
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the start date of the regatta.
    /// </summary>
    [Column(Order = 50)]
    public required DateOnly StartDate { get; set; }

    /// <summary>
    /// Gets or sets the competition days for the regatta.
    /// </summary>
    [Column(Order = 60)]
    [MaxLength(100)]
    public DateOnly[]? CompetitionDays { get; set; } = [];

    /// <summary>
    /// Gets or sets the registration deadline for the regatta.
    /// </summary>
    [Column(Order = 70)]
    public DateTimeOffset? RegistrationDeadline { get; set; }

    /// <summary>
    /// Gets or sets when registration opens for the regatta.
    /// </summary>
    [Column(Order = 80)]
    public DateTimeOffset? RegistrationOpening { get; set; }

    /// <summary>
    /// Gets or sets the first line of the address where the regatta is held.
    /// </summary>
    [Column(Order = 90)]
    [StringLength(100)]
    public string AddressLine1 { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the second line of the address where the regatta is held.
    /// </summary>
    [Column(Order = 100)]
    [StringLength(100)]
    public string AddressLine2 { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the city where the regatta is held.
    /// </summary>
    [Column(Order = 110)]
    [StringLength(100)]
    public string City { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the ID of the event logo.
    /// </summary>
    [Column(Order = 120)]
    [ForeignKey(nameof(EventLogo))]
    public string? EventLogoId { get; set; }

    /// <summary>
    /// Gets or sets the event logo file record.
    /// </summary>
    public virtual FileRecord? EventLogo { get; set; }

    /// <summary>
    /// Gets or sets the end date of the regatta.
    /// </summary>
    [Column(Order = 130)]
    public required DateOnly EndDate { get; set; }

    /// <summary>
    /// Gets or sets the location description of the regatta.
    /// </summary>
    [Column(Order = 140)]
    [StringLength(1000)]
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the regatta is private.
    /// </summary>
    [Column(Order = 150)]
    public bool Private { get; set; }

    /// <summary>
    /// Gets or sets the state or province where the regatta is held.
    /// </summary>
    [Column(Order = 160)]
    [StringLength(50)]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the website URL for the regatta.
    /// </summary>
    [Column(Order = 170)]
    [Url(ErrorMessage = "Invalid URL. Be sure to include `http://` or `https://`")]
    public string Website { get; set; } = string.Empty;

    /// <summary>
    /// Gets the prefix used for generating IDs for regattas.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the collection of external links associated with this regatta.
    /// </summary>
    public virtual ICollection<RegattaExternalLink> ExternalLinks { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of boats participating in this regatta.
    /// </summary>
    public virtual ICollection<RegattaBoat> RegattaBoats { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of competitors participating in this regatta.
    /// </summary>
    public virtual ICollection<RegattaCompetitor> RegattaCompetitors { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of classes in this regatta.
    /// </summary>
    public virtual ICollection<RegattaClass> RegattaClasses { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of fleets in this regatta.
    /// </summary>
    public virtual ICollection<RegattaFleet> RegattaFleets { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of ratings used in this regatta.
    /// </summary>
    public virtual ICollection<RegattaRating> RegattaRatings { get; set; } = [];

    /// <summary>
    /// Gets or sets the collection of rating values used in this regatta.
    /// </summary>
    public virtual ICollection<RegattaRatingValue> RegattaRatingValues { get; set; } = [];

    #endregion

    #region DB Configuration

    /// <summary>
    /// Configures foreign key relationships for the Regatta entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the Regatta entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<Regatta> entity)
    {
        entity
            .HasOne(e => e.OrganizingAuthority)
            .WithMany()
            .HasForeignKey(e => e.OrganizingAuthorityId)
            .OnDelete(DeleteBehavior.Restrict);

        entity.HasOne(e => e.EventLogo).WithMany().HasForeignKey(e => e.EventLogoId).OnDelete(DeleteBehavior.Restrict);
        // Configure navigation collections for RegattaEntityBase derived entities
        entity.HasMany(r => r.RegattaBoats).WithOne(rb => rb.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.RegattaCompetitors).WithOne(rc => rc.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.RegattaClasses).WithOne(rc => rc.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.RegattaFleets).WithOne(rf => rf.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.RegattaRatings).WithOne(rr => rr.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.RegattaRatingValues).WithOne(rrv => rrv.Regatta).OnDelete(DeleteBehavior.Restrict);
        entity.HasMany(r => r.ExternalLinks).WithOne(rel => rel.Regatta).OnDelete(DeleteBehavior.Restrict);

        // Configure auto-include for navigation properties
        entity.Navigation(r => r.OrganizingAuthority).AutoInclude();
        entity.Navigation(r => r.EventLogo).AutoInclude();
        entity.Navigation(r => r.RegattaBoats).AutoInclude();
        entity.Navigation(r => r.RegattaCompetitors).AutoInclude();
        entity.Navigation(r => r.RegattaClasses).AutoInclude();
        entity.Navigation(r => r.RegattaFleets).AutoInclude();
        entity.Navigation(r => r.RegattaRatings).AutoInclude();
        entity.Navigation(r => r.RegattaRatingValues).AutoInclude();
        // ExternalLinks don't need to be automatically loaded
    }

    #endregion DB Configuration
}
