using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the OrganizingAuthoritiesList component's visual appearance and card dimensions.
/// </summary>
public class OrganizingAuthoritiesCardDimensionsTests : IClassFixture<PlaywrightFixture>
{
    #region Fields
    private readonly PlaywrightFixture _fixture;
    #endregion Fields

    #region Constructors
    public OrganizingAuthoritiesCardDimensionsTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion Constructors

    #region Test Methods
    /// <summary>
    /// Tests that all organizing authority cards have the same height when using card view.
    /// </summary>
    [Fact]
    public async Task OrganizingAuthorities_Cards_Have_Same_Height_In_CardView()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Navigate to the home page
            await page.GotoAsync(_fixture.Settings.BaseUrl);

            // Wait for the page to load
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Click on the Organizers tab to show the organizing authorities list
            await page.ClickAsync("[data-testid=tab-organizers]");

            // Wait for the organizing authorities content to be visible
            await page.WaitForSelectorAsync("[data-testid=content-organizers]");

            // Make sure we're in card view by clicking the Card view button if needed
            var isCardViewActive = await page.EvaluateAsync<bool>(
                @"
                document.querySelector('[data-testid=oa-display-mode-toggle] .rz-button:first-child')?.classList.contains('rz-state-active') ?? false
            "
            );

            if (!isCardViewActive)
            {
                await page.ClickAsync("[data-testid=oa-display-mode-toggle] .rz-button:first-child");
                // Wait for the view to update
                await page.WaitForTimeoutAsync(500);
            }

            // Wait for cards to be visible
            await page.WaitForSelectorAsync("[data-testid=oa-info-card]");

            // Get heights of all organizing authority cards
            var cardHeights = await page.EvaluateAsync<double[]>(
                @"
                Array.from(document.querySelectorAll('[data-testid=oa-info-card]'))
                    .map(card => {
                        const rect = card.getBoundingClientRect();
                        return rect.height;
                    })
            "
            );

            // Assert
            cardHeights.Should().NotBeEmpty("There should be at least one card visible");

            // Check that all heights are the same
            if (cardHeights.Length > 1)
            {
                var firstHeight = cardHeights[0];
                foreach (var height in cardHeights)
                {
                    // With our dynamic height solution, all cards should have exactly the same height
                    height
                        .Should()
                        .BeApproximately(firstHeight, 1.0, "All cards should have the same height in card view");
                }
            }
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that all organizing authority cards have the same width when using card view.
    /// </summary>
    [Fact]
    public async Task OrganizingAuthorities_Cards_Have_Same_Width_In_CardView()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Navigate to the home page
            await page.GotoAsync(_fixture.Settings.BaseUrl);

            // Wait for the page to load
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Click on the Organizers tab to show the organizing authorities list
            await page.ClickAsync("[data-testid=tab-organizers]");

            // Wait for the organizing authorities content to be visible
            await page.WaitForSelectorAsync("[data-testid=content-organizers]");

            // Make sure we're in card view by clicking the Card view button if needed
            var isCardViewActive = await page.EvaluateAsync<bool>(
                @"
                document.querySelector('[data-testid=oa-display-mode-toggle] .rz-button:first-child')?.classList.contains('rz-state-active') ?? false
            "
            );

            if (!isCardViewActive)
            {
                await page.ClickAsync("[data-testid=oa-display-mode-toggle] .rz-button:first-child");
                // Wait for the view to update
                await page.WaitForTimeoutAsync(500);
            }

            // Wait for cards to be visible
            await page.WaitForSelectorAsync("[data-testid=oa-info-card]");

            // Get widths of all organizing authority cards
            var cardWidths = await page.EvaluateAsync<double[]>(
                @"
                Array.from(document.querySelectorAll('[data-testid=oa-info-card]'))
                    .map(card => {
                        const rect = card.getBoundingClientRect();
                        return rect.width;
                    })
            "
            );

            // Assert
            cardWidths.Should().NotBeEmpty("There should be at least one card visible");

            // Check that all widths are the same
            if (cardWidths.Length > 1)
            {
                var firstWidth = cardWidths[0];
                foreach (var width in cardWidths)
                {
                    // With our dynamic width solution, all cards should have exactly the same width
                    width
                        .Should()
                        .BeApproximately(firstWidth, 1.0, "All cards should have the same width in card view");
                }
            }
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Test Methods
}
