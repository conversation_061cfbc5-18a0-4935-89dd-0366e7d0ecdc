using System;
using System.Collections.Generic; // For List
using System.Linq; // For .ToArray() on GetChildren
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity.UI.Services; // For IEmailSender
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using ProScoring.Domain.Entities; // For ApplicationUser
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class EmailSenderServiceTests
    {
        private readonly IEmailSender _actualSenderSubstitute; // Changed
        private readonly ILogger<EmailSenderService> _loggerSubstitute; // Changed
        private readonly ApplicationUser _testUser;
        private EmailSenderService _emailSenderService;

        public EmailSenderServiceTests()
        {
            _actualSenderSubstitute = Substitute.For<IEmailSender>();
            _loggerSubstitute = Substitute.For<ILogger<EmailSenderService>>();
            _testUser = new ApplicationUser { Email = "<EMAIL>", UserName = "testuser" };
        }

        private void SetupEmailOptions(
            bool enabled,
            string apiKey = "test_api_key",
            string fromAddress = "<EMAIL>",
            string fromName = "Test App"
        )
        {
            // Use in-memory configuration instead of complex mocking
            var configData = new Dictionary<string, string?>
            {
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.Enabled)}"] = enabled.ToString(),
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.ApiKey)}"] = apiKey,
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromAddress)}"] = fromAddress,
                [$"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromName)}"] = fromName,
            };

            var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

            _emailSenderService = new EmailSenderService(_actualSenderSubstitute, configuration, _loggerSubstitute);
        }

        // --- SendConfirmationLinkAsync Tests ---
        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute
                .Received(1)
                .Log( // Changed
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception?, string>>()
                );
        }

        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }

        // --- SendPasswordResetCodeAsync Tests ---
        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute
                .Received(1)
                .Log( // Changed
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }

        // --- SendPasswordResetLinkAsync Tests ---
        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled, email would have been <NAME_EMAIL>";

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            _actualSenderSubstitute
                .DidNotReceive()
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute
                .Received(1)
                .Log( // Changed
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains(expectedLogMessage)),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            _actualSenderSubstitute
                .Received(1)
                .SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }
    }
}
