using System.Text;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Service that generates unique identifiers for entities with customizable formatting.
/// </summary>
public class GuidishIdGenerationUtilService(
    IDateTimeOffsetProvider dateTimeOffsetProvider,
    ILogger<GuidishIdGenerationUtilService> logger
) : IIdGenerationUtilService
{
    #region Fields
    private const string DOMAIN_CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static readonly object _lockObject = new();
    private static int _suffixIndex;
    private readonly IDateTimeOffsetProvider _dateTimeOffsetProvider = dateTimeOffsetProvider;
    private readonly ILogger<GuidishIdGenerationUtilService> _logger = logger;
    #endregion

    #region Methods
    /// <summary>
    /// Generates a new ID string with optional prefix, length, and randomization.
    /// </summary>
    /// <param name="prefix">The optional prefix for the ID.</param>
    /// <param name="length">The length of the ID.</param>
    /// <param name="pad">Indicates whether the ID should be padded to the specified length.</param>
    /// <returns>The generated ID string.</returns>
    public string GenerateId(string prefix = "", int length = 8, bool pad = false)
    {
        StringBuilder id = new(ReturnSuffixCharacter(), length);
        var timeSeed = _dateTimeOffsetProvider.UtcNow.ToUnixTimeSeconds();

        // since ToUnixTimeSeconds is Negative for dates before 1970-01-01T00:00:00Z, we need to handle negative values
        // which can happen in our test cases.
        if (timeSeed < 0)
        {
            _logger.LogWarning("Time provided is before 1970");
            id.Insert(0, "-");
            timeSeed *= -1;
        }

        length -= id.Length + prefix.Length;

        while (timeSeed > 0)
        {
            id.Insert(0, DOMAIN_CHARACTERS[(int)(timeSeed % DOMAIN_CHARACTERS.Length)]);
            timeSeed /= DOMAIN_CHARACTERS.Length;
        }

        if (pad)
        {
            while (id.Length < length)
            {
                id.Insert(0, "0");
            }
        }

        // 2025-02-10 -- there is a problem with this. It is possible if there are some prefixes that are longer than
        // others, then the random characters could change the prefix to actually match another type.
        // the next line should fix that, as long as we guarantee that the prefix is always letters.
        if (char.IsLetter(id[0]))
        {
            id.Insert(0, ".");
        }

        id = id.Insert(0, prefix);
        return id.ToString();
    }

    /// <summary>
    /// Generates an ID for an entity that implements IHasAutoInsertedId.
    /// </summary>
    /// <param name="entity">The entity requiring an ID.</param>
    /// <returns>A generated ID string using the entity's ID generation properties.</returns>
    public string GenerateId(IHasAutoInsertedId entity)
    {
        return GenerateId(entity.IdPrefix, entity.IdLength, entity.IdPadToLength);
    }

    /// <summary>
    /// Returns a character from the domain characters to use as a suffix.
    /// This method is thread-safe and cycles through available characters.
    /// </summary>
    /// <returns>A single character string to use as a suffix.</returns>
    private static string ReturnSuffixCharacter()
    {
        lock (_lockObject)
        {
            _suffixIndex++;
            if (_suffixIndex >= DOMAIN_CHARACTERS.Length)
            {
                _suffixIndex = 0;
            }
            return DOMAIN_CHARACTERS[_suffixIndex % DOMAIN_CHARACTERS.Length].ToString();
        }
    }
    #endregion
}
