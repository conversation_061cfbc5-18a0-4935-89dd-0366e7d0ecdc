@namespace ProScoring.Blazor.Components.Shared
@using System.Net.Http.Headers

@inject HttpClient HttpClient

<div class="upload-container">
    <h3>File Upload</h3>

    @if (!string.IsNullOrEmpty(_message))
    {
        <div class="alert @_alertClass" role="alert">
            @_message
        </div>
    }

    <InputFile OnChange="HandleFileSelection" class="form-control" />

    @if (_selectedFile != null)
    {
        <div class="mt-3">
            <p>Selected file: @_selectedFile.Name</p>
            <p>Size: @(_selectedFile.Size / 1024) KB</p>
            <button class="btn btn-primary" @oclick="UploadFile" disabled="@_isUploading">
                @if (_isUploading)
                {
                    <span>Uploading...</span>
                }
                else
                {
                    <span>Upload</span>
                }
            </button>
        </div>
    }
</div>

@code {
    private IBrowserFile? _selectedFile;
    private string _message = string.Empty;
    private string _alertClass = string.Empty;
    private bool _isUploading;

    internal async Task HandleFileSelection(InputFileChangeEventArgs e)
    {
        await InvokeAsync(() =>
        {
            _selectedFile = e.File;
            _message = string.Empty;
            StateHasChanged();
        });
    }

    internal async Task UploadFile()
    {
        _isUploading = true;
        _message = string.Empty;

        if (_selectedFile == null)
        {
            _message = "Please select a file first.";
            _alertClass = "alert-warning";
            return;
        }

        try
        {
            using var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(_selectedFile.OpenReadStream());
            fileContent.Headers.ContentType = new MediaTypeHeaderValue(_selectedFile.ContentType);
            content.Add(fileContent, "file", _selectedFile.Name);

            var response = await HttpClient.PostAsync("api/file/upload", content);

            if (response.IsSuccessStatusCode)
            {
                _message = "File uploaded successfully!";
                _alertClass = "alert-success";
                _selectedFile = null;
            }
            else
            {
                _message = $"Error uploading file: {response.ReasonPhrase}";
                _alertClass = "alert-danger";
            }
        }
        catch (Exception ex)
        {
            _message = $"Error uploading file: {ex.Message}";
            _alertClass = "alert-danger";
        }
        finally
        {
            _isUploading = false;
        }
    }
}

<style>
    .upload-container {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        max-width: 600px;
        margin: 0 auto;
    }

    .alert {
        margin-bottom: 1rem;
        padding: 0.75rem 1.25rem;
        border: 1px solid transparent;
        border-radius: 0.25rem;
    }

    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }
</style>
