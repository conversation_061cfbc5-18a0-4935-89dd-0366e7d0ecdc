using System;
using System.Linq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain.Entities;

/// <summary>
/// Contains unit tests for the ApplicationUser class functionality.
/// </summary>
public class ApplicationUserTests
{
    private readonly ILogger<ApplicationUser> _logger;

    public ApplicationUserTests()
    {
        _logger = Substitute.For<ILogger<ApplicationUser>>();
    }

    #region Constructor Tests

    [Fact]
    public void ApplicationUser_DefaultConstructor_SetsIdToNull()
    {
        // Arrange & Act
        var user = new ApplicationUser();

        // Assert
        user.Id.Should().BeNull();
    }

    [Fact]
    public void ApplicationUser_LoggerConstructor_SetsIdToNull()
    {
        // Arrange & Act
        var user = new ApplicationUser(_logger);

        // Assert
        user.Id.Should().BeNull();
    }

    [Fact]
    public void ApplicationUser_LoggerConstructor_WhenThrowsException_ShouldPropagateException()
    {
        // Arrange
        var mockLogger = Substitute.For<ILogger<ApplicationUser>>();
        mockLogger
            .When(l =>
                l.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Throw(new InvalidOperationException("Constructor exception"));

        // Act & Assert
        Action act = () => new ApplicationUser(mockLogger);
        act.Should().Throw<InvalidOperationException>();
    }

    #endregion // Constructor Tests

    #region Property Tests

    [Fact]
    public void Id_WhenSettingValue_ShouldSetBaseId()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.Id = "*********";

        // Assert
        user.Id.Should().Be("*********");
    }

    [Fact]
    public void Id_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        // Set up logger to throw exception after user has been created
        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("Test exception"));

        // Act & Assert
        Action act = () => user.Id = "*********";
        act.Should().Throw<InvalidOperationException>().WithMessage("Test exception");
    }

    [Fact]
    public void GivenName_WhenValueExceedsMaxLength_ShouldTruncateTo50Characters()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var longName = new string('A', 60);

        // Act
        user.GivenName = longName;

        // Assert
        user.GivenName.Should().Be(longName.Substring(0, 50));
        user.GivenName.Length.Should().Be(50);
    }

    [Fact]
    public void GivenName_WhenValueIsWithinMaxLength_ShouldSetValueAsIs()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var name = "John";

        // Act
        user.GivenName = name;

        // Assert
        user.GivenName.Should().Be(name);
    }

    [Fact]
    public void GivenName_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("GivenName exception"));

        // Act & Assert
        Action act = () => user.GivenName = "John";
        act.Should().Throw<InvalidOperationException>().WithMessage("GivenName exception");
    }

    [Fact]
    public void Surname_WhenValueExceedsMaxLength_ShouldTruncateTo50Characters()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var longName = new string('B', 60);

        // Act
        user.Surname = longName;

        // Assert
        user.Surname.Should().Be(longName.Substring(0, 50));
        user.Surname.Length.Should().Be(50);
    }

    [Fact]
    public void Surname_WhenValueIsWithinMaxLength_ShouldSetValueAsIs()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        var name = "Doe";

        // Act
        user.Surname = name;

        // Assert
        user.Surname.Should().Be(name);
    }

    [Fact]
    public void Surname_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("Surname exception"));

        // Act & Assert
        Action act = () => user.Surname = "Doe";
        act.Should().Throw<InvalidOperationException>().WithMessage("Surname exception");
    }

    [Fact]
    public void Name_WhenBothNamesAreNull_ShouldReturnNull()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        user.GivenName = null;
        user.Surname = null;

        // Act & Assert
        user.Name.Should().BeNull();
    }

    [Fact]
    public void Name_WhenOnlyGivenNameExists_ShouldReturnGivenName()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        user.GivenName = "John";
        user.Surname = null;

        // Act & Assert
        user.Name.Should().Be("John");
    }

    [Fact]
    public void Name_WhenOnlySurnameExists_ShouldReturnSurname()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        user.GivenName = null;
        user.Surname = "Doe";

        // Act & Assert
        user.Name.Should().Be("Doe");
    }

    [Fact]
    public void Name_WhenBothNamesExist_ShouldReturnCombinedNameWithSpace()
    {
        // Arrange
        var user = new ApplicationUser(_logger);
        user.GivenName = "John";
        user.Surname = "Doe";

        // Act & Assert
        user.Name.Should().Be("John Doe");
    }

    [Fact]
    public void Name_WhenGetterThrows_ShouldReturnNull()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();

        var user = new ApplicationUser(errorLogger);

        user.GivenName = "John";
        user.Surname = "Doe";

        // Setup logger to throw only on first call
        var callCount = 0;
        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x =>
            {
                if (callCount == 0)
                {
                    callCount++;
                    throw new InvalidOperationException("Test exception");
                }
                // Don't throw on subsequent calls
            });
        // Act & Assert
        user.Name.Should().BeNull();
    }

    [Fact]
    public void UserName_WhenSettingValue_ShouldSetBaseUserName()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.UserName = "johndoe";

        // Assert
        user.UserName.Should().Be("johndoe");
    }

    [Fact]
    public void UserName_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("UserName exception"));

        // Act & Assert
        Action act = () => user.UserName = "johndoe";
        act.Should().Throw<InvalidOperationException>().WithMessage("UserName exception");
    }

    [Fact]
    public void Email_WhenSettingValue_ShouldSetBaseEmail()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.Email = "<EMAIL>";

        // Assert
        user.Email.Should().Be("<EMAIL>");
    }

    [Fact]
    public void Email_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("Email exception"));

        // Act & Assert
        Action act = () => user.Email = "<EMAIL>";
        act.Should().Throw<InvalidOperationException>().WithMessage("Email exception");
    }

    [Fact]
    public void PhoneNumber_WhenSettingValue_ShouldSetBasePhoneNumber()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.PhoneNumber = "1234567890";

        // Assert
        user.PhoneNumber.Should().Be("1234567890");
    }

    [Fact]
    public void PhoneNumber_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("PhoneNumber exception"));

        // Act & Assert
        Action act = () => user.PhoneNumber = "1234567890";
        act.Should().Throw<InvalidOperationException>().WithMessage("PhoneNumber exception");
    }

    [Fact]
    public void AccessFailedCount_WhenSettingValue_ShouldSetBaseAccessFailedCount()
    {
        // Arrange
        var user = new ApplicationUser(_logger);

        // Act
        user.AccessFailedCount = 3;

        // Assert
        user.AccessFailedCount.Should().Be(3);
    }

    [Fact]
    public void AccessFailedCount_WhenSetterThrows_ShouldPropagateException()
    {
        // Arrange
        var errorLogger = Substitute.For<ILogger<ApplicationUser>>();
        var user = new ApplicationUser(errorLogger);

        errorLogger
            .When(x =>
                x.Log(
                    Arg.Any<LogLevel>(),
                    Arg.Any<EventId>(),
                    Arg.Any<object>(),
                    Arg.Any<Exception>(),
                    Arg.Any<Func<object, Exception?, string>>()
                )
            )
            .Do(x => throw new InvalidOperationException("AccessFailedCount exception"));

        // Act & Assert
        Action act = () => user.AccessFailedCount = 3;
        act.Should().Throw<InvalidOperationException>().WithMessage("AccessFailedCount exception");
    }

    #endregion // Property Tests

    #region IHasAutoInsertedId Implementation Tests

    [Fact]
    public void IdPrefix_ShouldReturnCorrectValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act & Assert
        user.IdPrefix.Should().Be("U");
    }

    [Fact]
    public void IdLength_ShouldReturnCorrectValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act & Assert
        user.IdLength.Should().Be(8);
    }

    [Fact]
    public void IdPadToLength_ShouldReturnFalse()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act & Assert
        user.IdPadToLength.Should().BeFalse();
    }

    #endregion // IHasAutoInsertedId Implementation Tests

    #region IHasLastChangeTracking Implementation Tests

    [Fact]
    public void CreatedById_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act
        user.CreatedById = "*********";

        // Assert
        user.CreatedById.Should().Be("*********");
    }

    [Fact]
    public void CreatedAt_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();
        var dateTime = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);

        // Act
        user.CreatedAt = dateTime;

        // Assert
        user.CreatedAt.Should().Be(dateTime);
    }

    [Fact]
    public void UpdatedById_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();

        // Act
        user.UpdatedById = "*********";

        // Assert
        user.UpdatedById.Should().Be("*********");
    }

    [Fact]
    public void UpdatedAt_WhenSettingValue_ShouldStoreValue()
    {
        // Arrange
        var user = new ApplicationUser();
        var dateTime = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);

        // Act
        user.UpdatedAt = dateTime;

        // Assert
        user.UpdatedAt.Should().Be(dateTime);
    }

    #endregion // IHasLastChangeTracking Implementation Tests

    #region SeedData Tests

    [Fact]
    public void SeedData_ShouldContainNullUser()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        seedData.Should().NotBeEmpty();
        seedData.Should().HaveCount(1);

        var nullUser = seedData.First();
        nullUser.Id.Should().Be("U000000000");
        nullUser.UserName.Should().Be("<EMAIL>");
        nullUser.Email.Should().Be("<EMAIL>");
        nullUser.CreatedById.Should().Be("U000000000");
        nullUser.UpdatedById.Should().Be("U000000000");
    }

    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectDates()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.CreatedAt.Should().Be(new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero));
        nullUser.UpdatedAt.Should().Be(new DateTimeOffset(2025, 1, 1, 1, 1, 0, TimeSpan.Zero));
    }

    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectSecuritySettings()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.EmailConfirmed.Should().BeTrue();
        nullUser.PhoneNumberConfirmed.Should().BeFalse();
        nullUser.TwoFactorEnabled.Should().BeFalse();
        nullUser.LockoutEnabled.Should().BeTrue();
        nullUser.AccessFailedCount.Should().Be(0);
        nullUser.SecurityStamp.Should().Be("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX");
        nullUser.ConcurrencyStamp.Should().Be("00000000-0000-0000-0000-000000000000");
    }

    [Fact]
    public void SeedData_NullUser_ShouldHaveCorrectNormalizedValues()
    {
        // Arrange & Act
        var seedData = ApplicationUser.SeedData;

        // Assert
        var nullUser = seedData.First();
        nullUser.NormalizedUserName.Should().Be("<EMAIL>");
        nullUser.NormalizedEmail.Should().Be("<EMAIL>");
    }

    #endregion // SeedData Tests
}
