using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Domain.Entities;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the approval functionality in Organizing Authority pages.
/// </summary>
[Collection(PlaywrightFixture.CollectionName)]
public class OrganizingAuthorityApprovalTests(PlaywrightFixture fixture) : IAsyncLifetime
{
    private readonly PlaywrightFixture _fixture = fixture;
    private IPage? _page;
    private ApplicationUser? _regularUser;
    private ApplicationUser? _hmficUser;

    public async Task InitializeAsync()
    {
        // For testing purposes, we'll use predefined user information
        // instead of actually creating users in the database

        // Set up regular user info
        _regularUser = new ApplicationUser
        {
            Id = "U-test-regular",
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
        };

        // Set up HMFIC user info
        _hmficUser = new ApplicationUser
        {
            Id = "U-test-hmfic",
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
        };

        // Create a page for testing
        _page = await _fixture.CreatePageAsync();
    }

    public async Task DisposeAsync()
    {
        if (_page != null)
        {
            await _page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the approval toggle is visible for HMFIC users but not for regular users.
    /// </summary>
    [Fact]
    public async Task ApprovalToggle_OnlyVisibleToHmficUsers()
    {
        if (_page == null || _regularUser == null || _hmficUser == null)
        {
            throw new InvalidOperationException("Test not properly initialized");
        }

        // First, test with regular user
        await LoginAsync(_regularUser.Email!, "Password123!");

        // Navigate to organizing authorities page
        await _page.GotoAsync($"{_fixture.Settings.BaseUrl}/organizingauthorities");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Click on edit for the first organizing authority
        await _page.ClickAsync("[data-testid='oa-edit-link']");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Check that approval toggle is not visible
        var approvalToggleCount = await _page.Locator("[data-testid='oa-approved-checkbox']").CountAsync();
        approvalToggleCount.Should().Be(0, "Approval toggle should not be visible for regular users");

        // Logout
        await LogoutAsync();

        // Now test with HMFIC user
        await LoginAsync(_hmficUser.Email!, "Password123!");

        // Navigate to organizing authorities page
        await _page.GotoAsync($"{_fixture.Settings.BaseUrl}/organizingauthorities");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Click on edit for the first organizing authority
        await _page.ClickAsync("[data-testid='oa-edit-link']");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Check that approval toggle is visible
        approvalToggleCount = await _page.Locator("[data-testid='oa-approved-checkbox']").CountAsync();
        approvalToggleCount.Should().Be(1, "Approval toggle should be visible for HMFIC users");
    }

    /// <summary>
    /// Tests that the approval column is visible for HMFIC users but not for regular users.
    /// </summary>
    [Fact]
    public async Task ApprovalColumn_OnlyVisibleToHmficUsers()
    {
        if (_page == null || _regularUser == null || _hmficUser == null)
        {
            throw new InvalidOperationException("Test not properly initialized");
        }

        // First, test with regular user
        await LoginAsync(_regularUser.Email!, "Password123!");

        // Navigate to organizing authorities page
        await _page.GotoAsync($"{_fixture.Settings.BaseUrl}/organizingauthorities");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Check that approval column is not visible
        var approvalColumnCount = await _page.Locator("[data-testid='oa-approved-column']").CountAsync();
        approvalColumnCount.Should().Be(0, "Approval column should not be visible for regular users");

        // Logout
        await LogoutAsync();

        // Now test with HMFIC user
        await LoginAsync(_hmficUser.Email!, "Password123!");

        // Navigate to organizing authorities page
        await _page.GotoAsync($"{_fixture.Settings.BaseUrl}/organizingauthorities");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Check that approval column is visible
        approvalColumnCount = await _page.Locator("[data-testid='oa-approved-column']").CountAsync();
        approvalColumnCount.Should().Be(1, "Approval column should be visible for HMFIC users");
    }

    private async Task LoginAsync(string email, string password)
    {
        if (_page == null)
        {
            throw new InvalidOperationException("Page not initialized");
        }

        await _page.GotoAsync(_fixture.Settings.BaseUrl);
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Click login link
        await _page.ClickAsync("text=Login");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Fill login form
        await _page.FillAsync("[data-testid='email-input']", email);
        await _page.FillAsync("[data-testid='password-input']", password);
        await _page.ClickAsync("[data-testid='login-button']");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
    }

    private async Task LogoutAsync()
    {
        if (_page == null)
        {
            throw new InvalidOperationException("Page not initialized");
        }

        // Click on user dropdown menu
        await _page.ClickAsync("[aria-label='Toggle Menu']");
        await _page.WaitForTimeoutAsync(500);

        // Click logout
        await _page.ClickAsync("text=Logout");
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
    }
}
