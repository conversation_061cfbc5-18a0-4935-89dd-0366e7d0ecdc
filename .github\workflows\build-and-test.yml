# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET

on:
  push:
    branches:
      - "Net9_develop"
      - "Net9_main"
  pull_request:
    branches:
      - "Net9_develop"
      - "Net9_main"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 9.0.x

      - name: Cache NuGet packages
        uses: actions/cache@v4
        with:
          path: ~/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/global.json', '**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Install EF Core Tools
        run: dotnet tool install dotnet-ef

      - name: Set PATH
        run: echo "PATH=$PATH:$HOME/.dotnet/tools" >> $GITHUB_ENV

      - name: Restore dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --no-restore

      - name: Test
        run: dotnet test --no-build --verbosity normal

      - name: Set CREATING_MIGRATION to True
        run: echo "CREATING_MIGRATION=True" >> $GITHUB_ENV

      - name: Generate SqLite DB Migrations
        run: dotnet ef migrations add SQLite_TestMigration --no-build --context SqliteCreationApplicationDbContext --startup-project ProScoring.Blazor/ProScoring.Blazor --project ProScoring.Infrastructure/ --output-dir ./Database/SQLite_Migrations

      - name: Generate PostgreSql DB Migrations
        run: dotnet ef migrations add PostgreSql_TestMigration --no-build --context PostgreSqlCreationApplicationDbContext --startup-project ProScoring.Blazor/ProScoring.Blazor --project ProScoring.Infrastructure/ --output-dir ./Database/PostgreSql_Migrations

      - name: Set CREATING_MIGRATION to False
        run: echo "CREATING_MIGRATION=False" >> $GITHUB_ENV
