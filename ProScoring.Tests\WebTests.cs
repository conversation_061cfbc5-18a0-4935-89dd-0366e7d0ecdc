using Microsoft.AspNetCore.Hosting.Server.Features;
using Xunit.Abstractions;

namespace ProScoring.Tests;

public class WebTests
{
    private readonly ITestOutputHelper _output;

    public WebTests(ITestOutputHelper output)
    {
        _output = output;
    }

    // [Fact(Skip = "Skipping this test temporarily. it fails in github actions.")]
    [Fact]
    public async Task GetWebResourceRootReturnsOkStatusCode()
    {
        // Arrange
        var appHost = await DistributedApplicationTestingBuilder.CreateAsync<Projects.ProScoring_AppHost>();
        appHost.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator // ONLY FOR TESTING
            });
            clientBuilder.AddStandardResilienceHandler();
        });
        // To output logs to the xUnit.net ITestOutputHelper, consider adding a package from https://www.nuget.org/packages?q=xunit+logging

        await using var app = await appHost.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        // Act
        var httpClient = app.CreateHttpClient("proscoring-blazor");
        _output.WriteLine($"HttpClient created for {httpClient.BaseAddress}.");

        await resourceNotificationService
            .WaitForResourceAsync("proscoring-blazor", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(30));
        var response = await httpClient.GetAsync("/");
        _output.WriteLine($"Received response with status code {response.StatusCode}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}
