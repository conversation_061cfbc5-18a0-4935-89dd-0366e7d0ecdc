using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Projects;
using ProScoring.Blazor;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.IntegrationTests;

[Collection(nameof(StaticIdGenerationUtilServiceForTesting))]
public class FileUploadIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly ITestOutputHelper _output;

    public FileUploadIntegrationTests(WebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        //_factory = factory.WithWebHostBuilder(builder =>
        //{
        //    builder.ConfigureServices(services =>
        //    {
        //        // Remove the existing DbContext registration
        //        var descriptors = services
        //            .Where(d =>
        //                d.ServiceType.Namespace?.StartsWith("Microsoft.EntityFrameworkCore") == true
        //                || d.ImplementationType?.Namespace?.StartsWith("Microsoft.EntityFrameworkCore") == true
        //                || d.ServiceType == typeof(ApplicationDbContext)
        //                || d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>)
        //                || d.ServiceType == typeof(DbContextOptions)
        //            )
        //            .ToList();

        //        foreach (var descriptor in descriptors)
        //        {
        //            services.Remove(descriptor);
        //        }

        //        services.AddDbContext<ApplicationDbContext>(options =>
        //        {
        //            options.UseSqlite(new SqliteConnection("Data Source=:memory:"));
        //            // options.UseInMemoryDatabase("TestDb_" + Guid.NewGuid().ToString());
        //        });
        //        services.AddSingleton<IIdUtilsService, GuidishIdUtilsService>();
        //        services.AddSingleton<CustomIdValueGenerator>();

        //        // Ensure the database schema is created
        //        var serviceProvider = services.BuildServiceProvider();
        //        using var scope = serviceProvider.CreateScope();
        //        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        //        dbContext.Database.EnsureCreated();
        //    });
        //});
        _output = output;
    }

    #region methods

    [Fact]
    public async Task GoodbyeWorld_ReturnsGoodbyeWorld()
    {
        // Arrange
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        appBuilder.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(
                () =>
                    new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator, // ONLY FOR TESTING
                    }
            );
            clientBuilder.AddStandardResilienceHandler();
        });

        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        var client = app.CreateHttpClient("proscoring-blazor");

        // Act
        await resourceNotificationService
            .WaitForResourceAsync("proscoring-blazor", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(30));

        var response = await client.PostAsync("/api/file/goodbyeworld", null);

        // Assert
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        Assert.Equal("Goodbye World", responseString);
    }

    [Fact]
    public async Task HelloWorld_ReturnsHelloWorld()
    {
        // Arrange

        // var client = _factory.CreateClient();
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        appBuilder.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.ConfigurePrimaryHttpMessageHandler(
                () =>
                    new HttpClientHandler
                    {
                        ServerCertificateCustomValidationCallback =
                            HttpClientHandler.DangerousAcceptAnyServerCertificateValidator, // ONLY FOR TESTING
                    }
            );
            clientBuilder.AddStandardResilienceHandler();
        });
        // To output logs to the xUnit.net ITestOutputHelper, consider adding a package from https://www.nuget.org/packages?q=xunit+logging

        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        var client = app.CreateHttpClient("proscoring-blazor");

        // Act
        await resourceNotificationService
            .WaitForResourceAsync("proscoring-blazor", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(30));

        var response = await client.GetAsync("/api/file/helloworld");

        // Assert
        response.EnsureSuccessStatusCode();
        var responseString = await response.Content.ReadAsStringAsync();
        Assert.Equal("Hello World", responseString);
    }

    [Fact(Skip = "Test needs to be rewritten after all other changes to the webapi, etc.")]
    public async Task UploadFile_SavesToDatabase()
    {
        // TODO: Rewrite this tests so it works after other canges made week ending 2025-01-25

        // Arrange
        // now there is a problem that I need a mock AuthenticationStateProvider

        // var mockedAuthStateProvider = Mock.Of<AuthenticationStateProvider>();
        var appBuilder = await DistributedApplicationTestingBuilder.CreateAsync<ProScoring_AppHost>();
        // var mocker = new AutoMocker();

        appBuilder.Services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseSqlite(new SqliteConnection("Data Source=:memory:"));
        });
        // appBuilder.Services.AddSingleton(mockedAuthStateProvider);
        // appBuilder.Services.AddSingleton(
        //     StaticIdGenerationUtilServiceForTesting.GetInstanceAndConfigure(Mock.Of<GuidishIdGenerationUtilService>())
        // );
        appBuilder.Services.AddSingleton<CustomIdValueGenerator>();
        appBuilder
            .Services.AddHttpClient(
                "proscoring-blazor",
                client =>
                {
                    client.Timeout = TimeSpan.FromSeconds(90);
                }
            )
            .AddStandardResilienceHandler();

        var serviceProvider = appBuilder.Services.BuildServiceProvider();

        await using var app = await appBuilder.BuildAsync();
        var resourceNotificationService = app.Services.GetRequiredService<ResourceNotificationService>();
        await app.StartAsync();
        _output.WriteLine("Application started.");

        using var scope = serviceProvider.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        // Ensure the database schema is created
        await dbContext.Database.EnsureCreatedAsync();

        var client = app.CreateHttpClient("proscoring-blazor");

        var content = new MultipartFormDataContent();
        var fileContent = new ByteArrayContent([1, 2, 3]);
        content.Add(fileContent, "file", "test.jpg");
        content.Add(new StringContent("Test note"), "note");

        // Act
        await resourceNotificationService
            .WaitForResourceAsync("proscoring-blazor", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(90));
        var response = await client.PostAsync("/api/file/upload", content);

        // Assert
        response.EnsureSuccessStatusCode();
        var savedFile = await dbContext.Files.FirstOrDefaultAsync(f =>
            f.TrustedFileNameForDisplay.Contains("test.jpg")
        );
        Assert.NotNull(savedFile);
        // Assert.Equal(3, savedFile.Data.Length);
    }

    #endregion
}
