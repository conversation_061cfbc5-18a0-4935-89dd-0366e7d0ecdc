using Microsoft.Extensions.Logging;
using Proscoring.TestData.Playwright.Helpers;

namespace Proscoring.TestData.Playwright.Operations;

/// <summary>
/// Base class for all UI automation operations.
/// </summary>
/// <typeparam name="T">The type of operation.</typeparam>
public abstract class BaseOperation<T>
    where T : BaseOperation<T>
{
    protected readonly ILogger<T> Logger;
    protected readonly AspireAppHelper AspireHelper;

    protected BaseOperation(ILogger<T> logger, AspireAppHelper aspireHelper)
    {
        Logger = logger;
        AspireHelper = aspireHelper;
    }

    /// <summary>
    /// Executes the operation.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public abstract Task ExecuteAsync();

    /// <summary>
    /// Waits for an element to be visible with a timeout.
    /// </summary>
    /// <param name="selector">The CSS selector to wait for.</param>
    /// <param name="timeoutMs">The timeout in milliseconds.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected async Task WaitForElementAsync(string selector, int timeoutMs = 10000)
    {
        var page = AspireHelper.GetPage();
        await page.WaitForSelectorAsync(selector, new() { Timeout = timeoutMs });
    }
}
