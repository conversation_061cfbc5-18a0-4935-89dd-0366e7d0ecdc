using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler for view operations on entities.
/// </summary>
public class ViewAuthorizationHandler(IAuthorizationProvider authorizationProvider)
    : AuthorizationHandlerForResourceBase<ViewAuthorizationHandler.Requirement>(
        authorizationProvider,
        AuthTypes.Actions.VIEW
    )
{
    public static readonly string PolicyName = nameof(ViewAuthorizationHandler).Replace("Handler", "Policy");

    /// <summary>
    /// The authorization requirement for view operations.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
