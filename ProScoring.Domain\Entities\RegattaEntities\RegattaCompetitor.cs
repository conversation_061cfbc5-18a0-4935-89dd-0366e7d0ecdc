using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents a competitor in a regatta.
/// This entity will not change other than within the regatta.
/// </summary>
public class RegattaCompetitor : RegattaEntityBase<RegattaCompetitor>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaCompetitor IDs.
    /// </summary>
    public const string ID_PREFIX = "RP";

    #endregion Constants

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaCompetitor.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the ID of the competitor this regatta competitor is linked to.
    /// </summary>
    [Column(Order = 20)]
    [ForeignKey(nameof(Competitor))]
    public string? CompetitorId { get; set; }

    /// <summary>
    /// Gets or sets the competitor this regatta competitor is linked to.
    /// </summary>
    public virtual Competitor? Competitor { get; set; }

    /// <summary>
    /// Gets or sets the name of the competitor.
    /// </summary>
    [Column(Order = 30)]
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the email address of the competitor.
    /// </summary>
    [Column(Order = 40)]
    [PersonalData]
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number of the competitor.
    /// </summary>
    [Column(Order = 50)]
    [PersonalData]
    public string? Phone { get; set; }

    /// <summary>
    /// Gets or sets the address of the competitor.
    /// </summary>
    [Column(Order = 60)]
    [PersonalData]
    public string? Address { get; set; }

    /// <summary>
    /// Gets or sets the second line of the address of the competitor.
    /// </summary>
    [Column(Order = 70)]
    [PersonalData]
    public string? Address2 { get; set; }

    /// <summary>
    /// Gets or sets the city of the competitor.
    /// </summary>
    [Column(Order = 80)]
    [PersonalData]
    public string? City { get; set; }

    /// <summary>
    /// Gets or sets the state of the competitor.
    /// </summary>
    [Column(Order = 90)]
    [PersonalData]
    public string? State { get; set; }

    /// <summary>
    /// Gets or sets the zip code of the competitor.
    /// </summary>
    [Column(Order = 100)]
    [PersonalData]
    public string? ZipCode { get; set; }

    /// <summary>
    /// Gets or sets the country of the competitor.
    /// </summary>
    [Column(Order = 110)]
    [PersonalData]
    public string? Country { get; set; }

    /// <summary>
    /// Gets or sets the birth date of the competitor.
    /// </summary>
    [Column(Order = 120)]
    [PersonalData]
    public DateOnly? BirthDate { get; set; }

    /// <summary>
    /// Gets or sets the World Sailing number of the competitor.
    /// </summary>
    [Column(Order = 130)]
    [PersonalData]
    public string? WorldSailingNumber { get; set; }

    /// <summary>
    /// Gets or sets the Member National Authority of the competitor.
    /// </summary>
    [Column(Order = 140)]
    [PersonalData]
    public string? MemberNationalAuthority { get; set; }

    /// <summary>
    /// Gets or sets the MNA number of the competitor.
    /// </summary>
    [Column(Order = 150)]
    [PersonalData]
    public string? MnaNumber { get; set; }

    /// <summary>
    /// Gets or sets the name of the first emergency contact for the competitor.
    /// </summary>
    [Column(Order = 160)]
    [PersonalData]
    public string? EmergencyContactName1 { get; set; }

    /// <summary>
    /// Gets or sets the phone number of the first emergency contact for the competitor.
    /// </summary>
    [Column(Order = 170)]
    [PersonalData]
    public string? EmergencyContactPhone1 { get; set; }

    /// <summary>
    /// Gets or sets the name of the second emergency contact for the competitor.
    /// </summary>
    [Column(Order = 180)]
    [PersonalData]
    public string? EmergencyContactName2 { get; set; }

    /// <summary>
    /// Gets or sets the phone number of the second emergency contact for the competitor.
    /// </summary>
    [Column(Order = 190)]
    [PersonalData]
    public string? EmergencyContactPhone2 { get; set; }

    /// <summary>
    /// Gets or sets any notes related to the competitor.
    /// </summary>
    [Column(Order = 200)]
    public string? Notes { get; set; }

    /// <summary>
    /// Gets or sets the collection of boat-competitor relationships this competitor is part of.
    /// </summary>
    public virtual ICollection<RegattaBoatCompetitor> RegattaBoatCompetitors { get; set; } =
        new List<RegattaBoatCompetitor>();

    #endregion Properties
}
