{
  "ConnectionStrings": {
    "SQLiteConnection": "Data Source=../../proscoring.db",
    "PostgreSqlConnection": "use aspire provided connection string"
  },
  "Database": {
    "UseSqLite": "True"
  },
  "EmailSender": {
    "Enabled": "False"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "LocalStorage": {
    "ExpirationTimeoutMinutes": 60 // 1 hour for development
  },
  "DetailedErrors": true
}
