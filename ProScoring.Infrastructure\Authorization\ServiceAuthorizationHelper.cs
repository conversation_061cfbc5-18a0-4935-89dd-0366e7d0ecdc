using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Helper class for handling authorization in service methods.
/// </summary>
public class ServiceAuthorizationHelper
{
    private readonly IAuthorizationService _authorizationService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<ServiceAuthorizationHelper> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="ServiceAuthorizationHelper"/> class.
    /// </summary>
    /// <param name="authorizationService">The authorization service.</param>
    /// <param name="httpContextAccessor">The HTTP context accessor.</param>
    /// <param name="logger">The logger.</param>
    public ServiceAuthorizationHelper(
        IAuthorizationService authorizationService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<ServiceAuthorizationHelper> logger
    )
    {
        _authorizationService = authorizationService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    /// <summary>
    /// Authorizes the current user against a specified policy.
    /// </summary>
    /// <param name="policyName">The policy name to check.</param>
    /// <param name="resource">Optional resource to check.</param>
    /// <returns>True if authorized, false otherwise.</returns>
    /// <exception cref="UnauthorizedAccessException">Thrown when authorization fails.</exception>
    public async Task AuthorizeAsync(string policyName, object? resource = null)
    {
        var user = _httpContextAccessor.HttpContext?.User;
        if (user == null)
        {
            _logger.LogWarning("No authenticated user found when checking policy {PolicyName}", policyName);
            throw new UnauthorizedAccessException($"Authentication required for {policyName}");
        }

        _logger.LogDebug(
            "Authorizing user {UserId} against policy {PolicyName}",
            user.Identity?.Name ?? "unknown",
            policyName
        );

        var result = await _authorizationService.AuthorizeAsync(user, resource, policyName);

        if (!result.Succeeded)
        {
            _logger.LogWarning(
                "User {UserId} failed authorization check for policy {PolicyName}",
                user.Identity?.Name ?? "unknown",
                policyName
            );
            throw new UnauthorizedAccessException($"Not authorized for {policyName}");
        }

        _logger.LogDebug(
            "User {UserId} successfully authorized for policy {PolicyName}",
            user.Identity?.Name ?? "unknown",
            policyName
        );
    }
}
