using System;
using System.Collections.Generic;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;

namespace ProScoring.Blazor.Services;

/// <summary>
/// Client for making HTTP requests to the OrganizingAuthorityController.
/// </summary>
public class OrganizingAuthorityHttpClient : IOrganizingAuthorityHttpClient
{
    #region fields
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    #endregion fields

    #region constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="OrganizingAuthorityHttpClient"/> class.
    /// </summary>
    /// <param name="httpClient">The HTTP client.</param>
    /// <param name="configuration">Optional configuration to get base URL.</param>
    public OrganizingAuthorityHttpClient(HttpClient httpClient, IConfiguration? configuration = null)
    {
        _httpClient = httpClient;

        // The BaseAddress should already be set by the HttpClient factory in Program.cs
        // This is just a fallback in case it's not set
        if (_httpClient.BaseAddress == null)
        {
            // Try to get the base URL from configuration
            var baseUrl = "http://localhost";

            if (configuration != null)
            {
                // Try to get the base URL from configuration
                var configUrl = configuration["BaseUrl"] ?? configuration["Urls"]?.Split(';').FirstOrDefault();
                if (!string.IsNullOrEmpty(configUrl))
                {
                    baseUrl = configUrl;
                }
            }

            _httpClient.BaseAddress = new Uri(baseUrl);
        }

        _jsonOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
    }
    #endregion constructors

    #region nested types
    /// <summary>
    /// Helper class for deserializing OData responses.
    /// </summary>
    /// <typeparam name="T">The type of items in the response.</typeparam>
    private class ODataResponse<T>
    {
        /// <summary>
        /// Gets or sets the context URL.
        /// </summary>
        public string? Context { get; set; }

        /// <summary>
        /// Gets or sets the value (collection of items).
        /// </summary>
        public List<T>? Value { get; set; }

        /// <summary>
        /// Gets or sets the OData count.
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("@odata.count")]
        public int? OdataCount { get; set; }
    }
    #endregion nested types

    #region methods

    /// <summary>
    /// Gets organizing authorities using OData query.
    /// </summary>
    /// <param name="query">The OData query string.</param>
    /// <returns>A list of organizing authorities that match the query.</returns>
    public async Task<(IEnumerable<OrganizingAuthority>, int TotalCount)> GetWithODataQueryAsync(string query)
    {
        try
        {
            // Ensure the query starts with a ? if it's not empty
            if (!string.IsNullOrEmpty(query) && !query.StartsWith('?'))
            {
                query = "?" + query;
            }

            var response = await _httpClient.GetAsync($"odata/OrganizingAuthorities{query}");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<ODataResponse<OrganizingAuthority>>(_jsonOptions);

            // Extract the count from the response headers if available
            // Try different possible header names for OData count
            string? countHeader = null;

            // Check for OData-Count header (most common)
            if (response.Headers.TryGetValues("OData-Count", out var countValues))
            {
                countHeader = countValues.FirstOrDefault();
            }
            // Check for OData-EntityCount header (alternative)
            else if (response.Headers.TryGetValues("OData-EntityCount", out var entityCountValues))
            {
                countHeader = entityCountValues.FirstOrDefault();
            }
            // Check for @odata.count in the response content
            else if (result != null)
            {
                // Try to get count from response content using reflection
                var odataCountProperty =
                    result.GetType().GetProperty("OdataCount")
                    ?? result.GetType().GetProperty("odatacount")
                    ?? result.GetType().GetProperty("count");

                if (odataCountProperty != null)
                {
                    var odataCount = odataCountProperty.GetValue(result)?.ToString();
                    if (!string.IsNullOrEmpty(odataCount))
                    {
                        countHeader = odataCount;
                    }
                }
            }

            // Parse the count header, defaulting to 0 if parsing fails
            _ = int.TryParse(countHeader, out var totalCount);

            return (result?.Value ?? [], totalCount);
        }
        catch (HttpRequestException ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Error connecting to the organizing authority OData API: {ex.Message}",
                ex
            );
        }
        catch (Exception ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Unexpected error when fetching organizing authorities with OData query: {ex.Message}",
                ex
            );
        }
    }

    /// <summary>
    /// Gets organizing authority information DTOs and total count using OData query.
    /// </summary>
    /// <param name="query">The OData query string.</param>
    /// <returns>An object containing a list of organizing authority information DTOs and the total count.</returns>
    public async Task<(IEnumerable<OrganizingAuthorityInfoDto> Items, int TotalCount)> GetInfoWithODataQueryAsync(
        string query
    )
    {
        try
        {
            // Ensure the query starts with a ? if it's not empty
            if (!string.IsNullOrEmpty(query) && !query.StartsWith('?'))
            {
                query = "?" + query;
            }

            // Add $select for OrganizingAuthorityInfoDto properties if not already present
            if (!query.Contains("$select="))
            {
                var separator = query.Length > 1 ? "&" : "";
                query += $"{separator}$select=Id,Name,Email,Phone,Website,City,State,Country,ImageId,Private,Approved";
            }

            // Use the OrganizingAuthorities endpoint with $select instead of the deprecated OrganizingAuthorityInfos endpoint
            var response = await _httpClient.GetAsync($"odata/OrganizingAuthorities{query}");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<ODataResponse<OrganizingAuthorityInfoDto>>(
                _jsonOptions
            );

            // Extract the count from the response headers if available
            // Try different possible header names for OData count
            string? countHeader = null;

            // Check for OData-Count header (most common)
            if (response.Headers.TryGetValues("OData-Count", out var countValues))
            {
                countHeader = countValues.FirstOrDefault();
            }
            // Check for OData-EntityCount header (alternative)
            else if (response.Headers.TryGetValues("OData-EntityCount", out var entityCountValues))
            {
                countHeader = entityCountValues.FirstOrDefault();
            }
            // Check for @odata.count in the response content
            else if (result != null)
            {
                // Try to get count from response content using reflection
                var odataCountProperty =
                    result.GetType().GetProperty("OdataCount")
                    ?? result.GetType().GetProperty("odatacount")
                    ?? result.GetType().GetProperty("count");

                if (odataCountProperty != null)
                {
                    var odataCount = odataCountProperty.GetValue(result)?.ToString();
                    if (!string.IsNullOrEmpty(odataCount))
                    {
                        countHeader = odataCount;
                    }
                }
            }

            // Parse the count header, defaulting to 0 if parsing fails
            _ = int.TryParse(countHeader, out var totalCount);

            return (result?.Value ?? [], totalCount);
        }
        catch (HttpRequestException ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Error connecting to the organizing authority OData API: {ex.Message}",
                ex
            );
        }
        catch (Exception ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Unexpected error when fetching organizing authority info with OData query: {ex.Message}",
                ex
            );
        }
    }

    /// <summary>
    /// Gets all unique states from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique states.</returns>
    public async Task<IEnumerable<string>> GetAllStatesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/OrganizingAuthority/states");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<List<string>>(_jsonOptions);
            return result ?? [];
        }
        catch (HttpRequestException ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Error connecting to the organizing authority states API: {ex.Message}",
                ex
            );
        }
        catch (Exception ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException($"Unexpected error when fetching states: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Gets all unique countries from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique countries.</returns>
    public async Task<IEnumerable<string>> GetAllCountriesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/OrganizingAuthority/countries");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<List<string>>(_jsonOptions);
            return result ?? [];
        }
        catch (HttpRequestException ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Error connecting to the organizing authority countries API: {ex.Message}",
                ex
            );
        }
        catch (Exception ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException($"Unexpected error when fetching countries: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Gets the total count of organizing authorities.
    /// </summary>
    /// <returns>The total number of organizing authorities.</returns>
    public async Task<int> GetTotalCountAsync()
    {
        try
        {
            // Use OData query with $count=true and $top=0 to get only the count without any data
            var query = "?$count=true&$top=0";

            var response = await _httpClient.GetAsync($"odata/OrganizingAuthorities{query}");
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<ODataResponse<OrganizingAuthorityInfoDto>>(
                _jsonOptions
            );

            // Extract the count from the response headers or content
            string? countHeader = null;

            // Check for OData-Count header (most common)
            if (response.Headers.TryGetValues("OData-Count", out var countValues))
            {
                countHeader = countValues.FirstOrDefault();
            }
            // Check for OData-EntityCount header (alternative)
            else if (response.Headers.TryGetValues("OData-EntityCount", out var entityCountValues))
            {
                countHeader = entityCountValues.FirstOrDefault();
            }
            // Check for @odata.count in the response content
            else if (result != null)
            {
                // Try to get count from response content using reflection
                var odataCountProperty =
                    result.GetType().GetProperty("OdataCount")
                    ?? result.GetType().GetProperty("odatacount")
                    ?? result.GetType().GetProperty("count");

                if (odataCountProperty != null)
                {
                    var odataCount = odataCountProperty.GetValue(result)?.ToString();
                    if (!string.IsNullOrEmpty(odataCount))
                    {
                        countHeader = odataCount;
                    }
                }
            }

            // Parse the count header, defaulting to 0 if parsing fails
            _ = int.TryParse(countHeader, out var totalCount);

            return totalCount;
        }
        catch (HttpRequestException ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException(
                $"Error connecting to the organizing authority OData API: {ex.Message}",
                ex
            );
        }
        catch (Exception ex)
        {
            // Log the error or handle it as needed
            throw new InvalidOperationException($"Unexpected error when fetching total count: {ex.Message}", ex);
        }
    }
    #endregion methods
}
