using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Tests.Infrastructure;

public class ApplicationDbContextTestsFixture : IDisposable
{
    private ServiceCollection? Services { get; set; }

    private readonly ApplicationDbContext? _dbContext;

    public ApplicationDbContextTestsFixture()
    {
        // Set up the services for creating the db context. I don't want to use
        // the same DB context for each test, so we need to do the setup in the
        // test class, not the fixture.

        // This code in this class is not used, but I'm keeping it for now because
        // I may want to remember how to do it later.
    }

    #region IDisposable
    private bool _disposed;

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }
        if (disposing)
        {
            // Dispose managed resources
            _dbContext?.Dispose();
        }
        // Dispose unmanaged resources
        _disposed = true;
    }

    #endregion
}
