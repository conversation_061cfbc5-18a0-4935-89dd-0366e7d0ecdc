using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents equipment used in a regatta, particularly useful for boat rotations.
/// </summary>
public class RegattaEquipment : RegattaEntityBase<RegattaEquipment>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaEquipment IDs.
    /// </summary>
    public const string ID_PREFIX = "RE";

    #endregion Constants

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaEquipment.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    #endregion Properties
}
