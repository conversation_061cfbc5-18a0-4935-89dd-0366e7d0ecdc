using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Database;

internal static class DbContextExtensions
{
    /// <summary>
    /// Creates a typed EntityTypeBuilder for the specified entity type.
    /// </summary>
    internal static object CreateTypedEntityBuilder(this ModelBuilder builder, Type entityType)
    {
        // there should be a way to make this a more specific type.
        return typeof(ModelBuilder)
                .GetMethod(nameof(ModelBuilder.Entity), Type.EmptyTypes)!
                .MakeGenericMethod(entityType)
                .Invoke(builder, null)
            ?? throw new InvalidOperationException($"Failed to create EntityTypeBuilder for {entityType.Name}");
    }

    /// <summary>
    /// Configures auto-generated IDs for entities implementing IHasAutoInsertedId.
    /// </summary>
    internal static void ConfigureAutoGeneratedId(
        this IMutableEntityType entityType,
        EntityTypeBuilder entity,
        ValueGenerator<string>? idValueGenerator
    )
    {
        if (typeof(IHasAutoInsertedId).IsAssignableFrom(entityType.ClrType))
        {
            entity.Property("Id").ValueGeneratedOnAdd().HasValueGenerator((_, __) => idValueGenerator!);
        }
    }

    /// <summary>
    /// Configures auto-generated IDs for entities implementing IHasAutoInsertedId.
    /// But does not define the ValueGenerator.
    /// </summary>
    internal static void ConfigureAutoGeneratedId(this IMutableEntityType entityType, EntityTypeBuilder entity)
    {
        if (typeof(IHasAutoInsertedId).IsAssignableFrom(entityType.ClrType))
        {
            entity.Property("Id").ValueGeneratedOnAdd();
        }
    }

    /// <summary>
    /// Configures compound keys for entities implementing IHasCompoundKey<T>.
    /// </summary>
    internal static void ConfigureCompoundKeys(this IMutableEntityType entityType, object typedEntityBuilder)
    {
        var hasCompoundKeyInterfaceType = entityType.ClrType.GetGenericInterface(typeof(IHasCompoundKey<>));
        if (hasCompoundKeyInterfaceType == null)
            return;

        // Get the DefineCompoundKey method implementation
        var defineCompoundKeyMethod = entityType.ClrType.GetInterfaceMethod(
            hasCompoundKeyInterfaceType,
            nameof(IHasCompoundKey<object>.DefineCompoundKey)
        );

        var keyExpression =
            defineCompoundKeyMethod.Invoke(typedEntityBuilder, null)
            ?? throw new InvalidOperationException($"DefineCompoundKey returned null for {entityType.ClrType.Name}");

        // Apply the compound key configuration
        var hasKeyMethod = typedEntityBuilder.GetEntityBuilderMethod(nameof(EntityTypeBuilder<object>.HasKey));
        hasKeyMethod.Invoke(typedEntityBuilder, [keyExpression]);
    }

    /// <summary>
    /// Configures foreign keys for entities implementing IHasForeignKeyConfiguration<T>.
    /// </summary>
    internal static void ConfigureForeignKeys(this IMutableEntityType entityType, object typedEntityBuilder)
    {
        var hasForeignKeyConfigurationInterfaceType = entityType.ClrType.GetGenericInterface(
            typeof(IHasForeignKeyConfiguration<>)
        );
        if (hasForeignKeyConfigurationInterfaceType == null)
            return;

        var configureForeignKeysMethod = entityType.ClrType.GetInterfaceMethod(
            hasForeignKeyConfigurationInterfaceType,
            nameof(IHasForeignKeyConfiguration<object>.ConfigureForeignKeys)
        );

        configureForeignKeysMethod.Invoke(typedEntityBuilder, [typedEntityBuilder]);
    }

    /// <summary>
    /// Configures seed data for entities implementing IHasInitialSeedData<T>.
    /// </summary>
    internal static void ConfigureSeedData(this IMutableEntityType entityType, object typedEntityBuilder)
    {
        var hasSeedDataInterfaceType = entityType.ClrType.GetGenericInterface(typeof(IHasInitialSeedData<>));
        if (hasSeedDataInterfaceType == null)
            return;

        // Get the SeedData property
        var seedDataProperty = entityType.ClrType.GetInterfaceMethod(
            hasSeedDataInterfaceType,
            $"get_{nameof(IHasInitialSeedData<object>.SeedData)}"
        );

        var seedData =
            seedDataProperty.Invoke(typedEntityBuilder, null) as object[]
            ?? throw new InvalidOperationException($"SeedData property returned null for {entityType.ClrType.Name}");

        // Apply the seed data configuration
        var hasDataMethod = typedEntityBuilder.GetEntityBuilderMethod(nameof(EntityTypeBuilder<object>.HasData));
        hasDataMethod.Invoke(typedEntityBuilder, [seedData]);
    }

    /// <summary>
    /// Gets the generic interface type if the entity implements it.
    /// </summary>
    private static Type? GetGenericInterface(this Type entityType, Type interfaceType)
    {
        return entityType
            .GetInterfaces()
            .FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == interfaceType);
    }

    /// <summary>
    /// Gets a method from an interface implementation.
    /// </summary>
    private static MethodInfo GetInterfaceMethod(this Type entityType, Type interfaceType, string methodName)
    {
        return entityType.GetInterfaceMap(interfaceType).TargetMethods.FirstOrDefault(m => m.Name.EndsWith(methodName))
            ?? throw new InvalidOperationException($"Could not find {methodName} method on {entityType.Name}");
    }

    /// <summary>
    /// Gets a method from an EntityTypeBuilder.
    /// </summary>
    private static MethodInfo GetEntityBuilderMethod(this object entityTypeBuilder, string methodName)
    {
        return entityTypeBuilder
                .GetType()
                .GetMethods()
                .FirstOrDefault(m =>
                    m.Name == methodName
                    && m.GetParameters().Length == 1
                    && m.GetParameters()[0].ParameterType.IsGenericType
                )
            ?? throw new InvalidOperationException(
                $"Could not find {methodName} method on {entityTypeBuilder.GetType().Name}"
            );
    }
}
