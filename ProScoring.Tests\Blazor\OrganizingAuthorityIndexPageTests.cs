using System.Security.Claims;
using Bunit;
using Bunit.TestDoubles;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages;
using ProScoring.Blazor.Extensions;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Tests.Helpers;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the OrganizingAuthority Index page.
/// </summary>
public class OrganizingAuthorityIndexPageTests : TestContext
{
    private readonly IOrganizingAuthorityService _mockOrganizingAuthorityService;
    private readonly IAuthorizationService _mockAuthorizationService;
    private readonly NavigationManager _mockNavigationManager;
    private readonly TestAuthenticationStateProvider _authStateProvider;

    public OrganizingAuthorityIndexPageTests()
    {
        _mockOrganizingAuthorityService = Substitute.For<IOrganizingAuthorityService>();
        _mockAuthorizationService = Substitute.For<IAuthorizationService>();
        _mockNavigationManager = Substitute.For<NavigationManager>();
        _authStateProvider = new TestAuthenticationStateProvider();

        Services.AddSingleton(_mockOrganizingAuthorityService);
        Services.AddSingleton(_mockAuthorizationService);
        Services.AddSingleton(_mockNavigationManager);
        Services.AddSingleton<AuthenticationStateProvider>(_authStateProvider);

        // Add authorization services
        Services.AddAuthorization();

        // Set a longer default timeout for WaitForState
        TestContext.DefaultWaitTimeout = TimeSpan.FromSeconds(5);
    }

    [Fact]
    public void ApprovalColumn_NotVisible_ForRegularUser()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority
            {
                Id = "O1",
                Name = "Test Authority 1",
                Approved = true,
            },
            new OrganizingAuthority
            {
                Id = "O2",
                Name = "Test Authority 2",
                Approved = false,
            },
        };

        _mockOrganizingAuthorityService.GetAllAsync().Returns(authorities);
        _mockAuthorizationService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));

        // Set up regular user (not HMFIC)
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Act
        var cut = RenderComponent<ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages.Index>();

        // Assert
        // For regular users, the approval column should not be rendered at all
        // So we should not find any approval icons
        var markup = cut.Markup;
        markup
            .Should()
            .NotContain("data-testid=\"oa-approved-icon\"", "Approval icons should not be visible for regular users");
        markup
            .Should()
            .NotContain(
                "data-testid=\"oa-not-approved-icon\"",
                "Not-approved icons should not be visible for regular users"
            );
    }

    [Fact]
    public void ApprovalColumn_Visible_ForHmficUser()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority
            {
                Id = "O1",
                Name = "Test Authority 1",
                Approved = true,
            },
            new OrganizingAuthority
            {
                Id = "O2",
                Name = "Test Authority 2",
                Approved = false,
            },
        };

        _mockOrganizingAuthorityService.GetAllAsync().Returns(authorities);
        _mockAuthorizationService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));

        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Act
        var cut = RenderComponent<ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages.Index>();

        // Assert
        // For HMFIC users, the approval column should be rendered
        // So we should find approval icons
        var markup = cut.Markup;
        markup.Should().Contain("data-testid=\"oa-approved-icon\"", "Approval icons should be visible for HMFIC users");
    }

    [Fact]
    public void ApprovalIcons_DisplayCorrectly_ForApprovedAndUnapprovedAuthorities()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority
            {
                Id = "O1",
                Name = "Test Authority 1",
                Approved = true,
            },
            new OrganizingAuthority
            {
                Id = "O2",
                Name = "Test Authority 2",
                Approved = false,
            },
        };

        _mockOrganizingAuthorityService.GetAllAsync().Returns(authorities);
        _mockAuthorizationService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));

        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Act
        var cut = RenderComponent<ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages.Index>();

        // Assert
        // Verify that we have both approved and not-approved icons
        var markup = cut.Markup;
        markup.Should().Contain("data-testid=\"oa-approved-icon\"", "Should show approved icon");
        markup.Should().Contain("data-testid=\"oa-not-approved-icon\"", "Should show not-approved icon");
    }
}
