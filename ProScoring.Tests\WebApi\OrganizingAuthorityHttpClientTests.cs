using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using ProScoring.Blazor.Services;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.WebApi;

public class OrganizingAuthorityHttpClientTests
{
    private readonly TestHttpMessageHandler _testHttpMessageHandler;
    private readonly HttpClient _httpClient;
    private readonly OrganizingAuthorityHttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public OrganizingAuthorityHttpClientTests()
    {
        _testHttpMessageHandler = new TestHttpMessageHandler();
        _httpClient = new HttpClient(_testHttpMessageHandler) { BaseAddress = new Uri("http://localhost") };
        _client = new OrganizingAuthorityHttpClient(_httpClient);
        _jsonOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
    }

    // Test HttpMessageHandler for mocking HTTP responses
    private class TestHttpMessageHandler : HttpMessageHandler
    {
        public HttpResponseMessage? ResponseToReturn { get; set; }
        public Func<HttpRequestMessage, bool>? RequestValidator { get; set; }

        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken
        )
        {
            if (RequestValidator != null && !RequestValidator(request))
            {
                return Task.FromResult(new HttpResponseMessage(HttpStatusCode.BadRequest));
            }
            return Task.FromResult(ResponseToReturn ?? new HttpResponseMessage(HttpStatusCode.NotFound));
        }
    }

    [Fact]
    public async Task GetWithODataQueryAsync_ReturnsAuthorities_WhenApiCallSucceeds()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthority { Id = "O2", Name = "Authority 2" },
        };

        var response = new { Context = "http://localhost/odata/$metadata#OrganizingAuthority", Value = authorities };

        var responseJson = JsonSerializer.Serialize(response, _jsonOptions);

        _testHttpMessageHandler.RequestValidator = req =>
            req.Method == HttpMethod.Get && req.RequestUri!.ToString().Contains("odata/OrganizingAuthorities");
        _testHttpMessageHandler.ResponseToReturn = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(responseJson),
        };

        // Act
        var result = await _client.GetWithODataQueryAsync("$filter=contains(Name,'Authority')");

        // Assert
        result.Should().NotBeNull();
        result.Item1.Should().HaveCount(2);
        result.Item1.First().Id.Should().Be("O1");
        result.Item1.Last().Id.Should().Be("O2");
        result.TotalCount.Should().Be(0); // No count header in this test
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_ReturnsAuthorityInfoDtos_WhenApiCallSucceeds()
    {
        // Arrange
        var dtos = new List<OrganizingAuthorityInfoDto>
        {
            new OrganizingAuthorityInfoDto { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthorityInfoDto { Id = "O2", Name = "Authority 2" },
        };

        var response = new { Context = "http://localhost/odata/$metadata#OrganizingAuthorityInfo", Value = dtos };

        var responseJson = JsonSerializer.Serialize(response, _jsonOptions);

        _testHttpMessageHandler.RequestValidator = req =>
            req.Method == HttpMethod.Get
            && req.RequestUri!.ToString().Contains("odata/OrganizingAuthorities")
            && req.RequestUri!.ToString().Contains("$select=");
        _testHttpMessageHandler.ResponseToReturn = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(responseJson),
        };

        // Act
        var result = await _client.GetInfoWithODataQueryAsync("$orderby=Name desc");

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.Items.First().Id.Should().Be("O1");
        result.Items.Last().Id.Should().Be("O2");
        result.TotalCount.Should().Be(0); // No count header in this test
    }

    [Fact]
    public async Task GetWithODataQueryAsync_ThrowsException_WhenApiCallFails()
    {
        // Arrange
        _testHttpMessageHandler.ResponseToReturn = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.InternalServerError,
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _client.GetWithODataQueryAsync("$filter=contains(Name,'Authority')")
        );
    }
}
