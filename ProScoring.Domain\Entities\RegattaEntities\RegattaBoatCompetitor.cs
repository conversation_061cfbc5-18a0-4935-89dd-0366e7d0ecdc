using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents the relationship between a RegattaBoat and a RegattaCompetitor in a regatta.
/// This allows tracking which competitors are associated with which boats.
/// </summary>
public class RegattaBoatCompetitor
    : IHasForeignKeyConfiguration<RegattaBoatCompetitor>,
        IHasCompoundKey<RegattaBoatCompetitor>
{
    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for the RegattaBoatCompetitor.
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the RegattaBoat ID that this competitor is associated with.
    /// </summary>
    [ForeignKey(nameof(RegattaBoat))]
    public string? RegattaBoatId { get; set; }

    /// <summary>
    /// Gets or sets the RegattaBoat that this competitor is associated with.
    /// </summary>
    public virtual RegattaBoat? RegattaBoat { get; set; }

    /// <summary>
    /// Gets or sets the RegattaCompetitor ID associated with this boat.
    /// </summary>
    [ForeignKey(nameof(RegattaCompetitor))]
    public string? RegattaCompetitorId { get; set; }

    /// <summary>
    /// Gets or sets the RegattaCompetitor associated with this boat.
    /// </summary>
    public virtual RegattaCompetitor? RegattaCompetitor { get; set; }

    #endregion

    #region DB Configuration

    /// <summary>
    /// Configures the foreign key relationships for the RegattaBoatCompetitor entity.
    /// </summary>
    /// <param name="entity">The entity type builder for RegattaBoatCompetitor.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaBoatCompetitor> entity)
    {
        entity
            .HasOne(rb => rb.RegattaBoat)
            .WithMany(b => b.RegattaBoatCompetitors)
            .HasForeignKey(rb => rb.RegattaBoatId);

        entity
            .HasOne(rb => rb.RegattaCompetitor)
            .WithMany(c => c.RegattaBoatCompetitors)
            .HasForeignKey(rb => rb.RegattaCompetitorId);
    }

    /// <summary>
    /// Defines the compound key for the RegattaBoatCompetitor entity.
    /// </summary>
    /// <returns>Expression defining the compound key properties.</returns>
    public static Expression<Func<RegattaBoatCompetitor, object?>> DefineCompoundKey()
    {
        return item => new { item.RegattaBoatId, item.RegattaCompetitorId };
    }

    #endregion
}
