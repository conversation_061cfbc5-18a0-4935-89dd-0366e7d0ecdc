using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization.Entities;

/// <summary>
/// Represents the association between a user, an authorization action, and a target.
/// </summary>
public class UserAuthAction : IEquatable<UserAuthAction>, IHasCompoundKey<UserAuthAction>
{
    /// <summary>
    /// Gets or sets the authorization action associated with this user authorization action.
    /// </summary>
    public virtual AuthAction AuthAction { get; set; } = null!;

    /// <summary>
    /// Gets or sets the authorization action ID.
    /// </summary>
    [Required]
    [ForeignKey(nameof(AuthAction))]
    [Column(Order = 10)]
    public required string AuthActionName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the target ID.
    /// </summary>
    [Required]
    [Column(Order = 30)]
    public required string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the user associated with this authorization action.
    /// </summary>
    [Column(Order = 40)]
    public virtual ApplicationUser User { get; set; } = null!;

    /// <summary>
    /// Gets or sets the user ID.
    /// </summary>
    [Required]
    [ForeignKey(nameof(User))]
    public required string UserId { get; set; } = string.Empty;

    static Expression<Func<UserAuthAction, object?>> IHasCompoundKey<UserAuthAction>.DefineCompoundKey()
    {
        return item => new
        {
            item.AuthActionName,
            item.UserId,
            item.TargetId,
        };
    }

    #region interfaces

    public bool Equals(UserAuthAction? other)
    {
        if (other is null)
            return false;
        return UserId == other.UserId && TargetId == other.TargetId && AuthActionName == other.AuthActionName;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as UserAuthAction);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(UserId, TargetId, AuthActionName);
    }

    #endregion
}
