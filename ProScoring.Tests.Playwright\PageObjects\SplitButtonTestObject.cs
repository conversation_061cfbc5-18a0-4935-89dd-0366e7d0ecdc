using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Test object for interacting with split buttons in the UI.
/// </summary>
public class SplitButtonTestObject
{
    #region Fields
    private readonly IPage _page;
    private readonly string _selector;
    private readonly Lazy<string> _id;
    #endregion Fields

    #region Constructors
    /// <summary>
    /// Initializes a new instance of the <see cref="SplitButtonTestObject"/> class.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <param name="selector">The CSS selector for the split button.</param>
    public SplitButtonTestObject(IPage page, string selector)
    {
        _page = page;
        _selector = $"div {selector}"; // Ensure the selector is scoped to a div

        _id = new Lazy<string>(() =>
        {
            if (_selector.StartsWith("#"))
            {
                return _selector.Substring(1);
            }
            else
            {
                return GetElementIdAsync().Result;
            }
        });
    }
    #endregion Constructors

    #region Properties
    /// <summary>
    /// Gets the selector for the menu items in the split button.
    /// </summary>
    public string MenuItemsSelector => $"div.rz-splitbutton-menu#popup{_id.Value} ul li";
    #endregion Properties

    #region Methods
    /// <summary>
    /// Gets all menu items within the split button menu.
    /// </summary>
    /// <returns>A collection of element handles representing the menu items.</returns>
    public async Task<IEnumerable<IElementHandle>> GetMenuItemsAsync()
    {
        // Get the menu items using the selector
        var menuItems = await _page.QuerySelectorAllAsync(MenuItemsSelector);
        return menuItems;
    }

    /// <summary>
    /// Gets the ID attribute from the selected element using JavaScript.
    /// </summary>
    /// <returns>The ID attribute of the element or null if not found.</returns>
    private async Task<string> GetElementIdAsync()
    {
        return await _page.EvaluateAsync<string>($"document.querySelector('{_selector}').getAttribute('id')");
    }

    /// <summary>
    /// Checks if the split button is visible.
    /// </summary>
    /// <returns>True if the split button is visible, false otherwise.</returns>
    public async Task<bool> IsVisibleAsync()
    {
        return await _page.IsVisibleAsync(_selector);
    }

    /// <summary>
    /// Clicks the split button.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickAsync()
    {
        await _page.ClickAsync(_selector);
    }

    /// <summary>
    /// Clicks the main button part of the split button.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickButtonPartAsync()
    {
        await _page.ClickAsync($"{_selector} button:first-child");
    }

    /// <summary>
    /// Clicks the menu part of the split button (dropdown trigger).
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickMenuPartAsync()
    {
        await _page.ClickAsync($"{_selector} button:nth-child(2)");
    }
    #endregion Methods
}
