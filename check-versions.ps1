# Script to check current versions in AssemblyInfo.cs files
# This script displays the current version information from all AssemblyInfo.cs files in the project

Write-Host "Checking versions in AssemblyInfo.cs files..." -ForegroundColor Cyan

# Find all AssemblyInfo.cs files in the current directory and subdirectories
$assemblyInfoFiles = Get-ChildItem -Path . -Filter "AssemblyInfo.cs" -Recurse

Write-Host "Found $($assemblyInfoFiles.Count) AssemblyInfo.cs files" -ForegroundColor Green

# Function to extract version information from a file
function Get-VersionInfo {
    param (
        [string]$FilePath
    )

    $result = @{
        Path                 = $FilePath
        Version              = "Not found"
        InformationalVersion = "Not found"
    }

    try {
        $content = Get-Content -Path $FilePath -ErrorAction Stop

        # Extract AssemblyVersion
        $versionLine = $content | Where-Object { $_ -match 'AssemblyVersion\(' }
        if ($versionLine) {
            $versionMatch = [regex]::Match($versionLine, 'AssemblyVersion\("([^"]+)"')
            if ($versionMatch.Success) {
                $result.Version = $versionMatch.Groups[1].Value
            }
        }

        # Extract AssemblyInformationalVersion
        $infoVersionLine = $content | Where-Object { $_ -match 'AssemblyInformationalVersion\(' }
        if ($infoVersionLine) {
            $infoVersionMatch = [regex]::Match($infoVersionLine, 'AssemblyInformationalVersion\("([^"]+)"')
            if ($infoVersionMatch.Success) {
                $result.InformationalVersion = $infoVersionMatch.Groups[1].Value
            }
        }
    }
    catch {
        Write-Host "Error reading file ${FilePath}: $($_.Exception.Message)" -ForegroundColor Red
    }

    return $result
}

# Check the current version in each AssemblyInfo.cs file
Write-Host "`nCurrent versions in AssemblyInfo.cs files:" -ForegroundColor Cyan
foreach ($file in $assemblyInfoFiles) {
    $versionInfo = Get-VersionInfo -FilePath $file.FullName

    Write-Host "  $($file.FullName)" -ForegroundColor Yellow
    Write-Host "    Version: $($versionInfo.Version)"
    Write-Host "    InformationalVersion: $($versionInfo.InformationalVersion)"
}

Write-Host "`nCheck completed." -ForegroundColor Green
