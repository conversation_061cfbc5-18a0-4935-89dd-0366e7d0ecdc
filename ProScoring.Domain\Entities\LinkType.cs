using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents link type for regattas and future uses.
/// This class defines the types of links that can be associated with regattas,
/// such as social media links or tracking links.
/// </summary>
public class LinkType : IHasInitialSeedData<LinkType>
{
    #region Properties

    /// <summary>
    /// Gets or sets the unique identifier for the link type.
    /// </summary>
    [Key]
    [Column(Order = 10)]
    [StringLength(25)]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the display name of the link type.
    /// </summary>
    [Column(Order = 20)]
    public string Name { get; set; } = string.Empty;

    #endregion Properties

    #region SeedData

    /// <summary>
    /// Gets the initial seed data for link types.
    /// </summary>
    public static LinkType[] SeedData =>
        [
            new LinkType { Id = "facebook", Name = "Facebook Link" },
            new LinkType { Id = "instagram", Name = "Instagram Link" },
            new LinkType { Id = "bluesky", Name = "BlueSky Link" },
            new LinkType { Id = "twitter", Name = "X/Twitter Link" },
            new LinkType { Id = "whatsapp", Name = "WhatsApp Link" },
            new LinkType { Id = "tracker", Name = "Tracker Link" },
        ];

    #endregion SeedData
}
