using System;
using System.Collections.Generic; // For IReadOnlyList if used in logger, and for general collections
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    // Stub class for testing
    public class StubHasAutoInsertedId : IHasAutoInsertedId
    {
        public string? Id { get; set; }
        public int IdLength => 8;
        public bool IdPadToLength => false;
        public string IdPrefix => "TEST";
    }

    public class CustomIdValueGeneratorTests : IDisposable
    {
        private readonly IIdGenerationUtilService _mockIdUtilsService; // Changed from Mock<T>
        private readonly ILogger<CustomIdValueGenerator> _mockLogger; // Changed from Mock<T>
        private readonly CustomIdValueGenerator _generator;
        private readonly TestDbContext _dbContext;

        public CustomIdValueGeneratorTests()
        {
            _mockIdUtilsService = Substitute.For<IIdGenerationUtilService>();
            _mockLogger = Substitute.For<ILogger<CustomIdValueGenerator>>();
            _generator = new CustomIdValueGenerator(_mockIdUtilsService, _mockLogger); // Removed .Object

            var options = new DbContextOptionsBuilder<TestDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            _dbContext = new TestDbContext(options);
        }

        // Helper to create a real EntityEntry using DbContext
        private EntityEntry CreateEntityEntry(object entity)
        {
            return _dbContext.Entry(entity);
        }

        // Test DbContext for creating EntityEntry objects
        private class TestDbContext : DbContext
        {
            public TestDbContext(DbContextOptions<TestDbContext> options)
                : base(options) { }

            public DbSet<StubHasAutoInsertedId> StubEntities { get; set; }
            public DbSet<PlainTestEntity> PlainEntities { get; set; }
        }

        // Plain entity that doesn't implement IHasAutoInsertedId
        private class PlainTestEntity
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }

        [Fact]
        public void Next_WhenEntityImplementsIHasAutoInsertedId_CallsIdUtilsService()
        {
            // Arrange
            var stubEntity = new StubHasAutoInsertedId();
            var entityEntry = CreateEntityEntry(stubEntity);

            var expectedId = "test-id";
            _mockIdUtilsService.GenerateId(stubEntity).Returns(expectedId); // Changed setup

            // Act
            var actualId = _generator.Next(entityEntry); // Removed .Object

            // Assert
            _mockIdUtilsService.Received(1).GenerateId(Arg.Is<IHasAutoInsertedId>(e => e == stubEntity)); // Changed verification
            Assert.Equal(expectedId, actualId);
        }

        [Fact]
        public void Next_WhenEntityDoesNotImplementIHasAutoInsertedId_ReturnsGuidAndLogsWarning()
        {
            // Arrange
            var plainEntity = new PlainTestEntity { Name = "Test" };
            var entityEntry = CreateEntityEntry(plainEntity);
            var expectedLogMessagePart =
                $"CustomIdValueGenerator is getting called for an entity of type {plainEntity.GetType().Name} that does not implement IHasAutoInsertedId";

            // Act
            var actualId = _generator.Next(entityEntry); // Removed .Object

            // Assert
            _mockIdUtilsService.DidNotReceive().GenerateId(Arg.Any<IHasAutoInsertedId>()); // Changed verification
            Assert.NotNull(actualId);
            Assert.True(Guid.TryParse(actualId.ToString(), out _), $"Generated ID '{actualId}' was not a valid GUID.");

            _mockLogger
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)), // More direct state check
                    null, // No exception expected
                    Arg.Any<Func<object, Exception?, string>>()
                ); // Formatter
        }

        [Fact]
        public void GeneratesTemporaryValues_IsFalse()
        {
            // Arrange
            // No specific arrangement needed beyond constructor setup

            // Act
            var generatesTemporaryValues = _generator.GeneratesTemporaryValues;

            // Assert
            Assert.False(generatesTemporaryValues);
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
