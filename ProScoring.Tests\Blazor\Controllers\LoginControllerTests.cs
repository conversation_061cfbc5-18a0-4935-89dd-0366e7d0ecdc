using System;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using ProScoring.Blazor.Controllers; // Assuming LoginController and LoginModel are here
using ProScoring.Domain.Entities; // For ApplicationUser
using Xunit;
using IdentitySignInResult = Microsoft.AspNetCore.Identity.SignInResult;

namespace ProScoring.Tests.Blazor.Controllers
{
    public class LoginControllerTests
    {
        private readonly SignInManager<ApplicationUser> _substituteSignInManager;
        private readonly ILogger<LoginController> _substituteLogger;
        private readonly IAntiforgery _substituteAntiforgery;
        private readonly LoginController _controller;

        public LoginControllerTests()
        {
            // Mock UserManager<ApplicationUser> if LoginController depends on it directly for user finding.
            // Assuming SignInManager handles user finding. If not, UserManager needs to be mocked.
            // For now, we'll use a simple UserStore substitute for UserManager if needed.
            var userStoreSubstitute = Substitute.For<IUserStore<ApplicationUser>>();
            var userManagerSubstitute = Substitute.For<UserManager<ApplicationUser>>(
                userStoreSubstitute,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            );

            // If ApplicationUser is needed by PasswordSignInAsync, setup UserManager to return a user.
            var testUser = new ApplicationUser { UserName = "user" };
            userManagerSubstitute.FindByNameAsync("user").Returns(Task.FromResult(testUser));
            userManagerSubstitute.CheckPasswordAsync(testUser, "password").Returns(Task.FromResult(true));

            _substituteSignInManager = Substitute.For<SignInManager<ApplicationUser>>(
                userManagerSubstitute, // UserManager
                Substitute.For<Microsoft.AspNetCore.Http.IHttpContextAccessor>(), // IHttpContextAccessor
                Substitute.For<IUserClaimsPrincipalFactory<ApplicationUser>>(), // IUserClaimsPrincipalFactory
                Substitute.For<IOptions<IdentityOptions>>(), // IOptions<IdentityOptions>
                Substitute.For<ILogger<SignInManager<ApplicationUser>>>(), // ILogger<SignInManager>
                Substitute.For<Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider>(), // IAuthenticationSchemeProvider
                Substitute.For<Microsoft.AspNetCore.Identity.IUserConfirmation<ApplicationUser>>() // IUserConfirmation
            );

            _substituteLogger = Substitute.For<ILogger<LoginController>>();
            _substituteAntiforgery = Substitute.For<IAntiforgery>();

            _controller = new LoginController(_substituteSignInManager, _substituteLogger, _substituteAntiforgery);
        }

        [Fact]
        public async Task Login_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            _controller.ModelState.AddModelError("Error", "Sample error");
            var loginModel = new LoginModel { Username = "user", Password = "password" };

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            _substituteLogger
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains("Invalid model state in login request")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task Login_Successful_ReturnsOkWithSuccessTrue()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "user",
                Password = "password",
                RememberMe = false,
            };
            _substituteSignInManager
                .PasswordSignInAsync(
                    loginModel.Username,
                    loginModel.Password,
                    loginModel.RememberMe,
                    lockoutOnFailure: false
                )
                .Returns(Task.FromResult(IdentitySignInResult.Success));

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result.As<OkObjectResult>();
            okResult.Value.Should().NotBeNull();
            okResult.Value.Should().BeEquivalentTo(new { success = true });

            _substituteLogger
                .Received(1)
                .Log(
                    LogLevel.Information,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains("User user logged in successfully")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task Login_RequiresTwoFactor_ReturnsOkWithRequiresTwoFactorTrue()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "user",
                Password = "password",
                RememberMe = false,
            };
            _substituteSignInManager
                .PasswordSignInAsync(
                    loginModel.Username,
                    loginModel.Password,
                    loginModel.RememberMe,
                    lockoutOnFailure: false
                )
                .Returns(Task.FromResult(IdentitySignInResult.TwoFactorRequired));

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result.As<OkObjectResult>();
            okResult.Value.Should().NotBeNull();
            okResult.Value.Should().BeEquivalentTo(new { success = false, requiresTwoFactor = true });

            _substituteLogger
                .Received(1)
                .Log(
                    LogLevel.Information,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains("User user requires two-factor authentication")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task Login_IsLockedOut_ReturnsOkWithIsLockedOutTrue()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "user",
                Password = "password",
                RememberMe = false,
            };
            _substituteSignInManager
                .PasswordSignInAsync(
                    loginModel.Username,
                    loginModel.Password,
                    loginModel.RememberMe,
                    lockoutOnFailure: false
                )
                .Returns(Task.FromResult(IdentitySignInResult.LockedOut));

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result.As<OkObjectResult>();
            okResult.Value.Should().NotBeNull();
            okResult.Value.Should().BeEquivalentTo(new { success = false, isLockedOut = true });

            _substituteLogger
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains("User account user is locked out")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }

        [Fact]
        public async Task Login_Failed_ReturnsOkWithSuccessFalseAndMessage()
        {
            // Arrange
            var loginModel = new LoginModel
            {
                Username = "user",
                Password = "password",
                RememberMe = false,
            };
            _substituteSignInManager
                .PasswordSignInAsync(
                    loginModel.Username,
                    loginModel.Password,
                    loginModel.RememberMe,
                    lockoutOnFailure: false
                )
                .Returns(Task.FromResult(IdentitySignInResult.Failed));

            // Act
            var result = await _controller.Login(loginModel);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result.As<OkObjectResult>();
            okResult.Value.Should().NotBeNull();
            // Check for success=false and the presence of a message property
            okResult.Value.GetType().GetProperty("success")?.GetValue(okResult.Value, null).Should().Be(false);
            okResult.Value.GetType().GetProperty("message")?.GetValue(okResult.Value, null).Should().NotBeNull();
            // Or more specific if message is known:
            // okResult.Value.Should().BeEquivalentTo(new { success = false, message = "Invalid login attempt." });


            _substituteLogger
                .Received(1)
                .Log(
                    LogLevel.Warning,
                    Arg.Any<EventId>(),
                    Arg.Is<object>(state => state.ToString().Contains("Invalid login attempt for user user")),
                    null,
                    Arg.Any<Func<object, Exception, string>>()
                );
        }
    }
}
