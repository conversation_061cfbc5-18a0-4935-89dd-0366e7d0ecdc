using System.Net;
using System.Text.RegularExpressions;
using HeyRed.Mime;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Service for handling file operations such as upload, download, and retrieval.
/// </summary>
public partial class FileService(
    IHostEnvironment environment,
    IOptions<FileUploadOptions> fileUploadOptions,
    ApplicationDbContext context,
    IDateTimeOffsetProvider dateTimeOffsetProvider,
    ILogger<FileService> logger
) : IFileService
{
    #region fields
    private readonly FileUploadOptions? _fileUploadOptions = fileUploadOptions.Value;
    #endregion

    #region methods
    /// <summary>
    /// Creates a file upload result with an error code and message.
    /// </summary>
    /// <param name="errorCode">The error code to set.</param>
    /// <param name="message">The error message to log.</param>
    /// <returns>A FileUploadResult with the specified error code.</returns>
    private FileUploadResult CreateUploadResult(FileUploadResult.ErrorCodes errorCode, string message)
    {
        var uploadResult = new FileUploadResult { ErrorCode = (int)errorCode };
        logger.LogInformation("{Message} (Err:{ErrorCode})", message, uploadResult.ErrorCode);
        return uploadResult;
    }

    /// <summary>
    /// Downloads a file and returns it as a data URI.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>A data URI representation of the file.</returns>
    /// <exception cref="FileNotFoundException">Thrown when the file is not found.</exception>
    public async Task<string> DownloadAsDataUriAsync(string id)
    {
        var file = await context.Files.FindAsync(id);
        if (file == null)
            throw new FileNotFoundException("File not found.");

        var fileBytes = await System.IO.File.ReadAllBytesAsync(file.Path);
        var base64Data = Convert.ToBase64String(fileBytes);
        var dataUri = $"data:{file.ContentType};base64,{base64Data}";

        return dataUri;
    }

    /// <summary>
    /// Downloads a file and returns it as a stream.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>A FileDownloadResult containing the file stream and metadata.</returns>
    /// <exception cref="FileNotFoundException">Thrown when the file is not found.</exception>
    public async Task<FileDownloadResult> DownloadAsync(string id)
    {
        var file = await context.Files.FindAsync(id);
        if (file == null)
            throw new FileNotFoundException("File not found.");

        var stream = new FileStream(file.Path, FileMode.Open, FileAccess.Read, FileShare.Read);
        return new FileDownloadResult
        {
            Stream = stream,
            ContentType = file.ContentType,
            FileName = file.UntrustedName,
        };
    }

    /// <summary>
    /// Ensures that the specified directory exists.
    /// </summary>
    /// <param name="directoryPath">The path of the directory to check/create.</param>
    private void EnsureDirectoryExists(string? directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath!);
            logger.LogInformation("Created directory {Directory}", directoryPath);
        }
    }

    /// <summary>
    /// Retrieves a file record by its ID.
    /// </summary>
    /// <param name="id">The ID of the file record to retrieve.</param>
    /// <returns>The file record.</returns>
    /// <exception cref="FileNotFoundException">Thrown when the file record is not found.</exception>
    public async Task<FileRecord> GetFileRecordAsync(string id)
    {
        var fileRecord = await context.Files.FindAsync(id);
        if (fileRecord == null)
            throw new FileNotFoundException("File not found.");
        return fileRecord;
    }

    /// <summary>
    /// Validates the length of the file.
    /// </summary>
    /// <param name="length">The length of the file in bytes.</param>
    /// <param name="trustedFileNameForDisplay">The trusted file name for display.</param>
    /// <returns>A <see cref="FileUploadResult"/> indicating the result of the validation, or null if the file length is valid.</returns>
    private FileUploadResult? IsFileLengthInvalid(long length, string trustedFileNameForDisplay)
    {
        if (length == 0)
        {
            var uploadResult = CreateUploadResult(
                FileUploadResult.ErrorCodes.FileEmpty,
                $"{trustedFileNameForDisplay} is empty"
            );
            return uploadResult;
        }

        if (length > _fileUploadOptions!.MaxSize)
        {
            var uploadResult = CreateUploadResult(
                FileUploadResult.ErrorCodes.FileTooLarge,
                $"{trustedFileNameForDisplay} length {length} bytes exceeds the maximum size limit {_fileUploadOptions.MaxSize} bytes"
            );
            return uploadResult;
        }

        return null;
    }

    /// <summary>
    /// Saves file information to the database.
    /// </summary>
    /// <param name="untrustedFileName">The original untrusted file name.</param>
    /// <param name="trustedFileNameForDisplay">The file name that is safe for display.</param>
    /// <param name="path">The path where the file is stored.</param>
    /// <param name="note">Optional note about the file.</param>
    /// <param name="size">The size of the file in bytes.</param>
    /// <param name="contentType">The MIME type of the file.</param>
    /// <returns>A FileUploadResult containing information about the uploaded file.</returns>
    private async Task<FileUploadResult> SaveFileAsync(
        string untrustedFileName,
        string trustedFileNameForDisplay,
        string path,
        string note,
        long size,
        string contentType
    )
    {
        var fileModel = new FileRecord
        {
            UntrustedName = untrustedFileName,
            TrustedFileNameForDisplay = trustedFileNameForDisplay,
            Path = path,
            Note = note,
            Size = size,
            ContentType = contentType,
            UploadDate = dateTimeOffsetProvider.UtcNow,
        };

        context.Files.Add(fileModel);
        await context.SaveChangesAsync();

        var uploadResult = new FileUploadResult
        {
            Id = fileModel.Id,
            TrustedFileNameForDisplay = fileModel.TrustedFileNameForDisplay,
        };

        return uploadResult;
    }

    /// <summary>
    /// Uploads a file from an IFormFile.
    /// </summary>
    /// <param name="file">The file to upload.</param>
    /// <param name="note">Optional note about the file.</param>
    /// <returns>A FileUploadResult containing information about the uploaded file.</returns>
    public async Task<FileUploadResult> UploadAsync(IFormFile file, string note)
    {
        if (file == null || string.IsNullOrEmpty(file.FileName))
        {
            return CreateUploadResult(FileUploadResult.ErrorCodes.FileNull, "File is null");
        }

        var untrustedFileName = Path.GetFileName(file.FileName);
        var trustedFileNameForDisplay = WebUtility.HtmlEncode(untrustedFileName);

        var validationResult = IsFileLengthInvalid(file.Length, trustedFileNameForDisplay);
        if (validationResult != null)
        {
            return validationResult;
        }

        try
        {
            var trustedFileNameForStorage = Path.GetRandomFileName();
            var path = Path.Combine(environment.ContentRootPath, "unsafe_uploads", trustedFileNameForStorage);

            EnsureDirectoryExists(Path.GetDirectoryName(path));

            await using (var fs = new FileStream(path, FileMode.Create))
            {
                await file.CopyToAsync(fs);
            }

            return await SaveFileAsync(
                untrustedFileName,
                trustedFileNameForDisplay,
                path,
                note,
                file.Length,
                file.ContentType
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error uploading file {FileName}", trustedFileNameForDisplay);
            throw;
        }
    }

    /// <summary>
    /// Uploads a file from a data URI.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <param name="note">Optional note about the file.</param>
    /// <param name="dataUri">The data URI containing the file data.</param>
    /// <returns>A FileUploadResult containing information about the uploaded file.</returns>
    public virtual async Task<FileUploadResult> UploadFromDataUriAsync(string fileName, string note, string dataUri)
    {
        if (string.IsNullOrEmpty(dataUri))
        {
            return CreateUploadResult(FileUploadResult.ErrorCodes.FileNull, "Data URI is null or empty");
        }

        var untrustedFileName = Path.GetFileName(fileName);
        var trustedFileNameForDisplay = WebUtility.HtmlEncode(untrustedFileName);

        try
        {
            var base64Data = DataUriRegex().Match(dataUri).Groups["data"].Value;
            var fileBytes = Convert.FromBase64String(base64Data);

            var validationResult = IsFileLengthInvalid(fileBytes.Length, trustedFileNameForDisplay);
            if (validationResult != null)
            {
                return validationResult;
            }

            var trustedFileNameForStorage = Path.GetRandomFileName();
            var path = Path.Combine(environment.ContentRootPath, "unsafe_uploads", trustedFileNameForStorage);

            EnsureDirectoryExists(Path.GetDirectoryName(path));

            await System.IO.File.WriteAllBytesAsync(path, fileBytes);

            var contentType = MimeTypesMap.GetMimeType(fileName);

            return await SaveFileAsync(
                untrustedFileName,
                trustedFileNameForDisplay,
                path,
                note,
                fileBytes.Length,
                contentType
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error uploading file {FileName}", trustedFileNameForDisplay);
            throw;
        }
    }

    /// <summary>
    /// Regular expression for extracting data from a data URI.
    /// </summary>
    [GeneratedRegex(@"data:(?<type>.+?);base64,(?<data>.+)")]
    private static partial Regex DataUriRegex();
    #endregion
}
