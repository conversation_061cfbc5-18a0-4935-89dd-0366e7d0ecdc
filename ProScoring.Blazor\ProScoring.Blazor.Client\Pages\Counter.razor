@page "/counter"
@rendermode InteractiveAuto
@inject TooltipService tooltipService

<PageTitle>Counter</PageTitle>

<h1>Counter</h1>

<p role="status">Current count: @currentCount</p>

<button class="btn btn-primary" @onclick="IncrementCount">Click me</button>

<RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem"
             MouseEnter="@(args => ShowTooltip(args, new TooltipOptions(){Delay=500}))">
    <RadzenLabel Text="Private" Component="Private" />
    <RadzenSwitch @bind-Value=@Private Name="Private" />
    <RadzenText Text="@Private.ToString()" />
</RadzenStack>

@code {
    private int currentCount = 0;

    bool Private;
    private void OnPrivateChanged(bool? value)
    {
        Private = value ?? false;
    }
    private void ShowTooltip(ElementReference elementReference, TooltipOptions? options = null)
    {
        tooltipService.Open(elementReference, "Private OAs are not discoverable by search.");
    }
    private void IncrementCount()
    {
        currentCount++;
    }
}
