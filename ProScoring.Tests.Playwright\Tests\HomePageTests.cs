using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the home page of the ProScoring application.
/// </summary>
public class HomePageTests : BasePageWithMainLayoutTests
{
    #region Constructors
    public HomePageTests(PlaywrightFixture fixture)
        : base(fixture) { }
    #endregion Constructors

    #region Test Methods

    /// <summary>
    /// Tests that navigating to the home page works correctly.
    /// </summary>
    [Fact]
    public async Task Navigate_To_HomePage_Succeeds()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var homePage = new HomePage(page);

            // Act
            await homePage.NavigateAsync();

            // Assert
            page.Url.Should().Contain(_fixture.Settings.BaseUrl);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the user can navigate from the home page to the login page.
    /// </summary>
    [Fact(Skip = "We don't navigate to the homepage anymore, we have a popup login.")]
    public async Task HomePage_CanNavigateToLoginPage()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var homePage = new HomePage(page);

            // Act
            await homePage.NavigateAsync();
            var isLoginButtonVisible = await homePage.IsLoginButtonVisible();
            isLoginButtonVisible.Should().BeTrue();

            await homePage.ClickLoginButton();

            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            // Wait for navigation to complete
            await Task.Delay(1000); // Sleep for 1 second

            // Assert
            page.Url.ToLower().Should().Contain("/Account/login".ToLower());
            var loginPage = new LoginPage(page);
            await loginPage.VerifyPageLoadedAsync();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that the login button is visible on the home page when not logged in.
    /// </summary>
    [Fact]
    public async Task Login_Button_Is_Visible_When_Not_Logged_In()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            var homePage = new HomePage(page);

            // Act
            await homePage.NavigateAsync();
            var isLoginButtonVisible = await homePage.IsLoginButtonVisible();

            // Assert
            isLoginButtonVisible.Should().BeTrue();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Gets the test page to use for the current test.
    /// </summary>
    /// <param name="page">The Playwright page instance.</param>
    /// <returns>An instance of a HomePage.</returns>
    protected override BasePageWithMainLayout GetTestPage(IPage page)
    {
        return new HomePage(page);
    }

    #endregion Test Methods
}
