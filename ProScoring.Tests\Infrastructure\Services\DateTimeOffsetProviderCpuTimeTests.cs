using System;
using Xunit;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class DateTimeOffsetProviderCpuTimeTests
    {
        [Fact]
        public void UtcNow_Should_Return_Value_Close_To_DateTimeOffsetUtcNow()
        {
            // Arrange
            var provider = new DateTimeOffsetProviderCpuTime();
            var tolerance = TimeSpan.FromSeconds(2);

            // Act
            var providerUtcNow = provider.UtcNow;
            var systemUtcNow = DateTimeOffset.UtcNow;

            // Assert
            var difference = (providerUtcNow - systemUtcNow).Duration(); // Gets the absolute difference
            Assert.True(difference < tolerance, $"Difference {difference} exceeded tolerance {tolerance}. Provider: {providerUtcNow}, System: {systemUtcNow}");
        }
    }
}
