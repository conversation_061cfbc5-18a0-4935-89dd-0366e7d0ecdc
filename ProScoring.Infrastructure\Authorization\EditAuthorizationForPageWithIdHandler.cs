using System.Web;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler for edit operations on pages with IDs.
/// Determines if the current user has permission to edit specific pages in the system.
/// </summary>
/// <param name="authorizationProvider">The provider that determines authorization status.</param>
/// <param name="navigationManager">The navigation manager to access the current URL.</param>
public class EditAuthorizationForPageWithIdHandler(
    IAuthorizationProvider authorizationProvider,
    NavigationManager navigationManager
)
    : AuthorizationHandlerForPageWithIdBase<EditAuthorizationForPageWithIdHandler.Requirement>(
        authorizationProvider,
        navigationManager,
        AuthTypes.Actions.EDIT
    )
{
    /// <summary>
    /// The policy name used for edit authorization requirements on pages with IDs.
    /// </summary>
    public const string PolicyName = "EditPageWithIdPolicy";

    /// <summary>
    /// The authorization requirement for edit operations on pages with IDs.
    /// This class is used to identify the requirement type for the handler.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
