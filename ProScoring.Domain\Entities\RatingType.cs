using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a type of rating that can be applied to boats.
/// </summary>
public class RatingType : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<RatingType>
{
    public const string ID_PREFIX = "HT";
    public override string IdPrefix => ID_PREFIX;

    #region Properties
    /// <summary>
    /// Gets or sets the unique identifier of the rating type.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets or sets the name of the rating type.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [StringLength(50)]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the description of this rating type.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(500)]
    public string? Description { get; set; }
    #endregion

    #region Methods
    /// <summary>
    /// Configures foreign key relationships for the RatingType entity.
    /// </summary>
    /// <param name="entity">The entity type builder for RatingType.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RatingType> entity)
    {
        // No navigation property from RatingType to Ratings,
        // but we still configure the foreign key relationship
        entity
            .HasMany<Rating>()
            .WithOne(r => r.RatingType)
            .HasForeignKey(r => r.RatingTypeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
    #endregion
}
