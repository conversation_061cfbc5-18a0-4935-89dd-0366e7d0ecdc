namespace ProScoring.Tests.Playwright.Configuration;

/// <summary>
/// Strongly typed configuration settings for Playwright tests.
/// </summary>
public class PlaywrightTestSettings
{
    #region Properties
    /// <summary>
    /// Gets or sets the base URL of the application being tested.
    /// </summary>
    public string BaseUrl { get; set; } = "https://localhost:7154";

    /// <summary>
    /// Gets or sets the email address for the test user.
    /// </summary>
    public string TestUserEmail { get; set; } = "<EMAIL>";

    /// <summary>
    /// Gets or sets the password for the test user.
    /// </summary>
    public string TestUserPassword { get; set; } = "Test@123456";

    /// <summary>
    /// Gets or sets whether the browser should run in headless mode.
    /// </summary>
    public bool Headless { get; set; } = true;

    /// <summary>
    /// Gets or sets the browser slow motion value in milliseconds.
    /// </summary>
    public int SlowMoMs { get; set; } = 100;

    /// <summary>
    /// Gets or sets the viewport width.
    /// </summary>
    public int ViewportWidth { get; set; } = 1280;

    /// <summary>
    /// Gets or sets the viewport height.
    /// </summary>
    public int ViewportHeight { get; set; } = 720;
    #endregion
}
