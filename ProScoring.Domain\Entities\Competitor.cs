using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;
using ProScoring.Domain.Entities.DbSupportBaseClasses;

namespace ProScoring.Domain.Entities;

/// <summary>
/// This is the competitor information associated with a user.
/// When a user registers for a regatta, this information is copied to the regatta competitor.
/// It is kept in-sync until the regatta starts or the regatta competitor is edited.
///
/// There could be more than one competitor to a user, particularly for youth events.
/// </summary>
public class Competitor : LastChangeTrackingWithAutoInsertedIdBase
{
    #region Properties

    [Key]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    [Column(Order = 15)]
    [ForeignKey(nameof(User))]
    public string? UserId { get; set; }

    [Column(Order = 20)]
    public string? Name { get; set; }

    [Column(Order = 30)]
    [PersonalData]
    public string? Email { get; set; }

    [Column(Order = 40)]
    [PersonalData]
    public string? Phone { get; set; }

    [Column(Order = 50)]
    [PersonalData]
    public string? Address { get; set; }

    [Column(Order = 60)]
    [PersonalData]
    public string? Address2 { get; set; }

    [Column(Order = 70)]
    [PersonalData]
    public string? City { get; set; }

    [Column(Order = 80)]
    [PersonalData]
    public string? State { get; set; }

    [Column(Order = 90)]
    [PersonalData]
    public string? ZipCode { get; set; }

    [Column(Order = 100)]
    [PersonalData]
    public string? Country { get; set; }

    [Column(Order = 110)]
    [PersonalData]
    public DateOnly? BirthDate { get; set; }

    [Column(Order = 120)]
    [PersonalData]
    public string? WorldSailingNumber { get; set; }

    [Column(Order = 130)]
    [PersonalData]
    public string? MemberNationalAuthority { get; set; }

    [Column(Order = 140)]
    [PersonalData]
    public string? MnaNumber { get; set; }

    [Column(Order = 150)]
    [PersonalData]
    public string? EmergencyContactName1 { get; set; }

    [Column(Order = 160)]
    [PersonalData]
    public string? EmergencyContactPhone1 { get; set; }

    [Column(Order = 170)]
    [PersonalData]
    public string? EmergencyContactName2 { get; set; }

    [Column(Order = 180)]
    [PersonalData]
    public string? EmergencyContactPhone2 { get; set; }

    [Column(Order = 190)]
    public string? Notes { get; set; }

    public override string IdPrefix => "C";

    /// <summary>
    /// Gets or sets the application user associated with this competitor.
    /// </summary>
    public virtual ApplicationUser? User { get; set; }

    #endregion Properties
}
