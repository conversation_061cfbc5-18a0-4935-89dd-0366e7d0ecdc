﻿using Microsoft.AspNetCore.Mvc;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Interface defining file controller operations for file handling.
/// </summary>
public interface IFileController
{
    /// <summary>
    /// Downloads a file by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a downloadable response or an error message.</returns>
    Task<IActionResult> Download(string id);

    /// <summary>
    /// Downloads a file as a data URI by its ID.
    /// </summary>
    /// <param name="id">The ID of the file to download.</param>
    /// <returns>The file as a data URI or an error message.</returns>
    Task<IActionResult> DownloadFileData(string id);

    /// <summary>
    /// Gets a file record by its ID.
    /// </summary>
    /// <param name="id">The ID of the file record to retrieve.</param>
    /// <returns>The file record or an error message.</returns>
    Task<ActionResult<FileRecord>> Get(string id);

    /// <summary>
    /// A test endpoint that returns "Goodbye World".
    /// </summary>
    /// <returns>A string message.</returns>
    Task<ActionResult<string>> GoodbyeWorld();

    /// <summary>
    /// A test endpoint that returns "Hello World".
    /// </summary>
    /// <returns>A string message.</returns>
    Task<ActionResult<string>> HelloWorld();

    /// <summary>
    /// Uploads a file.
    /// </summary>
    /// <param name="file">The file to upload.</param>
    /// <param name="note">A note about the file.</param>
    /// <returns>The result of the upload operation.</returns>
    Task<ActionResult<FileUploadResult>> Upload(IFormFile file, [FromForm] string note);

    /// <summary>
    /// Uploads a file from a data URI.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <param name="note">A note about the file.</param>
    /// <param name="fileData">The file data as a data URI.</param>
    /// <returns>The result of the upload operation.</returns>
    Task<ActionResult<FileUploadResult>> UploadFileData(string fileName, string note, string fileData);
}
