# OData API Documentation

ProScoring provides OData endpoints for querying organizing authorities and their information. This document describes how to use these endpoints.

## Base URL

The base URL for OData endpoints is:

```
/odata
```

## Available Entity Sets

- `OrganizingAuthorities` - Organizing authority entities
  - Use with `$select` to get simplified organizing authority information (see examples below)

## Common Query Options

OData supports the following query options:

| Option | Description | Example |
|--------|-------------|---------|
| `$select` | Select specific properties | `$select=Id,Name,City` |
| `$filter` | Filter results based on a condition | `$filter=Name eq 'Yacht Club'` |
| `$orderby` | Sort results | `$orderby=Name asc` |
| `$top` | Limit the number of results | `$top=10` |
| `$skip` | Skip a number of results | `$skip=10` |
| `$count` | Include a count of total results | `$count=true` |
| `$expand` | Expand related entities | `$expand=Image` |

## Examples

### Get all organizing authorities

```
GET /odata/OrganizingAuthorities
```

### Get organizing authorities with pagination

```
GET /odata/OrganizingAuthorities?$top=10&$skip=0&$orderby=Name asc
```

### Filter organizing authorities by name

```
GET /odata/OrganizingAuthorities?$filter=contains(Name, 'Yacht')
```

### Get organizing authorities with their images

```
GET /odata/OrganizingAuthorities?$expand=Image
```

### Get only specific fields

```
GET /odata/OrganizingAuthorities?$select=Id,Name,City,State
```

### Combine multiple query options

```
GET /odata/OrganizingAuthorities?$select=Id,Name,City&$filter=contains(Name, 'Yacht')&$orderby=Name asc&$top=5
```

### Get organizing authority info (simplified view)

```
GET /odata/OrganizingAuthorities?$select=Id,Name,City,State,Country,Email,Phone,Website,Private
```

### Count total organizing authorities

```
GET /odata/OrganizingAuthorities?$count=true
```

## Filter Operators

OData supports various filter operators:

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Equal | `$filter=Name eq 'Yacht Club'` |
| `ne` | Not equal | `$filter=Name ne 'Yacht Club'` |
| `gt` | Greater than | `$filter=CreatedDate gt 2023-01-01T00:00:00Z` |
| `ge` | Greater than or equal | `$filter=CreatedDate ge 2023-01-01T00:00:00Z` |
| `lt` | Less than | `$filter=CreatedDate lt 2023-01-01T00:00:00Z` |
| `le` | Less than or equal | `$filter=CreatedDate le 2023-01-01T00:00:00Z` |
| `and` | Logical and | `$filter=City eq 'Seattle' and State eq 'WA'` |
| `or` | Logical or | `$filter=City eq 'Seattle' or City eq 'Portland'` |
| `not` | Logical not | `$filter=not(Private eq true)` |
| `contains` | String contains | `$filter=contains(Name, 'Yacht')` |
| `startswith` | String starts with | `$filter=startswith(Name, 'Seattle')` |
| `endswith` | String ends with | `$filter=endswith(Name, 'Club')` |

## Error Handling

OData errors are returned with a 400 Bad Request status code and a JSON object with the following structure:

```json
{
  "error": {
    "code": "ODataQueryError",
    "message": "Error message",
    "details": [
      {
        "code": "QueryOption",
        "message": "Check the syntax of the $filter parameter",
        "target": "$filter"
      }
    ]
  }
}
```

## Metadata

OData metadata is available at:

```
GET /odata/$metadata
```

This endpoint returns an XML document describing the entity model.

## Client Usage in ProScoring

In the ProScoring application, you can use the `OrganizingAuthorityHttpClient` to make OData queries:

```csharp
// Get organizing authorities with OData query
var authorities = await _organizingAuthorityClient.GetWithODataQueryAsync("$filter=contains(Name, 'Yacht')&$orderby=Name asc");

// Get organizing authority info DTOs with OData query
// The $select parameter is automatically added if not provided
var authorityInfos = await _organizingAuthorityClient.GetInfoWithODataQueryAsync("$top=10&$skip=0&$orderby=Name asc");

// You can also specify your own $select to customize the fields
var customAuthorityInfos = await _organizingAuthorityClient.GetInfoWithODataQueryAsync("$select=Id,Name,City,State&$orderby=Name asc");
```

## Authorization

The OData endpoints apply the same authorization rules as the rest of the application:

- Unauthenticated users can only see public organizing authorities
- Authenticated users can see:
  - All public organizing authorities
  - Private organizing authorities for which they have explicit VIEW, EDIT, or ADMIN permissions
- Users with the HMFIC claim can see all organizing authorities
