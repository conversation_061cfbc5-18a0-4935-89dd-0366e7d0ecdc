using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using ProScoring.Blazor.Services;
using ProScoring.Domain.Dtos;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the OrganizingAuthorityHttpClient class focusing on count functionality.
/// </summary>
public class OrganizingAuthorityHttpClientCountTests
{
    private readonly HttpClient _httpClient;
    private readonly TestHttpMessageHandler _testHttpMessageHandler;
    private readonly OrganizingAuthorityHttpClient _client;

    public OrganizingAuthorityHttpClientCountTests()
    {
        _testHttpMessageHandler = new TestHttpMessageHandler();
        _httpClient = new HttpClient(_testHttpMessageHandler) { BaseAddress = new Uri("http://localhost") };
        _client = new OrganizingAuthorityHttpClient(_httpClient);
    }

    // Test HttpMessageHandler for mocking HTTP responses
    private class TestHttpMessageHandler : HttpMessageHandler
    {
        public HttpResponseMessage? ResponseToReturn { get; set; }

        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken
        )
        {
            return Task.FromResult(ResponseToReturn ?? new HttpResponseMessage(HttpStatusCode.NotFound));
        }
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_ReturnsCorrectCount_FromODataCountHeader()
    {
        // Arrange
        var dtos = new List<OrganizingAuthorityInfoDto>
        {
            new OrganizingAuthorityInfoDto { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthorityInfoDto { Id = "O2", Name = "Authority 2" },
        };

        var response = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(new { value = dtos })),
        };

        // Add the OData-Count header
        response.Headers.Add("OData-Count", "10");

        _testHttpMessageHandler.ResponseToReturn = response;

        // Act
        var result = await _client.GetInfoWithODataQueryAsync("$count=true");

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.TotalCount.Should().Be(10);
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_ReturnsCorrectCount_FromODataEntityCountHeader()
    {
        // Arrange
        var dtos = new List<OrganizingAuthorityInfoDto>
        {
            new OrganizingAuthorityInfoDto { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthorityInfoDto { Id = "O2", Name = "Authority 2" },
        };

        var response = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(new { value = dtos })),
        };

        // Add the OData-EntityCount header
        response.Headers.Add("OData-EntityCount", "15");

        _testHttpMessageHandler.ResponseToReturn = response;

        // Act
        var result = await _client.GetInfoWithODataQueryAsync("$count=true");

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.TotalCount.Should().Be(15);
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_ReturnsCorrectCount_FromODataCountProperty()
    {
        // Arrange
        var dtos = new List<OrganizingAuthorityInfoDto>
        {
            new OrganizingAuthorityInfoDto { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthorityInfoDto { Id = "O2", Name = "Authority 2" },
        };

        // Create a JSON object with just the value property
        var jsonObject = new { value = dtos };

        var response = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(JsonSerializer.Serialize(jsonObject)),
        };

        // Add the OData-Count header instead
        response.Headers.Add("OData-Count", "20");

        _testHttpMessageHandler.ResponseToReturn = response;

        // Act
        var result = await _client.GetInfoWithODataQueryAsync("$count=true");

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.TotalCount.Should().Be(20);
    }
}
