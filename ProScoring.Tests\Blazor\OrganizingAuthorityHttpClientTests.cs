using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using ProScoring.Blazor.Services;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the <see cref="OrganizingAuthorityHttpClient"/> class.
/// </summary>
public class OrganizingAuthorityHttpClientTests
{
    [Fact]
    public async Task GetInfoWithODataQueryAsync_WithoutBaseAddress_UsesDefaultBaseAddress()
    {
        // Arrange
        var httpClient = new HttpClient();
        var mockConfiguration = Substitute.For<IConfiguration>();

        // Configure the mock to return null for the BaseUrl and Urls settings
        mockConfiguration["BaseUrl"].Returns((string?)null);
        mockConfiguration["Urls"].Returns((string?)null);

        var client = new OrganizingAuthorityHttpClient(httpClient, mockConfiguration);

        // The client should now use the default "http://localhost" base address
        // This should not throw an InvalidOperationException anymore

        // We expect an InvalidOperationException with a specific message about connection errors
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => client.GetInfoWithODataQueryAsync("$orderby=Name asc&$top=10")
        );

        // The exception message should indicate a connection error
        Assert.Contains("Error connecting to the organizing authority OData API", exception.Message);
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_WithBaseAddress_ThrowsConnectionError()
    {
        // Arrange
        var httpClient = new HttpClient { BaseAddress = new Uri("http://localhost") };

        var client = new OrganizingAuthorityHttpClient(httpClient);

        // We expect an InvalidOperationException with a specific message about connection errors
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => client.GetInfoWithODataQueryAsync("$orderby=Name asc&$top=10")
        );

        // The exception message should indicate a connection error
        Assert.Contains("Error connecting to the organizing authority OData API", exception.Message);
    }

    [Fact]
    public async Task GetInfoWithODataQueryAsync_WithConfigurationBaseUrl_UsesConfiguredBaseAddress()
    {
        // Arrange
        var httpClient = new HttpClient();
        var mockConfiguration = Substitute.For<IConfiguration>();

        // Configure the mock to return a value for the BaseUrl setting
        mockConfiguration["BaseUrl"].Returns("http://testserver");

        var client = new OrganizingAuthorityHttpClient(httpClient, mockConfiguration);

        // We expect an InvalidOperationException with a specific message about connection errors
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => client.GetInfoWithODataQueryAsync("$orderby=Name asc&$top=10")
        );

        // The exception message should indicate a connection error
        Assert.Contains("Error connecting to the organizing authority OData API", exception.Message);
    }

    [Fact]
    public async Task GetWithODataQueryAsync_WithoutBaseAddress_UsesDefaultBaseAddress()
    {
        // Arrange
        var httpClient = new HttpClient();
        var mockConfiguration = Substitute.For<IConfiguration>();

        // Configure the mock to return null for the BaseUrl and Urls settings
        mockConfiguration["BaseUrl"].Returns((string?)null);
        mockConfiguration["Urls"].Returns((string?)null);

        var client = new OrganizingAuthorityHttpClient(httpClient, mockConfiguration);

        // We expect an InvalidOperationException with a specific message about connection errors
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => client.GetWithODataQueryAsync("$filter=Id eq 'test-id'")
        );

        // The exception message should indicate a connection error
        Assert.Contains("Error connecting to the organizing authority OData API", exception.Message);
    }
}
