using System.Threading.Tasks;

namespace ProScoring.Blazor.Services;

/// <summary>
/// Service for managing the main layout context and shared functionality.
/// Provides methods and properties to control the visibility of UI elements
/// and handle login dialog interactions.
/// </summary>
public class MainLayoutContextService
{
    #region Fields

    private Func<Action?, Task>? OpenLoginDialogMethod { get; set; }

    #endregion

    #region Properties

    /// <summary>
    /// Gets whether the login button is visible.
    /// </summary>
    public bool LoginButtonVisible { get; private set; } = true;

    #endregion

    #region Methods

    /// <summary>
    /// Updates the layout properties and notifies subscribers of the change.
    /// </summary>
    /// <param name="loginButtonVisible">Optional. When provided, updates the visibility state of the login button.</param>
    public void UpdateLayout(bool? loginButtonVisible = null)
    {
        if (loginButtonVisible.HasValue)
            LoginButtonVisible = loginButtonVisible.Value;

        NotifyLayoutChanged();
    }

    /// <summary>
    /// Sets the method to be used for opening the login dialog.
    /// This method should be called during initialization to register the dialog opening functionality.
    /// </summary>
    /// <param name="openLoginDialogMethod">The method that will be called to open the login dialog.
    /// The method should accept an optional post-login action.</param>
    public void SetOpenLoginDialogMethod(Func<Action?, Task> openLoginDialogMethod)
    {
        OpenLoginDialogMethod = openLoginDialogMethod;
    }

    /// <summary>
    /// Opens the login dialog using the registered method.
    /// </summary>
    /// <param name="postLoginAction">Optional action to execute after successful login.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task OpenLoginDialogAsync(Action? postLoginAction = null)
    {
        if (OpenLoginDialogMethod != null)
        {
            await OpenLoginDialogMethod(postLoginAction);
        }
    }

    /// <summary>
    /// Notifies subscribers that the layout has changed.
    /// </summary>
    private void NotifyLayoutChanged() => OnLayoutChanged?.Invoke();

    #endregion

    #region Events

    /// <summary>
    /// Event that is triggered when the layout is changed.
    /// Subscribers can use this to update their UI when layout properties are modified.
    /// </summary>
    public event Action? OnLayoutChanged;

    #endregion
}
