using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents a boat rating that has been copied from a master Rating
/// for use in a specific regatta.
///
/// This allows ratings to be frozen at the time of regatta registration
/// or optionally synchronized with the master rating if changes occur.
/// </summary>
public class RegattaRating : RegattaEntityBase<RegattaRating>, IHasForeignKeyConfiguration<RegattaRating>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaRating IDs.
    /// </summary>
    public const string ID_PREFIX = "RR";

    #endregion

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaRating.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the ID of the RegattaBoat this rating belongs to.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [ForeignKey(nameof(RegattaBoat))]
    public required string RegattaBoatId { get; set; }

    /// <summary>
    /// Gets or sets the ID of the master Rating record.
    /// </summary>
    [Column(Order = 30)]
    [ForeignKey(nameof(SourceRating))]
    public string? SourceRatingId { get; set; }

    /// <summary>
    /// Gets or sets the rating type ID.
    /// </summary>
    [Required]
    [Column(Order = 40)]
    [ForeignKey(nameof(RatingType))]
    public required string RatingTypeId { get; set; }

    /// <summary>
    /// Gets or sets notes about this rating.
    /// </summary>
    [Column(Order = 50)]
    [StringLength(500)]
    public string? RatingNote { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether changes to the source Rating
    /// should be synchronized with this RegattaRating.
    /// </summary>
    [Required]
    [Column(Order = 60)]
    public bool KeepSynced { get; set; } = false;

    #endregion

    #region Navigation Properties

    /// <summary>
    /// Gets or sets the RegattaBoat this rating belongs to.
    /// </summary>
    public virtual RegattaBoat RegattaBoat { get; set; } = null!;

    /// <summary>
    /// Gets or sets the source Rating this RegattaRating was copied from.
    /// </summary>
    public virtual Rating? SourceRating { get; set; }

    /// <summary>
    /// Gets or sets the rating type.
    /// </summary>
    public virtual RatingType RatingType { get; set; } = null!;

    /// <summary>
    /// Gets or sets the collection of rating values associated with this rating.
    /// </summary>
    public virtual ICollection<RegattaRatingValue> RegattaRatingValues { get; set; } = new List<RegattaRatingValue>();

    #endregion

    #region DB Configuration

    #region IHasForeignKeyConfiguration

    /// <summary>
    /// Configures foreign key relationships for the RegattaRating entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the RegattaRating entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaRating> entity)
    {
        entity
            .HasOne(rr => rr.RegattaBoat)
            .WithMany(rb => rb.RegattaRatings)
            .HasForeignKey(rr => rr.RegattaBoatId)
            .OnDelete(DeleteBehavior.Cascade);

        entity
            .HasOne(rr => rr.SourceRating)
            .WithMany(r => r.RegattaRatings)
            .HasForeignKey(rr => rr.SourceRatingId)
            .OnDelete(DeleteBehavior.SetNull);

        entity
            .HasOne(rr => rr.RatingType)
            .WithMany()
            .HasForeignKey(rr => rr.RatingTypeId)
            .OnDelete(DeleteBehavior.Restrict);
    }

    #endregion

    #endregion
}
