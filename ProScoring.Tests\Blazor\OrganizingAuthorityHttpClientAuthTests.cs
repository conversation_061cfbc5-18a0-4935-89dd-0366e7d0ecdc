using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using NSubstitute;
using ProScoring.Blazor.Services;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the authentication behavior of the <see cref="AuthenticatedHttpClientHandler"/> class.
/// </summary>
public class OrganizingAuthorityHttpClientAuthTests
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public OrganizingAuthorityHttpClientAuthTests()
    {
        _httpContextAccessor = Substitute.For<IHttpContextAccessor>();
    }

    [Fact]
    public async Task AuthenticatedHttpClientHandler_CopiesCookiesFromHttpContext()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers.Append("Cookie", ".AspNetCore.Identity.Application=test-auth-cookie");
        _httpContextAccessor.HttpContext.Returns(httpContext);

        var handler = new AuthenticatedHttpClientHandler(_httpContextAccessor);
        var invoker = new HttpMessageInvoker(handler);

        var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
        var cancellationToken = CancellationToken.None;

        // Mock the inner handler to capture the request
        var cookieHeaderAdded = false;
        handler.InnerHandler = new TestHandler(req =>
        {
            // Check if the cookie header was added to the request
            if (req.Headers.Contains("Cookie"))
            {
                cookieHeaderAdded = true;
            }
            return new HttpResponseMessage();
        });

        // Act
        await invoker.SendAsync(request, cancellationToken);

        // Assert
        Assert.True(cookieHeaderAdded, "Cookie header should be added to the request");
    }

    [Fact]
    public async Task AuthenticatedHttpClientHandler_DoesNotAddCookiesWhenHttpContextIsNull()
    {
        // Arrange
        _httpContextAccessor.HttpContext.Returns((HttpContext?)null);

        var handler = new AuthenticatedHttpClientHandler(_httpContextAccessor);
        var invoker = new HttpMessageInvoker(handler);

        var request = new HttpRequestMessage(HttpMethod.Get, "http://localhost/api/test");
        var cancellationToken = CancellationToken.None;

        // Mock the inner handler to capture the request
        var cookieHeaderAdded = false;
        handler.InnerHandler = new TestHandler(req =>
        {
            // Check if the cookie header was added to the request
            if (req.Headers.Contains("Cookie"))
            {
                cookieHeaderAdded = true;
            }
            return new HttpResponseMessage();
        });

        // Act
        await invoker.SendAsync(request, cancellationToken);

        // Assert
        Assert.False(cookieHeaderAdded, "Cookie header should not be added when HttpContext is null");
    }

    /// <summary>
    /// Test handler that allows inspecting the request.
    /// </summary>
    private class TestHandler(Func<HttpRequestMessage, HttpResponseMessage> responseFactory) : DelegatingHandler
    {
        protected override Task<HttpResponseMessage> SendAsync(
            HttpRequestMessage request,
            CancellationToken cancellationToken
        )
        {
            return Task.FromResult(responseFactory(request));
        }
    }
}
