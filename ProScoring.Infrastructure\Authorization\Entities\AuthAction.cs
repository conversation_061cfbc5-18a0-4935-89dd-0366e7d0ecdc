using System.ComponentModel.DataAnnotations;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization.Entities;

/// <summary>
/// Represents an authorization action that can be performed.
/// </summary>
public class AuthAction : IHasInitialSeedData<AuthAction>
{
    public static AuthAction[] SeedData =>
        [
            new AuthAction { Name = AuthTypes.Actions.ADMIN },
            new AuthAction { Name = AuthTypes.Actions.DELETE },
            new AuthAction { Name = AuthTypes.Actions.EDIT },
            new AuthAction { Name = AuthTypes.Actions.VIEW },
            new AuthAction { Name = AuthTypes.GOD },
        ];

    /// <summary>
    /// Gets or sets the name of the authorization action.
    /// </summary>
    [Key]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
}
