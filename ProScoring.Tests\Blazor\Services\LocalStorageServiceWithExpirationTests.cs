using System;
using System.Text.Json;
using System.Threading.Tasks;
using Blazored.LocalStorage; // For ILocalStorageService
using FluentAssertions;
using Microsoft.Extensions.Options;
using NSubstitute;
using NSubstitute.ExceptionExtensions; // For ThrowsAsync
using ProScoring.Blazor.Options;      // For LocalStorageOptions
using ProScoring.Blazor.Services;      // For LocalStorageServiceWithExpiration and LocalSettingWithExpiration
using ProScoring.Infrastructure.ServiceInterfaces; // For IDateTimeOffsetProvider
using Xunit;

namespace ProScoring.Tests.Blazor.Services
{
    public class TestData 
    { 
        public string? Name { get; set; } 

        // Optional: For BeEquivalentTo to work smoothly, especially with records or complex types.
        public override bool Equals(object? obj) => obj is TestData data && Name == data.Name;
        public override int GetHashCode() => Name?.GetHashCode() ?? 0;
    }

    public class LocalStorageServiceWithExpirationTests
    {
        private readonly ILocalStorageService _localStorageSubstitute;
        private readonly IDateTimeOffsetProvider _dateTimeProviderSubstitute;
        private readonly IOptions<LocalStorageOptions> _optionsSubstitute;
        private readonly LocalStorageServiceWithExpiration _service;
        private readonly DateTimeOffset _fakeUtcNow = new DateTimeOffset(2023, 1, 1, 12, 0, 0, TimeSpan.Zero);
        private readonly int _defaultExpirationMinutes = 30;

        public LocalStorageServiceWithExpirationTests()
        {
            _localStorageSubstitute = Substitute.For<ILocalStorageService>();
            _dateTimeProviderSubstitute = Substitute.For<IDateTimeOffsetProvider>();
            _dateTimeProviderSubstitute.UtcNow.Returns(_fakeUtcNow);
            
            _optionsSubstitute = Substitute.For<IOptions<LocalStorageOptions>>();
            _optionsSubstitute.Value.Returns(new LocalStorageOptions { ExpirationTimeoutMinutes = _defaultExpirationMinutes });
            
            _service = new LocalStorageServiceWithExpiration(_localStorageSubstitute, _optionsSubstitute, _dateTimeProviderSubstitute);
        }

        private bool AssertStoredJson<T>(string json, T expectedValue, DateTimeOffset expectedExpiration, TimeSpan? expectedLifetime)
        {
            var deserialized = JsonSerializer.Deserialize<LocalSettingWithExpiration<T>>(json);
            deserialized.Should().NotBeNull();
            if (deserialized == null) return false; // Should().NotBeNull() will throw if it is null.

            deserialized.Value.Should().BeEquivalentTo(expectedValue);
            deserialized.Expiration.Should().BeCloseTo(expectedExpiration, TimeSpan.FromSeconds(1));
            deserialized.LifeTime.Should().Be(expectedLifetime);
            return true;
        }

        // --- SetItemAsync Tests ---
        [Fact]
        public async Task SetItemAsync_WithLifetime_StoresCorrectValueAndExpiration()
        {
            // Arrange
            string key = "testKey";
            string value = "testValue";
            TimeSpan lifetime = TimeSpan.FromMinutes(10);

            // Act
            await _service.SetItemAsync(key, value, lifetime);

            // Assert
            await _localStorageSubstitute.Received(1).SetItemAsStringAsync(key, Arg.Is<string>(json => 
                AssertStoredJson(json, value, _fakeUtcNow.Add(lifetime), lifetime)));
        }

        [Fact]
        public async Task SetItemAsync_NullLifetime_UsesDefaultExpiration()
        {
            // Arrange
            string key = "testKey";
            string value = "testValue";

            // Act
            await _service.SetItemAsync(key, value, null);

            // Assert
            await _localStorageSubstitute.Received(1).SetItemAsStringAsync(key, Arg.Is<string>(json => 
                AssertStoredJson(json, value, _fakeUtcNow.AddMinutes(_defaultExpirationMinutes), null)));
        }

        [Fact]
        public async Task SetItemAsync_LocalStorageThrows_DoesNotThrow()
        {
            // Arrange
            _localStorageSubstitute.SetItemAsStringAsync(Arg.Any<string>(), Arg.Any<string>())
                .ThrowsAsync(new Exception("Storage error"));

            // Act
            Func<Task> act = async () => await _service.SetItemAsync("key", "value", null);

            // Assert
            await act.Should().NotThrowAsync();
        }

        // --- GetItemAsync Tests ---
        [Fact]
        public async Task GetItemAsync_ItemDoesNotExist_ReturnsDefault()
        {
            // Arrange
            _localStorageSubstitute.GetItemAsStringAsync("key").Returns(ValueTask.FromResult<string?>(null));

            // Act
            var result = await _service.GetItemAsync<string>("key");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetItemAsync_ItemNotExpired_ReturnsValueAndRefreshesExpirationByDefault()
        {
            // Arrange
            string key = "key";
            var originalValue = new TestData { Name = "Test" };
            TimeSpan originalLifetime = TimeSpan.FromMinutes(60);
            DateTimeOffset futureExpiration = _fakeUtcNow.AddMinutes(30); // Item is not expired
            var setting = new LocalSettingWithExpiration<TestData> { Value = originalValue, Expiration = futureExpiration, LifeTime = originalLifetime };
            _localStorageSubstitute.GetItemAsStringAsync(key).Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

            // Act
            var result = await _service.GetItemAsync<TestData>(key);

            // Assert
            result.Should().BeEquivalentTo(originalValue);
            await _localStorageSubstitute.Received(1).SetItemAsStringAsync(key, Arg.Is<string>(json => 
                AssertStoredJson(json, originalValue, _fakeUtcNow.Add(originalLifetime), originalLifetime))); // Refreshed
        }

        [Fact]
        public async Task GetItemAsync_ItemNotExpired_NoRefresh_ReturnsValueAndDoesNotRefresh()
        {
            // Arrange
            string key = "key";
            var originalValue = new TestData { Name = "Test" };
            TimeSpan originalLifetime = TimeSpan.FromMinutes(60);
            DateTimeOffset futureExpiration = _fakeUtcNow.AddMinutes(30);
            var setting = new LocalSettingWithExpiration<TestData> { Value = originalValue, Expiration = futureExpiration, LifeTime = originalLifetime };
            _localStorageSubstitute.GetItemAsStringAsync(key).Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

            // Act
            var result = await _service.GetItemAsync<TestData>(key, refreshExpiration: false);

            // Assert
            result.Should().BeEquivalentTo(originalValue);
            await _localStorageSubstitute.DidNotReceive().SetItemAsStringAsync(Arg.Any<string>(), Arg.Any<string>());
        }

        [Fact]
        public async Task GetItemAsync_ItemExpired_RemovesItemAndReturnsDefault()
        {
            // Arrange
            string key = "key";
            DateTimeOffset pastExpiration = _fakeUtcNow.AddMinutes(-10);
            var setting = new LocalSettingWithExpiration<string> { Value = "expiredData", Expiration = pastExpiration };
            _localStorageSubstitute.GetItemAsStringAsync(key).Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));

            // Act
            var result = await _service.GetItemAsync<string>(key);

            // Assert
            result.Should().BeNull();
            await _localStorageSubstitute.Received(1).RemoveItemAsync(key);
        }

        [Fact]
        public async Task GetItemAsync_LocalStorageThrowsOnGet_ReturnsDefault()
        {
            // Arrange
            _localStorageSubstitute.GetItemAsStringAsync(Arg.Any<string>())
                .ThrowsAsync(new Exception("Storage error"));

            // Act
            var result = await _service.GetItemAsync<string>("key");

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetItemAsync_LocalStorageThrowsOnRemove_ReturnsDefaultAndDoesNotThrow()
        {
            // Arrange
            string key = "key";
            DateTimeOffset pastExpiration = _fakeUtcNow.AddMinutes(-10); // Expired item
            var setting = new LocalSettingWithExpiration<string> { Value = "expiredData", Expiration = pastExpiration };
            _localStorageSubstitute.GetItemAsStringAsync(key).Returns(ValueTask.FromResult<string?>(JsonSerializer.Serialize(setting)));
            _localStorageSubstitute.RemoveItemAsync(key).ThrowsAsync(new Exception("Remove error"));

            // Act
            Func<Task<string?>> act = () => _service.GetItemAsync<string>(key);

            // Assert
            (await act.Should().NotThrowAsync()).Subject.Should().BeNull();
        }

        // --- RemoveItemAsync Tests ---
        [Fact]
        public async Task RemoveItemAsync_CallsLocalStorageRemove()
        {
            // Arrange
            string key = "testKey";

            // Act
            await _service.RemoveItemAsync(key);

            // Assert
            await _localStorageSubstitute.Received(1).RemoveItemAsync(key);
        }

        [Fact]
        public async Task RemoveItemAsync_LocalStorageThrows_DoesNotThrow()
        {
            // Arrange
            _localStorageSubstitute.RemoveItemAsync(Arg.Any<string>())
                .ThrowsAsync(new Exception("Remove error"));

            // Act
            Func<Task> act = async () => await _service.RemoveItemAsync("key");

            // Assert
            await act.Should().NotThrowAsync();
        }
    }
}
