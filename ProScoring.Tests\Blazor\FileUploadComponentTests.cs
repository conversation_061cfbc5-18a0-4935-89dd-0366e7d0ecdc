using System.Text.Json;
using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Components.Forms;
using NSubstitute;
using ProScoring.Blazor.Components.Shared;
using ProScoring.Blazor.Controllers;
using ProScoring.Domain.Dtos;
using RichardSzalay.MockHttp;

namespace ProScoring.Tests.Blazor;

public class FileUploadComponentTests : TestContext // note to self, TestContext is a base class for Bunit tests
{
    private bool _disposed = false;

    ~FileUploadComponentTests()
    {
        Dispose(false);
    }

    #region methods

    [Fact]
    public async Task FileUpload_FileSelected_UploadButtonVisible()
    {
        // Arrange
        var cut = RenderComponent<FileUpload>();
        var file = CreateMockedBrowserFile();

        // Act
        await cut.Instance.HandleFileSelection(new InputFileChangeEventArgs([file]));

        // Assert
        cut.FindAll("button:contains('Upload')").Count.Should().Be(1);
    }

    [Fact]
    public void FileUpload_InitialRender_ShowsUploadControl()
    {
        // Arrange & Act
        var cut = RenderComponent<FileUpload>();

        // Assert
        cut.FindAll("input[type='file']").Count.Equals(1);
        cut.Markup.Should().Contain("File Upload");
    }

    [Fact]
    public void FileUpload_NoFileSelected_UploadButtonNotVisible()
    {
        // Arrange & Act
        var cut = RenderComponent<FileUpload>();

        // Assert
        cut.FindAll("button:contains('Upload')").Count.Should().Be(0);
    }

    [Fact]
    public async Task FileUpload_ValidFile_CallsFileUploadEndpoint()
    {
        // Arrange
        var mockFileController = Substitute.For<IFileController>();
        Services.AddScoped(_ => mockFileController);
        var mockHttp = new MockHttpMessageHandler();
        var httpClient = mockHttp.ToHttpClient();
        httpClient.BaseAddress = new Uri("http://localhost");
        var cut = RenderComponent<FileUpload>();
        var file = CreateMockedBrowserFile();

        var uploadResult = new FileUploadResult
        {
            Id = "6fEE5s-3",
            TrustedFileNameForDisplay = file.Name,
            StoredFileName = Path.GetTempFileName(),
            ErrorCode = 0,
        };

        mockHttp.When("/api/file/upload").Respond("application/json", JsonSerializer.Serialize(uploadResult));

        // Act
        await cut.Instance.HandleFileSelection(new InputFileChangeEventArgs([file]));
        await cut.Instance.UploadFile();

        // Assert
        mockHttp.VerifyNoOutstandingRequest();
        mockHttp.VerifyNoOutstandingExpectation();
    }

    protected override void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                Assert.True(true);
            }

            // Clean up any unmanaged resources if needed

            _disposed = true;
        }
        base.Dispose(disposing);
    }

    private static IBrowserFile CreateMockedBrowserFile()
    {
        var fileContent = new byte[] { 1, 2, 3 };
        var file = Substitute.For<IBrowserFile>();
        file.Name.Returns("test.jpg");
        file.ContentType.Returns("image/jpeg");
        file.OpenReadStream(Arg.Any<long>(), Arg.Any<CancellationToken>()).Returns(new MemoryStream(fileContent));
        return file;
    }

    #endregion
}
