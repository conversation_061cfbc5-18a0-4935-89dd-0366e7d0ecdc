using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.BusinessLogic.Services;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using Xunit;
using Xunit.Abstractions;

namespace ProScoring.Tests.BusinessLogic;

/// <summary>
/// Tests for the approval update functionality in the OrganizingAuthorityService.
/// </summary>
public class OrganizingAuthorityServiceApprovalUpdateTests : IDisposable
{
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly IProScoringAuthorizationService _mockProScoringAuthorizationService;
    private readonly IHttpContextAccessor _mockHttpContextAccessor;
    private readonly ServiceAuthorizationHelper _mockServiceAuthorizationHelper;
    private readonly ApplicationDbContext _dbContext;
    private readonly IFileService _mockFileService;
    private readonly OrganizingAuthorityService _service;
    private readonly ITestOutputHelper _output;
    private readonly string _userId = "U-test-user-id";
    private readonly string _dbName;

    public OrganizingAuthorityServiceApprovalUpdateTests(ITestOutputHelper output)
    {
        _output = output;
        _dbName = $"OrganizingAuthorityServiceApprovalUpdateTests_{Guid.NewGuid()}";
        _authStateProvider = Substitute.For<AuthenticationStateProvider>();
        SetupAuthenticationState();
        _dbContext = CreateNewContext();
        _mockFileService = Substitute.For<IFileService>();
        _mockProScoringAuthorizationService = Substitute.For<IProScoringAuthorizationService>();
        _mockHttpContextAccessor = Substitute.For<IHttpContextAccessor>();
        var mockAuthService = Substitute.For<IAuthorizationService>();
        mockAuthService
            .AuthorizeAsync(Arg.Any<ClaimsPrincipal>(), Arg.Any<object>(), Arg.Any<string>())
            .Returns(Task.FromResult(AuthorizationResult.Success()));
        _mockServiceAuthorizationHelper = Substitute.For<ServiceAuthorizationHelper>(
            mockAuthService,
            _mockHttpContextAccessor,
            Substitute.For<ILogger<ServiceAuthorizationHelper>>()
        );

        _service = new OrganizingAuthorityService(
            _dbContext,
            _mockFileService,
            _mockProScoringAuthorizationService,
            _mockHttpContextAccessor,
            _mockServiceAuthorizationHelper,
            Substitute.For<ILogger<OrganizingAuthorityService>>()
        );

        SeedDatabase();
    }

    private void SetupAuthenticationState()
    {
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, _userId) };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        var authState = new AuthenticationState(user);
        _authStateProvider.GetAuthenticationStateAsync().Returns(Task.FromResult(authState));
    }

    private ApplicationDbContext CreateNewContext()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>().UseInMemoryDatabase(_dbName).Options;

        // Create a proper CustomIdValueGenerator that implements ValueGenerator<string>
        var idGenerationUtilService = Substitute.For<IIdGenerationUtilService>();
        var idGeneratorLogger = Substitute.For<ILogger<CustomIdValueGenerator>>();
        var idGenerator = new CustomIdValueGenerator(idGenerationUtilService, idGeneratorLogger);

        var logger = Substitute.For<ILogger<ApplicationDbContext>>();
        var context = new ApplicationDbContext(options, idGenerator, logger);
        context.Database.EnsureCreated();
        return context;
    }

    private void SeedDatabase()
    {
        // Add an organizing authority with approval status set to false
        var authority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
            CreatedById = _userId,
        };

        _dbContext.OrganizingAuthorities.Add(authority);
        _dbContext.SaveChanges();
    }

    public void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        GC.SuppressFinalize(this);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateApprovalStatus()
    {
        // Arrange
        var authority = await _dbContext.OrganizingAuthorities.FindAsync("O1");
        authority.Should().NotBeNull();
        authority!.Approved.Should().BeFalse("Initial approval status should be false");

        var dto = OrganizingAuthorityUploadDto.FromEntity(authority!);
        dto.Approved = true; // Change approval status

        // Act
        await _service.UpdateAsync(dto);

        // Assert
        var updatedAuthority = await _dbContext.OrganizingAuthorities.FindAsync("O1");
        updatedAuthority.Should().NotBeNull();
        updatedAuthority!.Approved.Should().BeTrue("Approval status should be updated to true");
    }

    [Fact]
    public async Task UpdateAsync_ShouldPreserveOtherFields()
    {
        // Arrange
        var authority = await _dbContext.OrganizingAuthorities.FindAsync("O1");
        authority.Should().NotBeNull();

        var dto = OrganizingAuthorityUploadDto.FromEntity(authority!);
        dto.Approved = true; // Change approval status
        dto.Name = "Updated Name"; // Change name
        dto.Website = "https://example.com"; // Add website

        // Act
        await _service.UpdateAsync(dto);

        // Assert
        var updatedAuthority = await _dbContext.OrganizingAuthorities.FindAsync("O1");
        updatedAuthority.Should().NotBeNull();
        updatedAuthority!.Approved.Should().BeTrue("Approval status should be updated");
        updatedAuthority.Name.Should().Be("Updated Name", "Name should be updated");
        updatedAuthority.Website.Should().Be("https://example.com", "Website should be updated");
    }
}
