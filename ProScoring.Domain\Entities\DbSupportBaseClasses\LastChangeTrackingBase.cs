using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.DbSupportBaseClasses;

public abstract class LastChangeTrackingBase : IHasLastChangeTracking
{
    #region properties

    [Column(Order = 1050)]
    public DateTimeOffset CreatedAt { get; set; }

    // navigation property for CreateBy
    public virtual ApplicationUser? CreatedBy { get; set; }

    [Column(Order = 1040)]
    public string? CreatedById { get; set; }

    [Column(Order = 1020)]
    public DateTimeOffset UpdatedAt { get; set; }

    // navigation property for UpdatedBy
    public virtual ApplicationUser? UpdatedBy { get; set; }

    [Column(Order = 1010)]
    public string? UpdatedById { get; set; }

    #endregion
}
