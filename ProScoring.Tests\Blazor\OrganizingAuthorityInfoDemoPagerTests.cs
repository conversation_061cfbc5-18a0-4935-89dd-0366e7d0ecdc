using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NSubstitute;
using ProScoring.Blazor.Services;
using ProScoring.Domain.Dtos;
using Ra<PERSON>zen;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the paging functionality in the OrganizingAuthorityInfoDemo component.
/// </summary>
public class OrganizingAuthorityInfoDemoPagerTests
{
    [Fact]
    public async Task OnPageChanged_ShouldUpdateCurrentPage_AndReloadData()
    {
        // Arrange
        const int initialPage = 1;
        const int newPage = 2;
        const int pageSize = 5;

        // Create test data for page 1
        var page1Items = CreateTestAuthorities(1, pageSize);

        // Create test data for page 2
        var page2Items = CreateTestAuthorities(2, pageSize);

        // Set up the mock HTTP client
        var mockOrganizingAuthorityClient = Substitute.For<IOrganizingAuthorityHttpClient>();

        // Set up the mock to return page 1 data for the initial query
        mockOrganizingAuthorityClient
            .GetInfoWithODataQueryAsync(Arg.Is<string>(q => q.Contains("$skip=0")))
            .Returns(Task.FromResult((Items: page1Items.AsEnumerable(), TotalCount: 10)));

        // Set up the mock to return page 2 data for the second query
        mockOrganizingAuthorityClient
            .GetInfoWithODataQueryAsync(Arg.Is<string>(q => q.Contains("$skip=5")))
            .Returns(Task.FromResult((Items: page2Items.AsEnumerable(), TotalCount: 10)));

        // Create a PagerEventArgs with PageIndex = 1 (which corresponds to page 2)
        var pagerEventArgs = new PagerEventArgs { PageIndex = newPage - 1 };

        // Act - Call the OnPageChanged method directly
        // This simulates what happens when a user clicks on page 2 in the UI
        await OnPageChangedSimulation(pagerEventArgs, mockOrganizingAuthorityClient, initialPage);

        // Assert - Verify that page 2 data was requested
        await mockOrganizingAuthorityClient
            .Received(1)
            .GetInfoWithODataQueryAsync(Arg.Is<string>(q => q.Contains("$skip=5")));
    }

    /// <summary>
    /// Simulates the behavior of the OnPageChanged method in the OrganizingAuthorityInfoDemo component.
    /// </summary>
    private static async Task OnPageChangedSimulation(
        PagerEventArgs args,
        IOrganizingAuthorityHttpClient organizingAuthorityClient,
        int initialPage
    )
    {
        // This simulates the logic in the OnPageChanged method
        // Convert from 0-based PageIndex to 1-based currentPage
        var currentPage = args.PageIndex + 1;

        if (currentPage != initialPage)
        {
            // Calculate the skip value for OData
            var skip = (currentPage - 1) * 5; // 5 is pageSize

            // Build OData query string
            var odataQuery = $"$skip={skip}&$top=5&$orderby=Name asc";

            // Call the HTTP client with the OData query
            await organizingAuthorityClient.GetInfoWithODataQueryAsync(odataQuery);
        }
    }

    /// <summary>
    /// Creates a list of test organizing authority DTOs for a specific page.
    /// </summary>
    private static List<OrganizingAuthorityInfoDto> CreateTestAuthorities(int pageNumber, int pageSize)
    {
        var result = new List<OrganizingAuthorityInfoDto>();
        var startIndex = (pageNumber - 1) * pageSize + 1;

        for (var i = 0; i < pageSize; i++)
        {
            result.Add(
                new OrganizingAuthorityInfoDto
                {
                    Id = $"O{startIndex + i}",
                    Name = $"Test Authority {startIndex + i}",
                    City = $"City {startIndex + i}",
                    State = $"State {startIndex + i}",
                    Country = "USA",
                    Email = $"test{startIndex + i}@example.com",
                    Phone = $"555-{startIndex + i:000}-{startIndex + i:000}",
                    Website = $"https://example{startIndex + i}.com",
                    Private = (startIndex + i) % 2 == 0,
                }
            );
        }

        return result;
    }
}
