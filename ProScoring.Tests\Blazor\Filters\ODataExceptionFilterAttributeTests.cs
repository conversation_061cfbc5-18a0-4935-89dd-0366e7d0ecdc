using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.OData.Extensions;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Microsoft.OData;
using NSubstitute;
using ProScoring.Blazor.Filters;
using Xunit;

namespace ProScoring.Tests.Blazor.Filters;

public class ODataExceptionFilterAttributeTests
{
    private readonly ILogger<ODataExceptionFilterAttribute> _mockLogger;
    private readonly ODataExceptionFilterAttribute _filter;

    public ODataExceptionFilterAttributeTests()
    {
        _mockLogger = Substitute.For<ILogger<ODataExceptionFilterAttribute>>();
        _filter = new ODataExceptionFilterAttribute(_mockLogger);
    }

    [Fact]
    public void OnException_WithODataException_ReturnsBadRequest()
    {
        // Arrange
        var exception = new ODataException("Invalid $filter expression");
        var context = CreateExceptionContext(exception);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeTrue();
        context.Result.Should().BeOfType<BadRequestObjectResult>();

        var result = context.Result as BadRequestObjectResult;
        result.Should().NotBeNull();

        // Since we've verified result is not null, we can safely use it
        var badRequestResult = result!;
        badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);

        // Verify the error object structure
        var errorObj = badRequestResult.Value;
        errorObj.Should().NotBeNull();

        // Since the error is an anonymous object, we need to use dynamic
        dynamic dynamicError = errorObj!;
        Assert.NotNull(dynamicError.error);
        Assert.Equal("ODataQueryError", dynamicError.error.code);
        Assert.Equal("Invalid $filter expression", dynamicError.error.message);
    }

    [Fact]
    public void OnException_WithInvalidOperationExceptionInODataContext_ReturnsBadRequest()
    {
        // Arrange
        var exception = new InvalidOperationException("Invalid operation in OData context");
        var context = CreateExceptionContext(exception, isODataRequest: true);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeTrue();
        context.Result.Should().BeOfType<BadRequestObjectResult>();

        var result = context.Result as BadRequestObjectResult;
        result.Should().NotBeNull();

        // Since we've verified result is not null, we can safely use it
        var badRequestResult = result!;
        badRequestResult.StatusCode.Should().Be(StatusCodes.Status400BadRequest);

        // Verify the error object structure
        var errorObj = badRequestResult.Value;
        errorObj.Should().NotBeNull();

        // Since the error is an anonymous object, we need to use dynamic
        dynamic dynamicError = errorObj!;
        Assert.NotNull(dynamicError.error);
        Assert.Equal("InvalidODataOperation", dynamicError.error.code);
        Assert.Equal("An error occurred while processing the OData request", dynamicError.error.message);
    }

    [Fact]
    public void OnException_WithNonODataException_DoesNotHandleException()
    {
        // Arrange
        var exception = new ArgumentException("Some non-OData exception");
        var context = CreateExceptionContext(exception);

        // Act
        _filter.OnException(context);

        // Assert
        context.ExceptionHandled.Should().BeFalse();
        context.Result.Should().BeNull();
    }

    private static ExceptionContext CreateExceptionContext(Exception exception, bool isODataRequest = false)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Request.QueryString = new QueryString("?$filter=Name eq 'Test'");

        if (isODataRequest)
        {
            // Set up OData feature by adding a marker item
            httpContext.Items["Microsoft.AspNetCore.OData.Extensions.ODataFeature"] = new object();
        }

        var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());

        return new ExceptionContext(actionContext, []) { Exception = exception };
    }
}
