using ProScoring.Domain.Common;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;

namespace ProScoring.BusinessLogic.ServiceInterfaces;

public interface IOrganizingAuthorityService
{
    Task<OrganizingAuthority> CreateAsync(OrganizingAuthorityUploadDto dto);
    Task<OrganizingAuthority> CreateAsync(OrganizingAuthority organizingAuthority);
    Task DeleteAsync(string id);
    Task<IEnumerable<OrganizingAuthority>> GetAllAsync();

    /// <summary>
    /// Gets all unique countries from organizing authorities, filtered by user permissions.
    /// </summary>
    /// <returns>A list of all unique countries.</returns>
    Task<List<string>> GetAllCountriesAsync();

    /// <summary>
    /// Gets a queryable of organizing authorities filtered by user permissions for OData queries.
    /// </summary>
    /// <returns>A queryable of organizing authorities filtered by user permissions.</returns>
    Task<IQueryable<OrganizingAuthority>> GetFilteredQueryableForODataAsync();

    /// <summary>
    /// Gets all unique states from organizing authorities, filtered by user permissions.
    /// </summary>
    /// <returns>A list of all unique states.</returns>
    Task<List<string>> GetAllStatesAsync();

    /// <summary>
    /// Gets a paged list of organizing authority information DTOs.
    /// </summary>
    /// <param name="page">The page number (1-based).</param>
    /// <param name="pageSize">The number of items per page.</param>
    /// <param name="sortBy">The property to sort by.</param>
    /// <param name="sortOrder">The sort order (asc or desc).</param>
    /// <param name="filter">The filter string to apply.</param>
    /// <returns>A paged list of organizing authority information DTOs.</returns>
    Task<PagedList<OrganizingAuthorityInfoDto>> GetPagedListAsync(
        int page = 1,
        int pageSize = 10,
        string? sortBy = null,
        string? sortOrder = "asc",
        string? filter = null
    );

    Task<OrganizingAuthority?> GetByIdAsync(string id);
    Task<OrganizingAuthority> UpdateAsync(OrganizingAuthority organizingAuthority);
    Task<OrganizingAuthority> UpdateAsync(OrganizingAuthorityUploadDto dto);
}
