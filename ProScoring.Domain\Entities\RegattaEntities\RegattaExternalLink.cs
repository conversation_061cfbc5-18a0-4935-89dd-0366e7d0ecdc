using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents external links to be shown in regatta info page.
/// </summary>
public class RegattaExternalLink
    : LastChangeTrackingWithAutoInsertedIdBase,
        IHasForeignKeyConfiguration<RegattaExternalLink>
{
    #region constants

    /// <summary>
    /// The prefix used for RegattaExternalLink IDs.
    /// </summary>
    public const string ID_PREFIX = "L";

    #endregion

    #region properties

    /// <summary>
    /// Gets or sets the unique identifier for this external link.
    /// </summary>
    [Key]
    [MaxLength(10)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaExternalLink.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the ID of the link type.
    /// </summary>
    [Column(Order = 20)]
    public string LinkTypeId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the URL of the external link.
    /// </summary>
    [Column(Order = 30)]
    [Url(ErrorMessage = "Invalid URL. Be sure to include `http://` or `https://`")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the link type.
    /// </summary>
    [Column(Order = 40)]
    public virtual LinkType Type { get; set; } = null!;

    /// <summary>
    /// Gets or sets the ID of the regatta this link belongs to.
    /// </summary>
    [Column(Order = 50)]
    [ForeignKey(nameof(Regatta))]
    public required string RegattaId { get; set; }

    /// <summary>
    /// Gets or sets the regatta this link belongs to.
    /// </summary>
    public virtual Regatta Regatta { get; set; } = null!;

    #endregion

    #region DB Configuration

    /// <summary>
    /// Configures foreign key relationships for the RegattaExternalLink entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the RegattaExternalLink entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaExternalLink> entity)
    {
        entity
            .HasOne(e => e.Regatta)
            .WithMany(r => r.ExternalLinks)
            .HasForeignKey(e => e.RegattaId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(e => e.Type).WithMany().HasForeignKey(e => e.LinkTypeId).OnDelete(DeleteBehavior.Restrict);
    }

    #endregion
}
