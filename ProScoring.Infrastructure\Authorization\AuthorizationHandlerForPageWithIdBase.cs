using System.Web;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Base authorization handler for operations on pages with IDs.
/// Determines if the current user has permission to perform specific operations on pages in the system.
/// </summary>
/// <typeparam name="TRequirement">The type of requirement being handled.</typeparam>
public abstract class AuthorizationHandlerForPageWithIdBase<TRequirement>(
    IAuthorizationProvider authorizationProvider,
    NavigationManager navigationManager,
    string authType
) : AuthorizationHandler<TRequirement>
    where TRequirement : IAuthorizationRequirement
{
    private readonly IAuthorizationProvider _authorizationProvider = authorizationProvider;
    private readonly NavigationManager _navigationManager = navigationManager;
    private readonly string _authType = authType;

    /// <summary>
    /// Handles the authorization requirement for operations on pages with IDs.
    /// </summary>
    /// <param name="context">The authorization handler context.</param>
    /// <param name="requirement">The requirement being handled.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, TRequirement requirement)
    {
        if (context.User.Identity?.IsAuthenticated != true)
        {
            return;
        }

        // Extract the page ID from the query string
        string? pageId;
        if (context.Resource is DefaultHttpContext requestContext)
        {
            // Server-side context available
            pageId = requestContext.Request.Query["id"].ToString();
        }
        else
        {
            // No context (likely Blazor component) - use NavigationManager to get URL
            try
            {
                var uri = new Uri(_navigationManager.Uri);
                var queryParams = HttpUtility.ParseQueryString(uri.Query);
                pageId = queryParams?["id"];
            }
            catch
            {
                // Failed to parse URL, could log this
                pageId = null;
            }
        }

        if (string.IsNullOrEmpty(pageId))
        {
            return;
        }

        var isAuthorized = await _authorizationProvider.IsAuthorizedAsync(context.User, pageId, _authType);

        if (isAuthorized)
        {
            context.Succeed(requirement);
        }
    }
}
