using System.Diagnostics;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;

namespace Proscoring.TestData.Playwright.Helpers;

/// <summary>
/// Helper class for managing Aspire application lifecycle and browser interactions.
/// also manages the Playwright lifecycle. Can bypass starting aspire based on setting
/// in appsettings.json.
/// </summary>
public class AspireAppHelper
{
    private readonly ILogger<AspireAppHelper> _logger;
    private readonly IConfiguration _configuration;
    private Process? _aspireProcess;
    private IPlaywright? _playwright;
    private IBrowser? _browser;
    private IPage? _page;
    private readonly HttpClient _httpClient = new HttpClient();

    public AspireAppHelper(ILogger<AspireAppHelper> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    /// <summary>
    /// Starts the Aspire application and initializes Playwright.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task StartAspireAppAsync()
    {
        try
        {
            // Get configuration values
            string baseUrl = _configuration["AspireApp:BaseUrl"]!;
            var startupTimeoutSeconds = int.Parse(_configuration["AspireApp:StartupTimeoutSeconds"] ?? "60");
            var launchAspireApp = bool.Parse(_configuration["AspireApp:LaunchAspireApp"] ?? "true");

            if (launchAspireApp)
            {
                // Start Aspire app process
                var executablePath = _configuration["AspireApp:ExecutablePath"];
                var workingDirectory = _configuration["AspireApp:WorkingDirectory"];
                var arguments = _configuration["AspireApp:Arguments"];
                var inheritEnvironmentVariables = bool.Parse(
                    _configuration["AspireApp:InheritEnvironmentVariables"] ?? "true"
                );
                var useDotNetCli = bool.Parse(_configuration["AspireApp:UseDotNetCli"] ?? "false");

                _logger.LogInformation("Starting Aspire app from {ExecutablePath}", executablePath);

                var processStartInfo = new ProcessStartInfo();

                if (useDotNetCli)
                {
                    // Use dotnet CLI to run the app, which properly sets up environment variables
                    string? appExtension = Path.GetExtension(executablePath)?.ToLowerInvariant();

                    if (appExtension == ".dll")
                    {
                        processStartInfo.FileName = "dotnet";
                        processStartInfo.Arguments = $"\"{executablePath}\" {arguments ?? ""}";
                    }
                    else if (appExtension == ".csproj" || string.IsNullOrEmpty(appExtension))
                    {
                        processStartInfo.FileName = "dotnet";
                        processStartInfo.Arguments = $"run --project \"{executablePath}\" {arguments ?? ""}";
                    }
                    else
                    {
                        // Fall back to direct executable if not a .NET assembly or project
                        processStartInfo.FileName = executablePath;
                        processStartInfo.Arguments = arguments ?? "";
                    }

                    _logger.LogInformation(
                        "Using dotnet CLI to launch: {Command} {Args}",
                        processStartInfo.FileName,
                        processStartInfo.Arguments
                    );
                }
                else
                {
                    // Launch executable directly
                    processStartInfo.FileName = executablePath;
                    processStartInfo.Arguments = arguments ?? "";
                }

                processStartInfo.UseShellExecute = false;
                processStartInfo.CreateNoWindow = false;
                processStartInfo.WorkingDirectory = workingDirectory ?? Path.GetDirectoryName(executablePath) ?? "";
                processStartInfo.RedirectStandardOutput = true;
                processStartInfo.RedirectStandardError = true;

                // Default critical environment variables for Aspire if not set elsewhere
                SetDefaultAspireVariablesIfNotPresent(processStartInfo.EnvironmentVariables);

                // Set environment variables from configuration
                var envVarsSection = _configuration.GetSection("AspireApp:EnvironmentVariables");
                if (envVarsSection.Exists())
                {
                    _logger.LogInformation("Setting environment variables for Aspire process");
                    foreach (var envVar in envVarsSection.GetChildren())
                    {
                        var value = envVar.Value;
                        if (!string.IsNullOrEmpty(value))
                        {
                            _logger.LogDebug("Setting environment variable: {Key}={Value}", envVar.Key, value);
                            processStartInfo.EnvironmentVariables[envVar.Key] = value;
                        }
                    }
                }

                // Optionally inherit environment variables from parent process
                if (inheritEnvironmentVariables)
                {
                    _logger.LogDebug("Inheriting environment variables from parent process");
                    foreach (
                        var envVar in Environment.GetEnvironmentVariables().Cast<System.Collections.DictionaryEntry>()
                    )
                    {
                        var key = envVar.Key.ToString();
                        var value = envVar.Value?.ToString() ?? "";

                        if (!string.IsNullOrEmpty(key) && !processStartInfo.EnvironmentVariables.ContainsKey(key))
                        {
                            processStartInfo.EnvironmentVariables[key] = value;
                        }
                    }
                }

                // Debug log all environment variables
                _logger.LogDebug("Environment variables for Aspire process:");
                foreach (var key in processStartInfo.EnvironmentVariables.Keys)
                {
                    _logger.LogDebug("  {Key}={Value}", key, processStartInfo.EnvironmentVariables[key.ToString()!]);
                }

                _aspireProcess = new Process { StartInfo = processStartInfo };

                // Capture output for logging
                _aspireProcess.OutputDataReceived += (sender, args) =>
                {
                    if (!string.IsNullOrEmpty(args.Data))
                        _logger.LogInformation("Aspire process output: {Output}", args.Data);
                };

                _aspireProcess.ErrorDataReceived += (sender, args) =>
                {
                    if (!string.IsNullOrEmpty(args.Data))
                        _logger.LogWarning("Aspire process error: {Error}", args.Data);
                };

                if (!_aspireProcess.Start())
                {
                    throw new InvalidOperationException($"Failed to start Aspire process at {executablePath}");
                }

                _aspireProcess.BeginOutputReadLine();
                _aspireProcess.BeginErrorReadLine();
            }
            else
            {
                _logger.LogInformation("Skipping Aspire app launch as it's configured to be started by Visual Studio");
            }

            // Wait for app to become available
            _logger.LogInformation(
                "Waiting for Aspire app to become available (timeout: {Timeout}s)",
                startupTimeoutSeconds
            );
            var isAppReady = await WaitForApplicationReadyAsync(baseUrl, TimeSpan.FromSeconds(startupTimeoutSeconds));

            if (!isAppReady)
            {
                throw new TimeoutException(
                    $"Aspire app failed to start within the specified timeout of {startupTimeoutSeconds} seconds"
                );
            }

            // Initialize Playwright
            _logger.LogInformation("Initializing Playwright");
            _playwright = await Microsoft.Playwright.Playwright.CreateAsync();

            // Launch browser
            _browser = await _playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = false });

            // Create a new page
            _page = await _browser.NewPageAsync();

            // Navigate to the application
            _logger.LogInformation("Navigating to {BaseUrl}", baseUrl);
            await _page.GotoAsync(baseUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Aspire application");
            await ShutdownAsync();
            throw;
        }
    }

    /// <summary>
    /// Sets default Aspire environment variables if they're not already present.
    /// </summary>
    /// <param name="environmentVariables">The dictionary of environment variables to modify.</param>
    private void SetDefaultAspireVariablesIfNotPresent(
        System.Collections.Specialized.StringDictionary environmentVariables
    )
    {
        var baseUrl = _configuration["AspireApp:BaseUrl"];
        var hostPort = baseUrl?.Split(':').LastOrDefault()?.Split('/').FirstOrDefault() ?? "5000";
        var dashboardPort = (int.TryParse(hostPort, out int port) ? port + 1 : 5001).ToString();

        // Set ASPNETCORE_URLS if not present
        if (!environmentVariables.ContainsKey("ASPNETCORE_URLS"))
        {
            environmentVariables["ASPNETCORE_URLS"] = $"http://localhost:{hostPort}";
            _logger.LogDebug("Setting default ASPNETCORE_URLS: {Value}", environmentVariables["ASPNETCORE_URLS"]);
        }

        // Set DOTNET environment variables for Aspire Dashboard
        if (
            !environmentVariables.ContainsKey("DOTNET_DASHBOARD_OTLP_ENDPOINT_URL")
            && !environmentVariables.ContainsKey("DOTNET_DASHBOARD_OTLP_HTTP_ENDPOINT_URL")
        )
        {
            environmentVariables["DOTNET_DASHBOARD_OTLP_ENDPOINT_URL"] = $"http://localhost:{dashboardPort}";
            _logger.LogDebug(
                "Setting default DOTNET_DASHBOARD_OTLP_ENDPOINT_URL: {Value}",
                environmentVariables["DOTNET_DASHBOARD_OTLP_ENDPOINT_URL"]
            );
        }

        // Set Aspire environment to Development if not present
        if (!environmentVariables.ContainsKey("ASPNETCORE_ENVIRONMENT"))
        {
            environmentVariables["ASPNETCORE_ENVIRONMENT"] = "Development";
            _logger.LogDebug(
                "Setting default ASPNETCORE_ENVIRONMENT: {Value}",
                environmentVariables["ASPNETCORE_ENVIRONMENT"]
            );
        }

        if (!environmentVariables.ContainsKey("DOTNET_ENVIRONMENT"))
        {
            environmentVariables["DOTNET_ENVIRONMENT"] = "Development";
            _logger.LogDebug("Setting default DOTNET_ENVIRONMENT: {Value}", environmentVariables["DOTNET_ENVIRONMENT"]);
        }
    }

    /// <summary>
    /// Waits for the application to become ready by polling the base URL.
    /// </summary>
    /// <param name="baseUrl">The base URL of the application.</param>
    /// <param name="timeout">The maximum time to wait for the application to become ready.</param>
    /// <returns>True if the application is ready, false otherwise.</returns>
    private async Task<bool> WaitForApplicationReadyAsync(string baseUrl, TimeSpan timeout)
    {
        var startTime = DateTime.UtcNow;
        var pollingInterval = TimeSpan.FromSeconds(2);

        while (DateTime.UtcNow - startTime < timeout)
        {
            try
            {
                _logger.LogDebug("Checking if application is available at {BaseUrl}", baseUrl);
                var response = await _httpClient.GetAsync(baseUrl);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Application is now available");
                    return true;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogDebug("Application not yet available: {ErrorMessage}", ex.Message);
            }

            await Task.Delay(pollingInterval);
        }

        _logger.LogWarning("Timeout reached while waiting for application to become available");
        return false;
    }

    /// <summary>
    /// Gets the current Playwright page.
    /// </summary>
    /// <returns>The current Playwright page.</returns>
    public IPage GetPage()
    {
        if (_page == null)
        {
            throw new InvalidOperationException("Playwright page is not initialized. Call StartAspireAppAsync first.");
        }

        return _page;
    }

    /// <summary>
    /// Navigates to a specific page in the application.
    /// </summary>
    /// <param name="path">The relative path to navigate to.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task NavigateToAsync(string path)
    {
        if (_page == null)
        {
            throw new InvalidOperationException("Playwright page is not initialized. Call StartAspireAppAsync first.");
        }

        var baseUrl = _configuration["AspireApp:BaseUrl"];
        var url = $"{baseUrl!.TrimEnd('/')}/{path.TrimStart('/')}";

        _logger.LogInformation("Navigating to {Url}", url);
        await _page.GotoAsync(url);
    }

    /// <summary>
    /// Shuts down the Playwright and Aspire application.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task ShutdownAsync()
    {
        _logger.LogInformation("Shutting down Playwright and Aspire app");

        if (_browser != null)
        {
            await _browser.CloseAsync();
        }

        _playwright?.Dispose();

        // Only kill the process if we started it
        var launchAspireApp = bool.Parse(_configuration["AspireApp:LaunchAspireApp"] ?? "true");
        if (launchAspireApp && _aspireProcess != null && !_aspireProcess.HasExited)
        {
            _aspireProcess.Kill();
            _aspireProcess.Dispose();
        }
    }
}
