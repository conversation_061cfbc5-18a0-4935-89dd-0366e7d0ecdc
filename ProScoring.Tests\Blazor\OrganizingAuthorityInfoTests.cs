using AutoFixture;
using Bunit;
using FluentAssertions;
using ProScoring.Blazor.Components.Shared;
using ProScoring.Domain.Dtos;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the <see cref="OrganizingAuthorityInfo"/> component.
/// </summary>
public class OrganizingAuthorityInfoTests : TestContext
{
    private readonly Fixture _fixture;

    public OrganizingAuthorityInfoTests()
    {
        _fixture = new Fixture();
        JSInterop.Mode = JSRuntimeMode.Loose;
    }

    ~OrganizingAuthorityInfoTests()
    {
        Dispose(false);
    }

    #region Methods

    [Fact]
    public void CardLayout_WithFullData_DisplaysAllInformation()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-burgee-image']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.Find("[data-testid='oa-website-link']").Should().NotBeNull();
    }

    [Fact]
    public void CardLayout_WithMinimalData_DisplaysOnlyAvailableInformation()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.FindAll("[data-testid='oa-state']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-country']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-city']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-website-link']").Count.Should().Be(0);
    }

    [Fact]
    public void CardLayout_WithoutAdditionalInfo_HidesExtraDetails()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.ShowAdditionalInfo, false)
        );

        // Assert
        cut.Find("[data-testid='oa-info-card']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        // The city is now always shown in the card layout, so we need to check it exists rather than not exists
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-website-link']").Count.Should().Be(0);
    }

    [Fact]
    public void RowLayout_WithFullData_DisplaysAllInformation()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-burgee-image-small']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.Find("[data-testid='oa-email']").TextContent.Should().Contain(authority.Email!);
        cut.Find("[data-testid='oa-phone']").TextContent.Should().Contain(authority.Phone!);
        cut.Find("[data-testid='oa-website-link']").Should().NotBeNull();
    }

    [Fact]
    public void RowLayout_WithMinimalData_DisplaysOnlyAvailableInformation()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, true)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.FindAll("[data-testid='oa-state']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-country']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-city']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-website-link']").Count.Should().Be(0);
    }

    [Fact]
    public void RowLayout_WithoutAdditionalInfo_HidesExtraDetails()
    {
        // Arrange
        var authority = CreateFullAuthorityDto();

        // Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.ShowAdditionalInfo, false)
        );

        // Assert
        cut.Find("[data-testid='oa-info-row']").Should().NotBeNull();
        cut.Find("[data-testid='oa-name']").TextContent.Should().Contain(authority.Name);
        cut.Find("[data-testid='oa-state']").TextContent.Should().Contain(authority.State!);
        cut.Find("[data-testid='oa-country']").TextContent.Should().Contain(authority.Country!);
        cut.Find("[data-testid='oa-city']").TextContent.Should().Contain(authority.City!);
        cut.FindAll("[data-testid='oa-email']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-phone']").Count.Should().Be(0);
        cut.FindAll("[data-testid='oa-website-link']").Count.Should().Be(0);
    }

    [Fact]
    public void NullAuthority_DisplaysPlaceholder()
    {
        // Arrange & Act
        var cut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters.Add(p => p.Authority, null).Add(p => p.DisplayMode, DisplayMode.Card)
        );

        // Assert
        cut.Find("[data-testid='oa-info-placeholder']").Should().NotBeNull();
        cut.Markup.Should().Contain("No organizing authority information available");
    }

    [Fact]
    public void CustomStyles_AreAppliedCorrectly()
    {
        // Arrange
        var authority = CreateMinimalAuthorityDto();
        var customCardStyle = "background-color: red; width: 300px;";
        var customRowStyle = "background-color: blue; padding: 20px;";

        // Act - Card Mode
        var cardCut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Card)
                .Add(p => p.CardStyle, customCardStyle)
        );

        // Act - Row Mode
        var rowCut = RenderComponent<OrganizingAuthorityInfo>(parameters =>
            parameters
                .Add(p => p.Authority, authority)
                .Add(p => p.DisplayMode, DisplayMode.Row)
                .Add(p => p.RowStyle, customRowStyle)
        );

        // Assert
        cardCut.Find("[data-testid='oa-info-card']").GetAttribute("style").Should().Contain(customCardStyle);
        rowCut.Find("[data-testid='oa-info-row']").GetAttribute("style").Should().Contain(customRowStyle);
    }

    private OrganizingAuthorityInfoDto CreateFullAuthorityDto()
    {
        return new OrganizingAuthorityInfoDto
        {
            Id = "O123",
            Name = "Seattle Yacht Club",
            Email = "<EMAIL>",
            Phone = "************",
            Website = "https://seattleyachtclub.org",
            City = "Seattle",
            State = "Washington",
            Country = "USA",
            ImageId = "img123",
            Private = false,
        };
    }

    private OrganizingAuthorityInfoDto CreateMinimalAuthorityDto()
    {
        return new OrganizingAuthorityInfoDto
        {
            Id = "O456",
            Name = "Minimal Yacht Club",
            ImageId = null,
            Private = true,
        };
    }

    #endregion Methods
}
