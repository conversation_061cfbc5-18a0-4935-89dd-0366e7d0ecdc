using System.Threading.Tasks;
using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Blazor.Services;
using Radzen;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Tests for the Index page component.
/// </summary>
public class IndexPageTests : TestContext
{
    private readonly ILocalStorageServiceWithExpiration _mockLocalStorageService;
    private readonly NavigationManager _mockNavigationManager;
    private readonly AuthenticationStateProvider _mockAuthStateProvider;

    public IndexPageTests()
    {
        // Set up mock services
        _mockLocalStorageService = Substitute.For<ILocalStorageServiceWithExpiration>();
        _mockAuthStateProvider = Substitute.For<AuthenticationStateProvider>();

        // Register all services before accessing any of them
        Services.AddSingleton(_mockLocalStorageService);
        Services.AddSingleton(_mockAuthStateProvider);

        // Add Radzen services
        Services.AddScoped<DialogService>();
        Services.AddScoped<NotificationService>();
        Services.AddScoped<TooltipService>();
        Services.AddScoped<MainLayoutContextService>();

        // Configure JSInterop
        JSInterop.Mode = JSRuntimeMode.Loose;

        // Get the navigation manager after registering all services
        _mockNavigationManager = Services.GetRequiredService<NavigationManager>();
    }

    [Fact]
    public void Index_ShouldRenderAboutCardByDefault()
    {
        // Arrange & Act
        var cut = RenderComponent<ProScoring.Blazor.Components.Pages.Index>();

        // Assert
        cut.Find(".rz-card-header strong").TextContent.Should().Be("About ProScoring");
    }

    [Fact]
    public async Task Index_ShouldSaveSelectionToLocalStorage()
    {
        // Arrange
        var cut = RenderComponent<ProScoring.Blazor.Components.Pages.Index>();

        // Get the property using reflection
        var property = typeof(ProScoring.Blazor.Components.Pages.Index).GetProperty(
            "selectedCard",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance
        );

        // Act - Set the property value to 1 (Regattas)
        property?.SetValue(cut.Instance, 1);

        // Wait for async operations to complete
        await Task.Delay(100);

        // Assert
        // Verify that SetItemAsync was called
        await _mockLocalStorageService.Received().SetItemAsync(Arg.Any<string>(), Arg.Any<object>());
    }
}
