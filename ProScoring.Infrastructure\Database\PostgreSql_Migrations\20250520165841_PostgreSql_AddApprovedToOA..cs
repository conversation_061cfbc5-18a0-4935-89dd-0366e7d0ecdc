﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ProScoring.Infrastructure.Database.PostgreSql_Migrations
{
    /// <inheritdoc />
    public partial class PostgreSql_AddApprovedToOA : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Approved",
                table: "OrganizingAuthorities",
                type: "boolean",
                nullable: false,
                defaultValue: false)
                .Annotation("Relational:ColumnOrder", 5);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "U000000000",
                columns: new[] { "GivenName", "Surname" },
                values: new object[] { null, null });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Approved",
                table: "OrganizingAuthorities");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "U000000000",
                columns: new[] { "GivenName", "Surname" },
                values: new object[] { "<EMAIL>", "<EMAIL>" });
        }
    }
}
