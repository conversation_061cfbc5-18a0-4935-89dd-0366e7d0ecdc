namespace ProScoring.Blazor.Options;

/// <summary>
/// Options for local storage settings in the application.
/// </summary>
public class LocalStorageOptions
{
    /// <summary>
    /// The configuration section name for local storage options.
    /// </summary>
    public const string SECTION_NAME = "LocalStorage";

    /// <summary>
    /// Gets or sets the expiration timeout for local storage items in minutes.
    /// Default is 1440 minutes (24 hours).
    /// </summary>
    public int ExpirationTimeoutMinutes { get; set; } = 1440;
}
