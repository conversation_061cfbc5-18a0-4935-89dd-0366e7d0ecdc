using Aspire.Hosting;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;

namespace ProScoring.Tests;

/// <summary>
/// Custom WebApplicationFactory for the proscoring-blazor project. It spins up the AppHost and injects the environmental
/// variables from the project (webfrontend). To adapt it for other projects, you need to change the name of the
/// AppHost project, of the required dependencies to spin up and the project's designation defined in the AppHost; see (https://stackoverflow.com/a/79290713/10119).
/// </summary>
public class AspireTestingWebApplicationFactory<TEntryPoint> : WebApplicationFactory<TEntryPoint>
    where TEntryPoint : class
{
    private DistributedApplication? _app;
    private bool disposed;

    // constructor that inserts ITestOutputHelper
    public AspireTestingWebApplicationFactory()
        : base() { }

    /// <summary>
    /// Configure the WebHostBuilder for the test server. Spins up AppHost and waits for required resources to start.
    /// Obtains the environmental variables from the project (webfrontend) and injects them into the test server.
    /// </summary>

    //private readonly ITestOutputHelper _output;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        // local variables
        var environmentVariables = new Dictionary<string, string?>();

        // === Arrange

        // build and configure AppHost (§§)
        var appHost = DistributedApplicationTestingBuilder
            .CreateAsync<Projects.ProScoring_AppHost>()
            .GetAwaiter()
            .GetResult();
        appHost.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.AddStandardResilienceHandler();
        });

        // disable port randomization for tests
        // https://github.com/dotnet/aspire/discussions/6843#discussioncomment-11444891
        //appHost.Configuration["DcpPublisher:RandomizePorts"] = "false";

        // build the AppHost synchronously
        _app = appHost.BuildAsync().GetAwaiter().GetResult();

        // start AppHost
        // Note: As far as I understand, the containers are not run in DockerDesktop but in some in-memory thingy
        var resourceNotificationService = _app.Services.GetRequiredService<ResourceNotificationService>();
        _app.StartAsync().GetAwaiter().GetResult();

        // wait until the required resources are running (§§)
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        resourceNotificationService
            .WaitForResourceAsync("webfrontend", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(20));
        resourceNotificationService
            .WaitForResourceAsync("PostgresServer", KnownResourceStates.Running)
            .WaitAsync(TimeSpan.FromSeconds(20));
        stopwatch.Stop();
        Console.WriteLine($"Waited {stopwatch.ElapsedMilliseconds} ms for required resources to start");

        // = get env variables from project-under-test

        // find blazor frontend project in AppHost (§§) (adapted from https://github.com/dotnet/aspire/discussions/878#discussioncomment-9596424)
        var testProject = appHost.Resources.First(r => r.Name == "proscoring-blazor");

        // make sure it is a ProjectResource with an environment and
        // get the annotations marking the methods/fields that contain the environmental variables
        if (testProject is IResourceWithEnvironment && testProject.TryGetEnvironmentVariables(out var annotations))
        {
            // To invoke the callback functions to obtain the environmental variables, an EnvironmentCallbackContext
            // is required, which can be created from a DistributedApplicationExecutionContext, but this requires
            // certain services, that can be obtained from the AppHost's service registry. Thus, hijack the AppHosts services
            // to create the DistributedApplicationExecutionContext.
            var options = new DistributedApplicationExecutionContextOptions(DistributedApplicationOperation.Run)
            {
                ServiceProvider = _app.Services,
            };

            var executionContext = new DistributedApplicationExecutionContext(options);
            var environmentCallbackContext = new EnvironmentCallbackContext(executionContext);

            // materialize the environmental variables by calling the callbacks in the environmentCallbackContext
            foreach (var annotation in annotations)
            {
                annotation.Callback(environmentCallbackContext).GetAwaiter().GetResult();
            }

            // Translate environment variable __ syntax to :
            foreach (var (key, value) in environmentCallbackContext.EnvironmentVariables)
            {
                if (testProject is ProjectResource && key == "ASPNETCORE_URLS")
                    continue;

                var configValue = value switch
                {
                    string val => val,
                    IValueProvider v => v.GetValueAsync().AsTask().Result,
                    null => null,
                    _ => throw new InvalidOperationException($"Unsupported value, {value.GetType()}"),
                };

                if (configValue is not null)
                {
                    environmentVariables[key.Replace("__", ":")] = configValue;
                }
            }
            environmentVariables["ConnectionStrings:SQLiteConnection"] = "Data Source=:memory:";
        }

        // inject the environmental variables into the test server
        builder.ConfigureAppConfiguration(
            (_, cb) =>
            {
                cb.AddInMemoryCollection(environmentVariables);
                // NOTE: it seems that this is all done before the InitializeAsync of the Test Fixture
                // for this reason, the Sqlite has already been configured, and the connection string set.
                // ConnectionString is set in AddInfrastructureServices in Infrastructure.ServiceConfigurationExtensions
                // which is called when the Blazor application spins up
            }
        );
    }

    /// <summary>
    /// This method is part of the recommended dispose pattern. I assume that WebApplicationFactory already implements
    /// <code>public void Dispose()</code>, which calls <code>Dispose(disposing: true)</code>.
    /// </summary>
    protected override void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                // dispose the AppHost
                _app?.Dispose();
            }

            // free unmanaged resources and override finalizer
            // set large fields to null

            // Call the base class's Dispose method to ensure base class resources are disposed of
            base.Dispose(disposing);

            disposed = true;
        }
    }
}
