namespace ProScoring.Domain.Dtos;

public class FileUploadResult
{
    #region properties
    public int ErrorCode { get; set; }

    // public string? FileName { get; set; }
    public string? Id { get; set; }
    public string? StoredFileName { get; set; }
    public string? TrustedFileNameForDisplay { get; set; }
    #endregion

    public enum ErrorCodes
    {
        None,
        FileNull,
        FileEmpty,
        FileTooLarge,
        InvalidFileType,
        UnknownError,
    }
}
