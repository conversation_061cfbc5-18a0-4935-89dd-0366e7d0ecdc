<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <EnableSourceLink>true</EnableSourceLink>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>ProScoring.Blazor-49b61f80-2eed-4d14-bf6b-d9496502f802</UserSecretsId>
    <!-- Disable specific assembly attributes since we're using AssemblyInfo.cs -->
    <GenerateAssemblyVersionAttribute>false</GenerateAssemblyVersionAttribute>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Markdig" Version="0.41.1" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="9.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1" />
    <PackageReference Include="Radzen.Blazor" Version="7.0.5" />
    <PackageReference Include="Scalar.AspNetCore" Version="2.0.11" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    <ProjectReference Include="..\..\ProScoring.BusinessLogic\ProScoring.BusinessLogic.csproj" />
    <ProjectReference Include="..\..\ProScoring.Domain\ProScoring.Domain.csproj" />
    <ProjectReference Include="..\..\ProScoring.Infrastructure\ProScoring.Infrastructure.csproj" />
    <ProjectReference Include="..\..\ProScoring.ServiceDefaults\ProScoring.ServiceDefaults.csproj" />
    <ProjectReference Include="..\ProScoring.Blazor.Client\ProScoring.Blazor.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid.EntityFrameworkAdapter" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
  </ItemGroup>
</Project>
