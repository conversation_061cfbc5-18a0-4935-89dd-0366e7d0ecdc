using ProScoring.Blazor;
using ProScoring.Infrastructure.Database;
using Xunit.Abstractions;

namespace ProScoring.Tests;

/// <summary>
/// Perform functional unit tests on the HistoryService defined in proscoring.blazor.
/// The custom WebApplicationFactory spins up the AppHost and injects the environmental variables from the project (webfrontend).
/// </summary>
/// <remarks>
/// In order for the compiler to find the project to test, you need to add <code>public partial class Program { }</code> to
/// <code>Program.cs</code> in the project you want to test.
/// </remarks>
public sealed class TempTests : IClassFixture<AspireTestingWebApplicationFactory<Program>>, IAsyncLifetime
{
    private readonly ITestOutputHelper _output;
    private readonly AspireTestingWebApplicationFactory<Program> _factory;

    // ITestOutputHelper is directly passed in by the test framework
    public TempTests(AspireTestingWebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _output = output;
        _factory = factory;
    }

    // grants access to the Services defined in UnitTestsInAspire.Web
    // InitializeAsync throws an exception if the scope could not be created, so it is never null when used
    private IServiceScope _scope = null!;

    /// <summary>
    /// Initializes the test environment.
    /// Creates the scope for the UnitTestsInAspire.Web services.
    /// </summary>
    public async Task InitializeAsync()
    {
        // === Spinning up AppHost and copying the environmantal variables to UnitTestsInAspire.Web is
        // done in the custon WebApplicationFactory

        // get ServiceProvider from UnitTestsInAspire.Web (for obtain dependencies from DI)
        // I store the scope as class variable, so that each test can derive it's own service. I think that if all tests were
        // to use the same service and run in parallel, they might interfere with each other (e.g. concurrent DBcontext access)
        _scope = _factory.Services.CreateScope();

        if (_scope == null)
        {
            throw new InvalidOperationException("ServiceScope could not be created for UnitTestsInAspire.Web.");
        }

        // development: Verify the configuration within the application
        // Note: The connection string is found in this way,
        // even if the ConnectionStrings:applicationDb is not present in the appsettings.json
        // Surprisingly, the Service does not pick up the connection string,
        // unless at least a dummy string is present in the appsettings.json
        // Let's set up the connection string for the database to be :memory:
        //var dbConnectionString = configuration.GetConnectionString("SQLiteConnection");
        //if (string.IsNullOrEmpty(dbConnectionString))
        //{
        //    throw new InvalidOperationException("ConnectionString for SQLiteConnection is missing.");
        //}

        // avoid CS1998 Async method lacks 'await' operators and will run synchronously
        await Task.CompletedTask;
    }

    [Fact]
    public void TestSomething()
    {
        // === Arrange
        // get Services via dependency injection
        var serviceProvider = _scope.ServiceProvider;

        // === Act

        var dbContext = serviceProvider.GetRequiredService<ApplicationDbContext>();

        // perform some tests and assert ...
        Assert.NotNull(dbContext);
        Assert.NotNull(dbContext.Files);
    }

    /// <summary>
    /// Disposes the scope for the UniTestsInAspire.Web services.
    /// </summary>
    /// <returns></returns>
    public async Task DisposeAsync()
    {
        _scope?.Dispose();

        // avoid CS1998 Async method lacks 'await' operators and will run synchronously
        await Task.CompletedTask;
    }
}
