using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// Represents a group of boats in a regatta, like "PHRF", "ORC", "PHRF non-spin", etc.
/// This is what YachtScoring calls a "Division".
/// Boats should then be scored in classes that race together.
/// One-design boats are both fleet and class, or their Fleet could be PHRF, ORC, etc.
/// </summary>
public class RegattaFleet : RegattaEntityBase<RegattaFleet>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaFleet IDs.
    /// </summary>
    public const string ID_PREFIX = "RF";

    #endregion

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaFleet.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the name of the fleet.
    /// </summary>
    [Column(Order = 20)]
    [StringLength(50, MinimumLength = 3)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description of the fleet.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether a rating is required for boats in this fleet.
    /// </summary>
    [Column(Order = 100)]
    public bool RatingRequired { get; set; } = false;

    /// <summary>
    /// Gets or sets a value indicating whether crew information is required for boats in this fleet.
    /// </summary>
    [Column(Order = 110)]
    public bool CrewRequired { get; set; } = false;

    #endregion
}
