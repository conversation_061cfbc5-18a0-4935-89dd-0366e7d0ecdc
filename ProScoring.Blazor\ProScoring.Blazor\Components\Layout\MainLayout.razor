@inherits LayoutComponentBase
@implements IAsyncDisposable

@using Blazored.LocalStorage
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Rendering
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Hosting
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Logging
@using Microsoft.JSInterop
@using ProScoring.Blazor.Components.Account
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services
@using ProScoring.Domain.Entities
@using <PERSON><PERSON>zen
@using Radzen.Blazor
@using System.Reflection
@using System.Text.Json

@inject AuthenticationStateProvider AuthenticationStateProvider
@inject DialogService DialogService
@inject IdentityRedirectManager RedirectManager
@inject IJSRuntime JSRuntime
@inject ILocalStorageService LocalStorage
@inject ILogger<MainLayout> Logger
@inject IWebHostEnvironment Environment
@inject MainLayoutContextService LayoutContext
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@* Page title *@
<PageTitle>ProScoring</PageTitle>

<RadzenDialog />
<RadzenNotification />
<RadzenTooltip />

<!-- Add AntiforgeryToken to the page -->
<AntiforgeryToken />

<RadzenLayout>
    <RadzenSidebar @bind-Expanded="@IsMenuVisible" Responsive="@(!_isLargeScreen)"
        @attributes="@("main-sidebar".AsTestId())">
        <RadzenPanelMenu @attributes="@("sidebar-menu".AsTestId())">
            <RadzenPanelMenuItem Text="Home" Icon="home" Path="/" @attributes="@("menu-item-home".AsTestId())" />
            <RadzenPanelMenuItem Text="About" Icon="info" Path="/about" @attributes="@("menu-item-about".AsTestId())" />
            <RadzenPanelMenuItem Text="Contact" Icon="contact_phone" Path="/contact"
                @attributes="@("menu-item-contact".AsTestId())" />
            <RadzenPanelMenuItem Text="Documentation" Icon="description" Path="/documentation"
                @attributes="@("menu-item-documentation".AsTestId())" />
            <RadzenPanelMenuItem Text="Settings" Icon="settings" Path="/settings"
                @attributes="@("menu-item-settings".AsTestId())" />
            <RadzenPanelMenuItem Text="Logout" Icon="logout" Path="/logout"
                @attributes="@("menu-item-logout".AsTestId())" />
            <RadzenPanelMenuItem Text="Demo Pages" Icon="code" @attributes="@("menu-item-demos".AsTestId())">
                <RadzenPanelMenuItem Text="OA Info Component" Icon="sailing" Path="/demo/organizing-authority-info"
                    @attributes="@("menu-item-oa-info-demo".AsTestId())" />
            </RadzenPanelMenuItem>
        </RadzenPanelMenu>
    </RadzenSidebar>
    <RadzenHeader @attributes="@("main-header".AsTestId())">
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
            JustifyContent="JustifyContent.SpaceBetween" Style="width: 100%">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                <RadzenSidebarToggle Click="@ToggleMenu" Icon="menu" @attributes="@("sidebar-toggle".AsTestId())" />
                <RadzenLabel Text="Menu" @attributes="@("header-menu-label".AsTestId())" />
            </RadzenStack>

            <AuthorizeView>
                <Authorized>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End">
                        <RadzenSplitButton AlwaysOpenPopup=true Text=@_displayName Icon="settings"
                            IconPosition="IconPosition.Left" ButtonStyle="ButtonStyle.Secondary"
                            Click="@(args => OnUserOptionsButtonClick(args))" Style="margin: 0.5rem;"
                            @attributes=@("user-options-split-button".AsTestId())>
                            <RadzenSplitButtonItem Text="My Account" Value="MyAccount" Icon="account_circle"
                                @attributes="@("my-account-menu-item".AsTestId())" />
                            <RadzenSplitButtonItem Text="Logout" Value="Logout" Icon="logout"
                                @attributes="@("logout-menu-item".AsTestId())" />
                        </RadzenSplitButton>
                    </RadzenStack>
                </Authorized>
                <NotAuthorized>
                    @if (LayoutContext.LoginButtonVisible)
                    {
                        <RadzenButton Text="Login" Icon="login" Click="@(async () => await OpenLoginDialog())"
                            ButtonStyle="ButtonStyle.Secondary" @attributes="@("header-login-button".AsTestId())"
                            id="login-button" />
                        <!-- Hidden button to help password managers recognize the login form -->
                        <button type="button" style="display:none;"
                            onclick="document.getElementById('loginForm').submit()">Login</button>
                    }
                </NotAuthorized>
            </AuthorizeView>
        </RadzenStack>
    </RadzenHeader>
    <main @attributes="@("main-content".AsTestId())">
        <RadzenBody>
            @Body
        </RadzenBody>
    </main>

    @if (Environment.IsDevelopment())
    {
        <RadzenFooter Style="padding: 8px; font-size: 12px; color: #666; text-align: center; border-top: 1px solid #eee;">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center" Gap="1rem">
                <div>Render Mode: <strong>@RendererInfo.Name</strong></div>
                <div>Environment: <strong>@Environment.EnvironmentName</strong></div>
                <div>App Version: <strong>@GetAppVersion()</strong></div>
            </RadzenStack>
        </RadzenFooter>
    }
</RadzenLayout>

<!-- Hidden logout form -->
<form id="logoutForm" method="post" action="/Account/Logout">
    <AntiforgeryToken />
    <input type="hidden" name="ReturnUrl" value="" />
</form>

<!-- Hidden login form to help password managers detect login fields -->
<form id="loginForm" method="post" action="/Account/Login" style="display:none;">
    <AntiforgeryToken />
    <input type="text" name="Input.Email" id="Input.Email" autocomplete="username email" />
    <input type="password" name="Input.Password" id="Input.Password" autocomplete="current-password" />
    <input type="checkbox" name="Input.RememberMe" id="Input.RememberMe" />
    <button type="submit">Login</button>
</form>

<script>
    // Function to submit the logout form
    function submitLogoutForm() {
        document.getElementById("logoutForm").submit();
    }

    // Function to get the anti-forgery token
    function getAntiForgeryToken() {
        const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
        if (tokenElement) {
            return tokenElement.value;
        }
        return null;
    }

    // Function to help password managers detect login fields
    function setupPasswordManagerSupport() {
        // This function uses MutationObserver to detect when the login dialog opens
        // and adds proper autocomplete attributes to help password managers
        const observer = new MutationObserver(mutations => {
            for (const mutation of mutations) {
                if (!mutation.addedNodes || mutation.addedNodes.length === 0) continue;

                for (const node of mutation.addedNodes) {
                    // Skip non-element nodes
                    if (node.nodeType !== Node.ELEMENT_NODE || !node.querySelector) continue;

                    // Check if this might be a login form
                    const hasLoginFields = node.querySelector('[id$="username"]') ||
                        node.querySelector('[autocomplete="username"]') ||
                        node.querySelector('input[type="password"]');

                    if (hasLoginFields) {
                        // Add proper autocomplete attributes if they don't exist
                        const inputs = node.querySelectorAll('input');
                        inputs.forEach(input => {
                            if ((input.type === 'text' || input.type === 'email') && !input.getAttribute('autocomplete')) {
                                input.setAttribute('autocomplete', 'username');
                            } else if (input.type === 'password' && !input.getAttribute('autocomplete')) {
                                input.setAttribute('autocomplete', 'current-password');
                            }
                        });
                    }
                }
            }
        });

        // Start observing the document
        observer.observe(document.body, {
            childList: true,  // Watch for changes to the direct children
            subtree: true     // Watch for changes in the entire subtree
        });
    }

    // Call the setup function when the page loads
    document.addEventListener('DOMContentLoaded', setupPasswordManagerSupport);

    // Function to perform login via AJAX
    async function performLogin(loginDataJson) {
        try {
            // Set up headers with content type and anti-forgery token
            const headers = {
                'Content-Type': 'application/json'
            };

            const token = getAntiForgeryToken();
            if (token) {
                headers['RequestVerificationToken'] = token;
            }

            // Make the fetch request
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: headers,
                body: loginDataJson
            });

            if (!response.ok) {
                return JSON.stringify({
                    success: false,
                    message: `API error: ${response.status} ${response.statusText}`
                });
            }

            const result = await response.json();
            return JSON.stringify(result);
        } catch (error) {
            return JSON.stringify({
                success: false,
                message: `Error: ${error.message}`
            });
        }
    }
</script>

@code {
    #region Fields
    /// <summary>
    /// Indicates whether the menu is visible.
    /// </summary>
    private bool _isMenuVisible = false;

    /// <summary>
    /// Stores the previously saved menu visibility state.
    /// </summary>
    private bool _savedMenuVisibility = false;

    /// <summary>
    /// Key used for storing menu visibility in local storage.
    /// </summary>
    private const string MenuVisibilityKey = "ProScoring_MenuVisible";

    /// <summary>
    /// Indicates whether the component has been initialized.
    /// </summary>
    private bool _isInitialized = false;

    /// <summary>
    /// Indicates whether the screen is large enough to show the full menu.
    /// </summary>
    private bool _isLargeScreen = true;

    /// <summary>
    /// Breakpoint for large screen detection in pixels.
    /// </summary>
    private const int LargeScreenBreakpoint = 768;

    /// <summary>
    /// Display name for the current user.
    /// </summary>
    private string _displayName = string.Empty;

    /// <summary>
    /// Action to be executed after a successful login.
    /// </summary>
    private Action? _postSuccessfulLoginAction = null;
    #endregion Fields

    #region Lifecycle Methods
    /// <summary>
    /// Initializes the component.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // Register the OpenLoginDialog method with the layout context service
        LayoutContext.SetOpenLoginDialogMethod(OpenLoginDialog);

        // Get user information if authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true && user.Identity.Name != null)
        {
            var applicationUser = await UserManager.FindByNameAsync(user.Identity.Name);
            if (applicationUser != null)
            {
                _displayName = GetDisplayName(applicationUser);
            }
            else
            {
                _displayName = user.Identity.Name;
            }
        }

        // We'll defer all JavaScript interop calls to OnAfterRenderAsync
        // to avoid issues with server-side rendering
    }

    /// <summary>
    /// Handles component initialization after rendering to avoid issues during static rendering.
    /// Sets up window resize event listener and ensures we have the correct screen size.
    /// </summary>
    /// <param name="firstRender">True if this is the first time the component has been rendered.</param>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!firstRender)
            return;

        try
        {
            // Check screen size - this is safe to do after rendering
            await CheckScreenSizeAsync();

            // If we're on a large screen, try to load the saved state
            if (_isLargeScreen)
            {
                var storedValue = await LocalStorage.GetItemAsync<bool?>(MenuVisibilityKey);
                if (storedValue.HasValue)
                {
                    SavedMenuVisibility = storedValue.Value;
                }
            }

            // Set up window resize event listener using our custom JavaScript function
            await JSRuntime.InvokeVoidAsync("setupResizeHandler", DotNetObjectReference.Create(this));
        }
        catch (Exception ex)
        {
            // Log the exception in development mode
            if (Environment.IsDevelopment())
            {
                Logger.LogWarning(ex, "Error during component initialization after render.");
            }
            // In production, silently handle exceptions
        }
        finally
        {
            _isInitialized = true;
        }
    }

    /// <summary>
    /// Cleans up event listeners when the component is disposed.
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        try
        {
            // Only try to remove the event listener if the component was initialized
            if (!_isInitialized)
                return;

            // Use our custom JavaScript function to remove the event listener
            await JSRuntime.InvokeVoidAsync("removeResizeHandler", DotNetObjectReference.Create(this));
        }
        catch (Exception ex)
        {
            if (Environment.IsDevelopment())
            {
                Logger.LogWarning(ex, "Error removing event listener during component disposal.");
            }
        }
    }
    #endregion Lifecycle Methods

    #region Properties
    /// <summary>
    /// Gets or sets the saved menu visibility state.
    /// This property is used to control when the menu visibility state is saved to local storage.
    /// </summary>
    private bool SavedMenuVisibility
    {
        get => _savedMenuVisibility;
        set
        {
            IsMenuVisible = value;
            if (_savedMenuVisibility != value)
            {
                _savedMenuVisibility = value;
                _ = SaveMenuVisibilityStateIfNeededAsync();
            }
        }
    }

    /// <summary>
    /// Gets or sets whether the menu is visible.
    /// </summary>
    private bool IsMenuVisible
    {
        get => _isMenuVisible;
        set
        {
            _isMenuVisible = value;
            StateHasChanged();
        }
    }
    #endregion Properties

    #region Event Handlers
    /// <summary>
    /// Toggles the visibility of the sidebar menu.
    /// The property setter will handle persisting the state to local storage if needed.
    /// </summary>
    private void ToggleMenu()
    {
        SavedMenuVisibility = !SavedMenuVisibility;
    }

    /// <summary>
    /// Opens the login dialog when the login button is clicked.
    /// </summary>
    private async Task OpenLoginDialog(Action? PostLoginAction = null)
    {
        try
        {
            // Successful login - refresh the page to update authentication state
            // We'll just refresh the current page and let the component that called the login dialog
            // handle any post-login navigation
            _postSuccessfulLoginAction = PostLoginAction
            ?? (() => { NavigationManager.NavigateTo(NavigationManager.Uri, forceLoad: true); }); // Default to refresh page
            var options = new DialogOptions
            {
                CloseDialogOnOverlayClick = true,
                Style = "min-width:350px;max-width:450px;",
                CssClass = "rz-shadow-3"
            };

            // Open the login dialog with RadzenLogin component
            await DialogService.OpenAsync<Radzen.Blazor.RadzenLogin>("Login",
            new Dictionary<string, object>
            {
                // Define a named delegate for processing login within this method
                ["Login"] = EventCallback.Factory.Create<LoginArgs>(this, ProcessLogin),
                ["Username"] = "Email",
                ["AllowResetPassword"] = true,
                ["ResetPassword"] = EventCallback.Factory.Create<string>(this, (string _) =>
    NavigationManager.NavigateTo("/Account/ForgotPassword", forceLoad: true)),
                ["AllowRegister"] = true,
                ["Register"] = EventCallback.Factory.Create(this, () => NavigationManager.NavigateTo("/Account/Register", forceLoad:
    true)),
                ["AllowRememberMe"] = true,
                // Add data-testid attributes for testing
                ["LoginTextAttributes"] = new Dictionary<string, object> { ["data-testid"] = "submit-login-button" },
                // Add autocomplete attributes for password managers
                ["UserTextAttributes"] = new Dictionary<string, object>
                {
                    ["data-testid"] = "email-input",
                    ["autocomplete"] = "username email",
                    ["name"] = "username",
                    ["id"] = "username"
                },
                ["PasswordTextAttributes"] = new Dictionary<string, object>
                {
                    ["data-testid"] = "password-input",
                    ["autocomplete"] = "current-password",
                    ["name"] = "password",
                    ["id"] = "password"
                },
                ["RememberMeAttributes"] = new Dictionary<string, object> { ["data-testid"] = "remember-me-checkbox" }
            },
            options);
        }
        catch (Exception ex)
        {
            // Log the exception
            Logger.LogError(ex, "Error opening login dialog");

            // Show an error notification to the user
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "Error",
                Detail = "Could not open login dialog. Please try again or navigate to the login page.",
                Duration = 5000
            });

            // Fallback to the login page if the dialog fails
            NavigationManager.NavigateTo("/Account/Login", forceLoad: true);
        }
    }

    /// <summary>
    /// Handles the click event for user options in the split button.
    /// </summary>
    /// <param name="item">The selected menu item.</param>
    private async void OnUserOptionsButtonClick(RadzenSplitButtonItem item)
    {
        switch (item.Value)
        {
            case "Logout":
                // Submit the logout form with antiforgery token
                await JSRuntime.InvokeVoidAsync("submitLogoutForm");
                break;
            case "MyAccount":
                // Navigate to My Account page
                NavigationManager.NavigateTo("/Account/Manage", forceLoad: true);
                break;
            default:
                // Show notification for unknown option
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Warning,
                    Summary = "Unknown Option",
                    Detail = $"Option '{item.Value}' is not recognized.",
                    Duration = 4000
                });
                break;
        }
    }

    /// <summary>
    /// Gets the display name for a user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <returns>The user's display name.</returns>
    private string GetDisplayName(ApplicationUser user)
    {
        if (!string.IsNullOrEmpty(user.GivenName))
            return user.GivenName;

        if (!string.IsNullOrEmpty(user.Surname))
            return user.Surname;

        return user.UserName ?? string.Empty;
    }

    /// <summary>
    /// Gets the current application version from the assembly's informational version.
    /// </summary>
    /// <returns>The application informational version as a string.</returns>
    private string GetAppVersion()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()
            ?.InformationalVersion ?? "Unknown";
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting application version");
            return "Unknown";
        }
    }

    /// <summary>
    /// Processes the login attempt with the provided credentials.
    /// </summary>
    /// <param name="args">The login arguments containing username and password.</param>
    private async void ProcessLogin(LoginArgs args)
    {
        try
        {
            // First, close the dialog to prevent header modification issues
            await SafeCloseDialogAsync();

            // Call our API endpoint to handle the login
            var loginData = new
            {
                username = args.Username,
                password = args.Password,
                rememberMe = args.RememberMe
            };

            try
            {
                // Use our custom JavaScript function to make the login request
                var result = await JSRuntime.InvokeAsync<string>("performLogin",
                JsonSerializer.Serialize(loginData));

                // Parse the result using JsonDocument for more flexibility
                using var jsonDoc = JsonDocument.Parse(result);
                var root = jsonDoc.RootElement;

                // Check if login was successful
                bool isSuccess = IsPropertyTrue(root, "success");

                if (isSuccess)
                {
                    if (_postSuccessfulLoginAction != null)
                    {
                        _postSuccessfulLoginAction();
                    }
                    return;
                }

                // Check for two-factor authentication requirement
                if (IsPropertyTrue(root, "requiresTwoFactor"))
                {
                    NavigationManager.NavigateTo("/Account/LoginWith2fa", forceLoad: true);
                    return;
                }

                // Check for account lockout
                if (IsPropertyTrue(root, "isLockedOut"))
                {
                    NavigationManager.NavigateTo("/Account/Lockout", forceLoad: true);
                    return;
                }

                // If we get here, it's a general login failure
                string errorMessage = "Invalid login attempt.";

                // Try to get error message from the response
                if (root.TryGetProperty("message", out var messageElement) &&
                messageElement.ValueKind == JsonValueKind.String)
                {
                    var message = messageElement.GetString();
                    if (!string.IsNullOrEmpty(message))
                    {
                        errorMessage = message;
                    }
                }

                await SafeShowAlertAsync(errorMessage, "Login Failed");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during login API call");
                await SafeShowAlertAsync("An error occurred during login. Please try again.", "Login Error");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during login process");
            await SafeShowAlertAsync("An error occurred during login.", "Error");
        }
    }

    /// <summary>
    /// Helper method to check if a property in a JsonElement is true.
    /// Handles different JSON value kinds (boolean, string, number).
    /// </summary>
    /// <param name="element">The JsonElement to check</param>
    /// <param name="propertyName">The name of the property to check</param>
    /// <returns>True if the property exists and is true, false otherwise</returns>
    private bool IsPropertyTrue(JsonElement element, string propertyName)
    {
        if (!element.TryGetProperty(propertyName, out var propElement))
            return false;

        return propElement.ValueKind switch
        {
            JsonValueKind.True => true,
            JsonValueKind.String => propElement.GetString()?.Equals("true", StringComparison.OrdinalIgnoreCase) == true,
            JsonValueKind.Number => propElement.GetInt32() != 0,
            _ => false
        };
    }

    /// <summary>
    /// Safely closes the dialog, handling any exceptions.
    /// </summary>
    private async Task SafeCloseDialogAsync()
    {
        try
        {
            DialogService.Close();
            await Task.CompletedTask; // To make the method async
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Error closing dialog");
        }
    }

    /// <summary>
    /// Safely shows an alert dialog, falling back to a notification if the dialog fails.
    /// </summary>
    /// <param name="message">The message to display.</param>
    /// <param name="title">The title of the alert.</param>
    private async Task SafeShowAlertAsync(string message, string title)
    {
        try
        {
            await DialogService.Alert(message, title, new AlertOptions { OkButtonText = "OK" });
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Error showing alert dialog");
            // Show a notification instead if the dialog fails
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = title,
                Detail = message,
                Duration = 5000
            });
        }
    }

    /// <summary>
    /// JavaScript callback for window resize events.
    /// This method is invoked from JavaScript.
    /// </summary>
    [JSInvokable]
    public async Task OnWindowResize()
    {
        await CheckScreenSizeAsync();
    }
    #endregion Event Handlers

    #region Helper Methods
    /// <summary>
    /// Checks the current screen size and updates the isLargeScreen flag.
    /// This is safe to call after the component has rendered.
    /// </summary>
    private async Task CheckScreenSizeAsync()
    {
        try
        {
            // Get the current window width using our JavaScript function
            var width = await JSRuntime.InvokeAsync<int>("getWindowWidth");
            var wasLargeScreen = _isLargeScreen;
            _isLargeScreen = width >= LargeScreenBreakpoint;

            // Only take action if the screen size category changed
            if (wasLargeScreen == _isLargeScreen)
                return;

            // Handle transition to large screen
            if (!wasLargeScreen && _isInitialized)
            {
                var storedValue = await LocalStorage.GetItemAsync<bool?>(MenuVisibilityKey);
                if (storedValue.HasValue)
                {
                    // Use IsMenuVisible directly since this is not a user-initiated toggle action
                    // We don't want to trigger a save back to storage during a resize event
                    IsMenuVisible = storedValue.Value;
                }
            }

            // If transitioning to small screen, we let Radzen handle it
            // but we still need to trigger a re-render to update the Responsive property
            StateHasChanged();
        }
        catch (Exception ex)
        {
            if (Environment.IsDevelopment())
            {
                Logger.LogWarning(ex, "Error checking screen size.");
            }
        }
    }

    /// <summary>
    /// Saves the menu visibility state to local storage if needed.
    /// Only saves when the component is initialized and on a large screen.
    /// </summary>
    private async Task SaveMenuVisibilityStateIfNeededAsync()
    {
        // Only save to local storage if the component is fully initialized and we're on a large screen
        if (!_isInitialized || !_isLargeScreen)
            return;

        try
        {
            // Save the menu visibility state to local storage
            await LocalStorage.SetItemAsync(MenuVisibilityKey, _savedMenuVisibility);
        }
        catch (Exception ex)
        {
            // Log the exception in development mode
            if (Environment.IsDevelopment())
            {
                Logger.LogWarning(ex, "Error saving menu visibility state to local storage.");
            }
            // In production, silently handle exceptions during local storage access
        }
    }
    #endregion Helper Methods
}
