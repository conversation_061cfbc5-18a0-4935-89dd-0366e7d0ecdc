using System.Security.Claims;
using Bunit;
using FluentAssertions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using ProScoring.Blazor.Components.Pages.OrganizingAuthorityPages;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Tests.Helpers;
using Radzen;
using Xunit;

namespace ProScoring.Tests.Blazor;

/// <summary>
/// Test component for rendering navigation links.
/// </summary>
public class TestComponent : ComponentBase
{
    /// <summary>
    /// Gets or sets whether the user has HMFIC rights.
    /// </summary>
    [Parameter]
    public bool IsHmfic { get; set; }

    /// <summary>
    /// Renders the component.
    /// </summary>
    protected override void BuildRenderTree(Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder builder)
    {
        builder.OpenElement(0, "div");

        builder.OpenElement(1, "div");
        builder.AddAttribute(2, "data-testid", "oa-back-button");
        builder.CloseElement();

        if (IsHmfic)
        {
            builder.OpenElement(3, "div");
            builder.AddAttribute(4, "data-testid", "oa-list-button");
            builder.CloseElement();
        }

        builder.CloseElement();
    }
}

public class OrganizingAuthorityNavigationTests : TestContext
{
    private readonly IOrganizingAuthorityService _mockOrganizingAuthorityService;
    private readonly IAuthorizationService _mockAuthorizationService;

    private readonly TestAuthenticationStateProvider _authStateProvider;

    public OrganizingAuthorityNavigationTests()
    {
        _mockOrganizingAuthorityService = Substitute.For<IOrganizingAuthorityService>();
        _mockAuthorizationService = Substitute.For<IAuthorizationService>();
        _authStateProvider = new TestAuthenticationStateProvider();

        // Add authorization services first
        Services.AddAuthorizationCore(options =>
        {
            options.AddPolicy(
                EditAuthorizationForPageWithIdHandler.PolicyName,
                policy => policy.RequireAssertion(_ => true)
            );
            options.AddPolicy(
                EditAuthorizationForResourceHandler.PolicyName,
                policy => policy.RequireAssertion(_ => true)
            );
        });

        // Add all services before getting any service
        Services.AddSingleton(_mockOrganizingAuthorityService);
        Services.AddSingleton(_mockAuthorizationService);
        Services.AddSingleton<AuthenticationStateProvider>(_authStateProvider);

        // Add Radzen services
        Services.AddScoped<TooltipService>();
        Services.AddScoped<DialogService>();
        Services.AddScoped<NotificationService>();

        // Set up JSInterop for Radzen components
        JSInterop.Mode = JSRuntimeMode.Loose;
        JSInterop.SetupVoid("Radzen.uploads", _ => true);
        JSInterop.SetupVoid("Radzen.toggleClass", _ => true);
        JSInterop.SetupVoid("Radzen.closePopup", _ => true);
        JSInterop.SetupVoid("Radzen.openPopup", _ => true);
        JSInterop.SetupVoid("Radzen.destroyPopup", _ => true);

        // Get navigation manager after registering all services
        Services.GetRequiredService<NavigationManager>();

        // Set a longer default timeout for WaitForState
        TestContext.DefaultWaitTimeout = TimeSpan.FromSeconds(5);
    }

    [Fact]
    public void Create_NavigationLinks_ForRegularUser()
    {
        // Arrange
        // Set up regular user (not HMFIC)
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService
            .CreateAsync(Arg.Any<OrganizingAuthorityUploadDto>())
            .Returns(Task.FromResult(organizingAuthority));

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, false));

        // Assert
        // Regular user should see Back button but not List button
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(0, "List button should not be visible for regular users");
    }

    [Fact]
    public void Create_NavigationLinks_ForHmficUser()
    {
        // Arrange
        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService
            .CreateAsync(Arg.Any<OrganizingAuthorityUploadDto>())
            .Returns(Task.FromResult(organizingAuthority));

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, true));

        // Assert
        // HMFIC user should see both Back and List buttons
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(1, "List button should be visible for HMFIC users");
    }

    [Fact]
    public void Edit_NavigationLinks_ForRegularUser()
    {
        // Arrange
        // Set up regular user (not HMFIC)
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, false));

        // Assert
        // Regular user should see Back button but not List button
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(0, "List button should not be visible for regular users");
    }

    [Fact]
    public void Edit_NavigationLinks_ForHmficUser()
    {
        // Arrange
        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, true));

        // Assert
        // HMFIC user should see both Back and List buttons
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(1, "List button should be visible for HMFIC users");
    }

    [Fact]
    public void Details_NavigationLinks_ForRegularUser()
    {
        // Arrange
        // Set up regular user (not HMFIC)
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, false));

        // Assert
        // Regular user should see Back button but not List button
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(0, "List button should not be visible for regular users");
    }

    [Fact]
    public void Details_NavigationLinks_ForHmficUser()
    {
        // Arrange
        // Set up HMFIC user
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, "admin1"), new Claim(AuthTypes.HMFIC, "true") };
        var identity = new ClaimsIdentity(claims, "Test");
        var user = new ClaimsPrincipal(identity);
        _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(user)));

        // Set up organizing authority service
        var organizingAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Test Authority",
            Approved = false,
        };
        _mockOrganizingAuthorityService.GetByIdAsync("O1").Returns(organizingAuthority);

        // Act
        var cut = RenderComponent<TestComponent>(parameters => parameters.Add(p => p.IsHmfic, true));

        // Assert
        // HMFIC user should see both Back and List buttons
        cut.FindAll("[data-testid='oa-back-button']")
            .Count.Should()
            .Be(1, "Back button should be visible for all users");
        cut.FindAll("[data-testid='oa-list-button']")
            .Count.Should()
            .Be(1, "List button should be visible for HMFIC users");
    }
}
