using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ValueGeneration;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Custom value generator for creating unique string IDs for entities.
/// Integrates with Entity Framework Core's value generation system.
/// </summary>
public class CustomIdValueGenerator(IIdGenerationUtilService idUtilsService, ILogger<CustomIdValueGenerator> logger)
    : ValueGenerator<string>,
        IValueGenerator
{
    private readonly IIdGenerationUtilService _idUtilsService = idUtilsService;
    private readonly ILogger<CustomIdValueGenerator> _logger = logger;

    /// <summary>
    /// Indicates whether this generator produces temporary values.
    /// </summary>
    public override bool GeneratesTemporaryValues => false;

    /// <summary>
    /// Generates the next value for the property based on the entity type.
    /// </summary>
    /// <param name="entry">The entity for which to generate a value.</param>
    /// <returns>The generated ID value.</returns>
    public override string Next(EntityEntry entry)
    {
        // get the object that the value will be applied to from the entity
        if (entry.Entity is not IHasAutoInsertedId entity)
        {
            _logger.LogWarning(
                "{nameOf(CustomIdValueGenerator)} is getting called for an entity of type "
                    + "{EntityType} that does not implement {nameOf(IHasAutoInsertedId)}",
                nameof(CustomIdValueGenerator),
                entry.Entity.GetType().Name,
                nameof(IHasAutoInsertedId)
            );
            return Guid.NewGuid().ToString();
        }
        // Custom logic to generate the Id value
        return _idUtilsService.GenerateId(entity);
    }
}
