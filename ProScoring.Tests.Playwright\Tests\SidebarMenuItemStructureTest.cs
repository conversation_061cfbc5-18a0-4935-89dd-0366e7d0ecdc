using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Test to examine the structure of sidebar menu items
/// </summary>
public class SidebarMenuItemStructureTest : IClassFixture<PlaywrightFixture>
{
    private readonly PlaywrightFixture _fixture;

    public SidebarMenuItemStructureTest(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task Examine_Sidebar_Menu_Item_Structure()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Navigate to the home page
            await page.GotoAsync(_fixture.Settings.BaseUrl);

            // Wait for the sidebar to be visible
            await page.WaitForSelectorAsync("[data-testid=main-sidebar]");

            // Make sure sidebar is visible
            var sidebar = await page.QuerySelectorAsync("[data-testid=main-sidebar]");
            if (sidebar == null)
                throw new Exception("Sidebar not found");

            var sidebarClass = await sidebar.GetAttributeAsync("class");
            if (sidebarClass == null)
                throw new Exception("Sidebar class not found");

            if (!sidebarClass.Contains("rz-sidebar-expanded"))
            {
                // Click the sidebar toggle to expand it
                await page.ClickAsync("[data-testid=sidebar-toggle]");
                await Task.Delay(500); // Wait for animation
            }

            // Get the menu items
            var menuItems = await page.QuerySelectorAllAsync("[data-testid=sidebar-menu] [data-testid^=menu-item-]");

            // Examine the first menu item's HTML structure
            if (menuItems.Count > 0)
            {
                var firstMenuItem = menuItems[0];

                // Get the HTML structure
                var outerHTML = await page.EvaluateAsync<string>("el => el.outerHTML", firstMenuItem);
                Console.WriteLine($"Menu Item HTML: {outerHTML}");

                // Get all child elements
                var children = await firstMenuItem.QuerySelectorAllAsync("*");

                foreach (var child in children)
                {
                    var tagName = await page.EvaluateAsync<string>("el => el.tagName", child);
                    var className = await child.GetAttributeAsync("class");
                    var textContent = await child.TextContentAsync();

                    Console.WriteLine($"Child Element: Tag={tagName}, Class={className}, Text={textContent}");
                }

                // Try to find the span that contains just the text (not the icon)
                var textSpans = await firstMenuItem.QuerySelectorAllAsync("span");
                foreach (var span in textSpans)
                {
                    var className = await span.GetAttributeAsync("class");
                    var textContent = await span.TextContentAsync();

                    Console.WriteLine($"Span: Class={className}, Text={textContent}");
                }
            }
        }
        finally
        {
            await page.CloseAsync();
        }
    }
}
