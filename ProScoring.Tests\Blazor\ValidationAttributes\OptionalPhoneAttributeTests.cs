using System.ComponentModel.DataAnnotations;
using FluentAssertions;
using ProScoring.Blazor.ValidationAttributes; // Assuming this is the namespace
using Xunit;

namespace ProScoring.Tests.Blazor.ValidationAttributes
{
    public class OptionalPhoneAttributeTests
    {
        private readonly OptionalPhoneAttribute _attribute = new OptionalPhoneAttribute();

        [Fact]
        public void IsValid_NullValue_ReturnsSuccess()
        {
            // Arrange
            var validationContext = new ValidationContext(new object());

            // Act
            var result = _attribute.GetValidationResult(null, validationContext);

            // Assert
            result.Should().Be(ValidationResult.Success);
        }

        [Fact]
        public void IsValid_EmptyString_ReturnsSuccess()
        {
            // Arrange
            var validationContext = new ValidationContext(new object());

            // Act
            var result = _attribute.GetValidationResult("", validationContext);

            // Assert
            result.Should().Be(ValidationResult.Success);
        }

        [Fact]
        public void IsValid_WhitespaceString_ReturnsSuccess()
        {
            // Arrange
            var validationContext = new ValidationContext(new object());

            // Act
            var result = _attribute.GetValidationResult("   ", validationContext);

            // Assert
            result.Should().Be(ValidationResult.Success);
        }

        [Theory]
        [InlineData("************")]
        [InlineData("****** 123 4567")]
        [InlineData("(*************")]
        [InlineData("************")]
        public void IsValid_ValidPhoneNumber_ReturnsSuccess(string validPhoneNumber)
        {
            // Arrange
            var validationContext = new ValidationContext(new object()) { DisplayName = "Phone" };

            // Act
            var result = _attribute.GetValidationResult(validPhoneNumber, validationContext);

            // Assert
            result.Should().Be(ValidationResult.Success);
        }

        [Theory]
        [InlineData("abc")]
        [InlineData("this is not a phone number")]
        [InlineData("!@#$%^&*()")] // Special characters only
        public void IsValid_InvalidPhoneNumber_ReturnsErrorResult(string invalidPhoneNumber)
        {
            // Arrange
            var validationContext = new ValidationContext(new object()) { DisplayName = "Phone Number" };

            // Act
            var result = _attribute.GetValidationResult(invalidPhoneNumber, validationContext);

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBe(ValidationResult.Success);
            // The default error message from PhoneAttribute is "The {0} field is not a valid phone number."
            // OptionalPhoneAttribute inherits from PhoneAttribute, so it should use the same error message.
            result
                ?.ErrorMessage.Should()
                .Be($"The {validationContext.DisplayName} field is not a valid phone number.");
        }
    }
}
