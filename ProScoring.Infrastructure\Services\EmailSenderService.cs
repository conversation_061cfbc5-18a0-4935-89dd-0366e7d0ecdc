using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Options;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Service for sending emails to application users for account management purposes.
/// </summary>
public class EmailSenderService(IEmailSender sender, IConfiguration configuration, ILogger<EmailSenderService> logger)
    : IEmailSender<ApplicationUser>
{
    private readonly IEmailSender _sender = sender ?? throw new ArgumentNullException(nameof(sender));
    private readonly ILogger<EmailSenderService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly EmailSenderOptions? _options = configuration
        .GetSection(EmailSenderOptions.SECTION_NAME)
        .Get<EmailSenderOptions>();

    /// <summary>
    /// Sends an account confirmation email to the user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <param name="email">The email address to send to.</param>
    /// <param name="confirmationLink">The confirmation link to include in the email.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
    {
        string htmlMessage = $"Please confirm your account by <a href='{confirmationLink}'>clicking here</a>";
        if (!_options?.Enabled ?? true)
        {
            _logger.LogWarning(
                "Email sending is disabled, email would have been sent to {Email} with body '{HtmlMessage}'",
                email,
                htmlMessage
            );
            return Task.CompletedTask;
        }
        return _sender.SendEmailAsync(email, "Confirm your email", htmlMessage);
    }

    /// <summary>
    /// Sends a password reset code to the user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <param name="email">The email address to send to.</param>
    /// <param name="resetCode">The password reset code.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
    {
        var htmlMessage = $"Please reset your password using the following code: {resetCode}";
        if (!_options?.Enabled ?? true)
        {
            _logger.LogWarning(
                "Email sending is disabled, email would have been sent to {Email} with body '{HtmlMessage}'",
                email,
                htmlMessage
            );
            return Task.CompletedTask;
        }
        return _sender.SendEmailAsync(email, "Reset your password", htmlMessage);
    }

    /// <summary>
    /// Sends a password reset link to the user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <param name="email">The email address to send to.</param>
    /// <param name="resetLink">The password reset link.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
    {
        string htmlMessage = $"Please reset your password by <a href='{resetLink}'>clicking here</a>.";
        if (!_options?.Enabled ?? true)
        {
            _logger.LogWarning(
                "Email sending is disabled, email would have been sent to {Email} with body '{HtmlMessage}'",
                email,
                htmlMessage
            );
            return Task.CompletedTask;
        }
        return _sender.SendEmailAsync(email, "Reset your password", htmlMessage);
    }
}
