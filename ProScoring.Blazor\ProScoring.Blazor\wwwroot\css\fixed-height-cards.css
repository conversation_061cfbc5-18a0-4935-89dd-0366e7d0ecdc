/* Equal size card styles - dynamically adjusts to match the tallest card in a row */
.fixed-height-card {
  display: flex;
  flex-direction: column;
  /* The height and width will be set dynamically by JavaScript */
  min-height: 100%;
  /* Minimum height to fill container */
  width: 100%;
  /* Default width */
  box-sizing: border-box;
  /* Include padding and border in dimensions */
  transition: height 0.2s ease-in-out, width 0.2s ease-in-out;
  /* Smooth transition for dimension changes */
}

/* Responsive styles for card layout */
.equal-height-cards .rz-g-col {
  flex: 0 0 25%;
  max-width: 25%;
}

@media (max-width: 992px) {

  /* Medium screens - adjust card sizes */
  .equal-height-cards .rz-g-col {
    flex: 0 0 33.33%;
    max-width: 33.33%;
  }
}

@media (max-width: 576px) {

  /* Small screens - full width cards */
  .equal-height-cards .rz-g-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.fixed-height-card .rz-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fixed-height-card .rz-stack {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Ensure content is properly aligned */
.fixed-height-card .rz-stack>.rz-text {
  text-align: center;
}

/* Ensure the image container has a fixed height */
.fixed-height-card .rz-image,
.fixed-height-card .rz-icon {
  height: 100px;
  width: 100px;
  object-fit: contain;
}

/* Ensure the name has a fixed height */
.fixed-height-card .rz-stack>.rz-text[data-testid="oa-name"] {
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure the location info has a fixed height */
.fixed-height-card .location-info {
  min-height: 24px;
  margin-top: auto;
}

/* Ensure the additional info has a fixed height */
.fixed-height-card .rz-stack:nth-child(3) {
  flex: 1;
  justify-content: flex-start;
}
