﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using ProScoring.Blazor.Components
@using ProScoring.Blazor.Components.Account
@using System.Linq
@using System.Reflection

<Router AppAssembly="typeof(Program).Assembly" AdditionalAssemblies="new[] { typeof(Client._Imports).Assembly }">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="routeData" DefaultLayout="@GetLayoutType(routeData)">
            <NotAuthorized>
                <RedirectToLogin />
            </NotAuthorized>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Page Not Found</PageTitle>
        <div class="container text-center mt-5">
            <h1 class="display-4">404</h1>
            <h2>Page Not Found</h2>
            <p role="alert" class="lead">
                Sorry, the page you requested could not be found.
            </p>
            <a href="/" class="btn btn-primary mt-3">Return to Home</a>
        </div>
    </NotFound>
</Router>

@code {
    /// <summary>
    /// Determines the layout type to use based on the route data.
    /// </summary>
    /// <param name="routeData">The route data for the current request.</param>
    /// <returns>The layout type to use for the current route.</returns>
    private Type GetLayoutType(RouteData routeData)
    {
        // Get the route template from the RouteData
        var relativePath = routeData.PageType.GetCustomAttributes(inherit: true)
        .OfType<Microsoft.AspNetCore.Components.RouteAttribute>()
        .FirstOrDefault()?.Template;

        // Check if the route is for an Account page
        if (relativePath != null &&
        (relativePath.StartsWith("Account/", StringComparison.OrdinalIgnoreCase) ||
        relativePath.StartsWith("/Account/", StringComparison.OrdinalIgnoreCase) ||
        relativePath.Equals("Account", StringComparison.OrdinalIgnoreCase) ||
        relativePath.Equals("/Account", StringComparison.OrdinalIgnoreCase)))
        {
            return typeof(Layout.AdminLayout);
        }

        // Default to MainLayout for all other routes
        return typeof(Layout.MainLayout);
    }
}
