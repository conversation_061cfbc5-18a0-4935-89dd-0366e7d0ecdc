using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class GuidishIdGenerationUtilServiceTests
    {
        [Fact]
        public void GenerateId_ReturnsCorrectLength()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero));
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);

            // Act
            var id = service.GenerateId("T", 10, false);

            // Assert
            Assert.Equal(10, id.Length);
        }

        [Fact]
        public void GenerateId_ReturnsUniqueIds()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero));
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);
            var generatedIds = new HashSet<string>();
            var numberOfIdsToGenerate = 100;

            // Act
            for (int i = 0; i < numberOfIdsToGenerate; i++)
            {
                // Increment time slightly to ensure uniqueness
                mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, i, TimeSpan.Zero));
                generatedIds.Add(service.GenerateId("T", 8, false));
            }

            // Assert
            Assert.Equal(numberOfIdsToGenerate, generatedIds.Count);
        }

        [Fact]
        public void GenerateId_StartsWithPrefixAndContainsValidChars()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero));
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);

            // Act
            var id = service.GenerateId("T", 8, false);

            // Assert
            Assert.True(id.Length > 0, "ID should not be empty."); // Ensure ID is not empty before checking characters
            Assert.True(id.StartsWith("T"), $"ID '{id}' should start with prefix 'T'.");
            Assert.True(
                id.All(c => char.IsLetterOrDigit(c) || c == '.'),
                $"ID '{id}' should only contain letters, digits, or dots."
            );
        }
    }
}
