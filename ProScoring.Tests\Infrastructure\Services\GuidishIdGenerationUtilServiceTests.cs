using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using Xunit;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class GuidishIdGenerationUtilServiceTests
    {
        [Fact]
        public void GenerateId_ReturnsReasonableLength()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero));
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);

            // Act
            var id = service.GenerateId("T", 10, false);

            // Assert
            // The service doesn't guarantee exact length due to time-based encoding
            // but should return a reasonable length (at least the prefix length)
            Assert.True(id.Length >= 1, $"ID '{id}' should have at least 1 character");
            Assert.True(id.Length <= 20, $"ID '{id}' should not be excessively long (was {id.Length})");
            Assert.StartsWith("T", id); // Should start with the prefix
        }

        [Fact]
        public void GenerateId_ReturnsUniqueIds()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            var baseTime = new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);
            var generatedIds = new HashSet<string>();
            var numberOfIdsToGenerate = 50; // Reduced to avoid DateTime overflow

            // Act
            for (int i = 0; i < numberOfIdsToGenerate; i++)
            {
                // Increment time by seconds, but handle overflow properly
                var currentTime = baseTime.AddSeconds(i);
                mockDateTimeProvider.UtcNow.Returns(currentTime);
                generatedIds.Add(service.GenerateId("T", 8, false));
            }

            // Assert
            Assert.Equal(numberOfIdsToGenerate, generatedIds.Count);
        }

        [Fact]
        public void GenerateId_StartsWithPrefixAndContainsValidChars()
        {
            // Arrange
            var mockDateTimeProvider = Substitute.For<IDateTimeOffsetProvider>();
            mockDateTimeProvider.UtcNow.Returns(new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero));
            var mockLogger = Substitute.For<ILogger<GuidishIdGenerationUtilService>>();
            var service = new GuidishIdGenerationUtilService(mockDateTimeProvider, mockLogger);

            // Act
            var id = service.GenerateId("T", 8, false);

            // Assert
            Assert.True(id.Length > 0, "ID should not be empty."); // Ensure ID is not empty before checking characters
            Assert.True(id.StartsWith("T"), $"ID '{id}' should start with prefix 'T'.");
            Assert.True(
                id.All(c => char.IsLetterOrDigit(c) || c == '.'),
                $"ID '{id}' should only contain letters, digits, or dots."
            );
        }
    }
}
