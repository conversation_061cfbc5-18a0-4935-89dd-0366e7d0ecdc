using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Proscoring.TestData.Playwright.DataLoaders;
using Proscoring.TestData.Playwright.Helpers;

namespace Proscoring.TestData.Playwright.Operations;

/// <summary>
/// Handles operations for creating organizing authorities for registered users.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="OrganizingAuthorityCreation"/> class.
/// </remarks>
/// <param name="logger">The logger instance.</param>
/// <param name="configuration">The configuration instance.</param>
/// <param name="jsonDataLoader">The JSON data loader instance.</param>
/// <param name="aspireHelper">The AspireAppHelper instance.</param>
public class OrganizingAuthorityCreation(
    ILogger<OrganizingAuthorityCreation> logger,
    IConfiguration configuration,
    JsonDataLoader jsonDataLoader,
    AspireAppHelper aspireHelper
)
{
    #region fields
    private readonly ILogger<OrganizingAuthorityCreation> _logger = logger;
    private readonly IConfiguration _configuration = configuration;
    private readonly JsonDataLoader _jsonDataLoader = jsonDataLoader;
    private readonly AspireAppHelper _aspireHelper = aspireHelper;

    #endregion
    #region constructors
    #endregion

    #region methods
    /// <summary>
    /// Creates organizing authorities for all registered users loaded from JSON data.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task CreateOrganizingAuthoritiesAsync()
    {
        _logger.LogInformation("Starting organizing authority creation process");

        var users = await _jsonDataLoader.LoadUsersAsync();
        if (users == null || !users.Any())
        {
            _logger.LogWarning("No users found to create organizing authorities for");
            return;
        }

        var organizingAuthorities = await _jsonDataLoader.LoadOrganizingAuthoritiesAsync();
        if (organizingAuthorities == null || !organizingAuthorities.Any())
        {
            _logger.LogWarning("No organizing authority data found");
            return;
        }

        _logger.LogDebug("Found {UserCount} users to create organizing authorities for", users.Count());
        _logger.LogDebug("Found {OACount} organizing authorities in seed data", organizingAuthorities.Count());

        var baseUrl =
            _configuration["AspireApp:BaseUrl"]
            ?? throw new InvalidOperationException("Web application URL is not configured");

        // foreach (var user in users.Skip(2))
        foreach (var user in users)
        {
            try
            {
                await CreateOrganizingAuthorityForUserAsync(baseUrl, user, organizingAuthorities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create organizing authority for user {Email}", user.Email);
            }
            // break; // only do one for now.
            await Task.Delay(2000);
        }

        _logger.LogInformation("Organizing authority creation process completed");
    }

    /// <summary>
    /// Creates an organizing authority for a specific user.
    /// </summary>
    /// <param name="baseUrl">The base URL of the application.</param>
    /// <param name="user">The user data.</param>
    /// <param name="organizingAuthorities">The collection of organizing authority data.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task CreateOrganizingAuthorityForUserAsync(
        string baseUrl,
        JsonDataLoader.UserData user,
        IEnumerable<JsonDataLoader.OrganizingAuthorityData> organizingAuthorities
    )
    {
        _logger.LogDebug("Creating organizing authority for user {Email}", user.Email);

        // Extract organizing authority info from email
        var oaData = GetOrganizingAuthorityForUser(user, organizingAuthorities);
        if (oaData == null)
        {
            _logger.LogWarning("No matching organizing authority found for user {Email}", user.Email);
            return;
        }

        try
        {
            // Login user
            await LoginUserAsync(baseUrl, user);

            // Get the page from AspireAppHelper
            var page = _aspireHelper.GetPage();
            if (page == null)
            {
                _logger.LogError(
                    "Page instance is null, cannot create organizing authority for user {Email}",
                    user.Email
                );
                return;
            }

            // Navigate to organizing authority creation page by clicking through UI
            _logger.LogDebug(
                "Navigating to organizing authority creation page through UI for user {Email}",
                user.Email
            );

            // Click on the Organizing Authorities navigation link
            await page.Locator("text=Organizing Authorities").ClickAsync();
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            _logger.LogDebug("Clicked on Organizing Authorities navigation link");

            // Click on the Create New button
            await page.Locator("text=Create New").ClickAsync();
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            _logger.LogDebug("Clicked on Create New button");

            _logger.LogDebug("Navigated to organization creation page for user {Email}", user.Email);

            // Fill in organizing authority details using data from seed file
            await page.FillAsync("[data-testid='oa-name-input']", oaData.Name.ToString());

            // Use the website from seed data if available
            if (oaData.Website != null)
            {
                await page.FillAsync("[data-testid='oa-website-input']", oaData.Website.ToString());
            }

            // Fill in additional fields if they exist in the form and in the data
            try
            {
                if (oaData.Email != null)
                {
                    await page.FillAsync("[data-testid='oa-email-input']", oaData.Email.ToString());
                }

                if (oaData.Phone != null)
                {
                    await page.FillAsync("[data-testid='oa-phone-input']", oaData.Phone.ToString());
                }

                // Fill address information if available
                if (oaData.AddressLine1 != null)
                {
                    await page.FillAsync("[data-testid='oa-address1-input']", oaData.AddressLine1.ToString());
                }

                if (oaData.AddressLine2 != null)
                {
                    await page.FillAsync("[data-testid='oa-address2-input']", oaData.AddressLine2.ToString());
                }

                if (oaData.City != null)
                {
                    await page.FillAsync("[data-testid='oa-city-input']", oaData.City.ToString());
                }

                if (oaData.State != null)
                {
                    await page.FillAsync("[data-testid='oa-state-input']", oaData.State.ToString());
                }

                if (oaData.PostalCode != null)
                {
                    await page.FillAsync("[data-testid='oa-postal-code-input']", oaData.PostalCode.ToString());
                }

                if (oaData.Country != null)
                {
                    await page.FillAsync("[data-testid='oa-country-input']", oaData.Country.ToString());
                }

                // Set private status if the field exists
                if (oaData.Private != null)
                {
                    bool isPrivate = (bool)oaData.Private;
                    if (isPrivate)
                    {
                        await page.ClickAsync("[data-testid='oa-private-checkbox']");
                    }
                }

                // Try to upload a burgee image if available
                try
                {
                    if (oaData.Name != null)
                    {
                        string formattedName = oaData.Name.ToString().Replace(" ", "_");
                        string burgeesDirectory =
                            @"c:\_dev\ProScoring\ProScoringNet9\Proscoring.TestData.Playwright\Data\Burgees";

                        if (Directory.Exists(burgeesDirectory))
                        {
                            // Get all files in the directory
                            var files = Directory.GetFiles(burgeesDirectory);

                            // Check if any file name matches the formatted name (regardless of extension)
                            string? burgeeFilePath = null;
                            foreach (var file in files)
                            {
                                string fileNameWithoutExt = Path.GetFileNameWithoutExtension(file);

                                if (
                                    string.Equals(fileNameWithoutExt, formattedName, StringComparison.OrdinalIgnoreCase)
                                )
                                {
                                    burgeeFilePath = file;
                                    _logger.LogDebug(
                                        "Found burgee image for {OaName} at {Path}",
                                        oaData.Name,
                                        burgeeFilePath
                                    );
                                    break;
                                }
                            }

                            // If a burgee image was found, upload it
                            if (burgeeFilePath != null)
                            {
                                await page.SetInputFilesAsync("input[data-testid='oa-burgee-input']", burgeeFilePath);
                                _logger.LogDebug("Successfully uploaded burgee image from {Path}", burgeeFilePath);
                            }
                            else
                            {
                                _logger.LogDebug("No burgee image found for {OaName}", oaData.Name);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Burgees directory not found at {Path}", burgeesDirectory);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("Could not process or upload burgee image: {Message}", ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("Some fields could not be filled: {Message}", ex.Message);
                // Continue with creation even if some fields couldn't be filled
            }

            // Submit the form
            await page.ClickAsync("[data-testid='create-oa-button']");

            // Wait for confirmation or redirect
            await page.WaitForURLAsync(
                new System.Text.RegularExpressions.Regex(
                    $"{baseUrl}/organizingauthorities",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase
                ),
                new PageWaitForURLOptions { Timeout = 10000 }
            );

            _logger.LogInformation(
                "Successfully created organizing authority '{OaName}' for user {Email}",
                oaData.Name,
                user.Email
            );
            // Log out after successfully creating the organizing authority
            await LogOutAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error during organizing authority creation for {Email}: {Message}",
                user.Email,
                ex.Message
            );
            throw;
        }
    }

    /// <summary>
    /// Logs out the current user by clicking on their email and then the Logout link.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task LogOutAsync()
    {
        try
        {
            var page = _aspireHelper.GetPage();
            if (page == null)
            {
                _logger.LogError("Page instance is null, cannot log out user");
                return;
            }

            _logger.LogDebug("Attempting to log out current user");

            // Click on the user email element in the navigation
            var userEmailElement = page.Locator("[data-testid='user-email']");
            if (!await userEmailElement.IsVisibleAsync())
            {
                _logger.LogWarning("User email element not found with data-testid selector, trying alternative");
                userEmailElement = page.Locator(".user-email, .user-dropdown");
            }

            await userEmailElement.ClickAsync();
            _logger.LogDebug("Clicked on user email element");

            // Wait a short time for the dropdown to appear
            await page.WaitForTimeoutAsync(500);

            // Click on the Logout link
            var logoutLink = page.Locator("text=Logout");
            await logoutLink.ClickAsync();
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            _logger.LogInformation("Successfully logged out user");
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Failed to log out user: {Message}", ex.Message);
            // Don't throw the exception as logging out is not critical
        }
    }

    /// <summary>
    /// Logs in a user through the UI.
    /// </summary>
    /// <param name="baseUrl">The base URL of the application.</param>
    /// <param="user">The user data to log in.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task LoginUserAsync(string baseUrl, JsonDataLoader.UserData user)
    {
        try
        {
            // Navigate to home page first
            await _aspireHelper.NavigateToAsync("/");

            var page = _aspireHelper.GetPage();
            _logger.LogDebug("Navigated to home page for user {Email}", user.Email);

            // Check if already logged in
            var logoutLocator = page.Locator("text=Logout");
            if (await logoutLocator.IsVisibleAsync())
            {
                _logger.LogDebug("User already logged in, logging out first");
                await logoutLocator.ClickAsync();
                await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            }

            // Look for login link
            var loginLocator = page.Locator("text=Login");
            await loginLocator.WaitForAsync(new() { State = WaitForSelectorState.Visible, Timeout = 5000 });
            _logger.LogDebug("Found login link, clicking it");
            await loginLocator.ClickAsync();
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            _logger.LogDebug("Navigated to login page for user {Email}", user.Email);

            // Login
            await page.FillAsync("[data-testid='email-input']", user.Email.ToString());
            await page.FillAsync("[data-testid='password-input']", UserRegistration.GenerateUserPassword(user.Id));
            await page.ClickAsync("[data-testid='login-button']");

            // await page.WaitForSelectorAsync("text=Hello, world!");
            _logger.LogDebug("Successfully logged in as {Email}", user.Email);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to login user {Email}: {Message}", user.Email, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Gets the organizing authority data that corresponds to a user based on their email.
    /// </summary>
    /// <param name="user">The user data.</param>
    /// <param="organizingAuthorities">The collection of organizing authority data.</param>
    /// <returns>The organizing authority data matching the user's email domain, or null if not found.</returns>
    private JsonDataLoader.OrganizingAuthorityData? GetOrganizingAuthorityForUser(
        JsonDataLoader.UserData user,
        IEnumerable<JsonDataLoader.OrganizingAuthorityData> organizingAuthorities
    )
    {
        string email = user.Email.ToString();
        string createdById = user.Id.ToString();

        // First try to find by createdById which is the most reliable match
        foreach (var oa in organizingAuthorities)
        {
            if (oa.CreatedById != null && oa.CreatedById.ToString() == createdById)
            {
                _logger.LogDebug("Found organizing authority match by createdById: {Id}", oa.Id);
                return oa;
            }
        }

        // If not found by createdById, try to extract the organizing authority name from the email
        // Expected format: <EMAIL>
        try
        {
            string[] parts = email.Split('@')[0].Split('.');
            if (parts.Length >= 3)
            {
                string oaNameFromEmail = parts[2].ToLower();

                foreach (var oa in organizingAuthorities)
                {
                    // Compare with name (simplified for matching)
                    string simplifiedName = oa.Name.ToString().ToLower().Replace(" ", "");
                    if (simplifiedName.Contains(oaNameFromEmail) || oaNameFromEmail.Contains(simplifiedName))
                    {
                        _logger.LogDebug("Found organizing authority match by email pattern: {Id}", oa.Id);
                        return oa;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Error parsing email for OA matching: {Message}", ex.Message);
        }

        _logger.LogWarning("No organizing authority match found for user {Email}", email);
        return null;
    }
    #endregion
}
