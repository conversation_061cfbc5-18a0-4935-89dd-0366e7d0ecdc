{"workbench.colorCustomizations": {"activityBar.activeBackground": "#b4e5b0", "activityBar.background": "#b4e5b0", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#878dd8", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#15202b99", "editorGroup.border": "#b4e5b0", "panel.border": "#b4e5b0", "sash.hoverBorder": "#b4e5b0", "sideBar.border": "#b4e5b0", "statusBar.background": "#90d98a", "statusBar.debuggingBackground": "#d38ad9", "statusBar.debuggingForeground": "#15202b", "statusBar.foreground": "#15202b", "statusBarItem.hoverBackground": "#6bcc63", "statusBarItem.remoteBackground": "#90d98a", "statusBarItem.remoteForeground": "#15202b", "titleBar.activeBackground": "#90d98a", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#90d98a99", "titleBar.inactiveForeground": "#15202b99"}, "dotnet.unitTests.runSettingsFile": "./.vscode/test.runsettings", "dotnetAcquisitionExtension.existingDotnetPath": [{"extensionId": "ms-dotnettools.csharp", "path": "C:\\Program Files\\dotnet\\dotnet.exe"}], "peacock.color": "#90d98a", "dotnet.defaultSolution": "ProScoring.Tests.Playwright.csproj", "terminal.integrated.env.windows": {"SkipPlaywrightInstall": "true"}, "terminal.integrated.env.linux": {"SkipPlaywrightInstall": "true"}, "terminal.integrated.env.osx": {"SkipPlaywrightInstall": "true"}}