// Framework imports
// Third-party imports
using Blazored.LocalStorage;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.OData;
using Microsoft.Extensions.Options;
using Microsoft.OData.ModelBuilder;
// Application imports
using ProScoring.Blazor.Components;
using ProScoring.Blazor.Components.Account;
using ProScoring.Blazor.Options;
using ProScoring.Blazor.Services;
using ProScoring.BusinessLogic.Extensions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Extensions;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.ServiceDefaults;
using Radzen;
using Scalar.AspNetCore;

namespace ProScoring.Blazor;

/// <summary>
/// Main entry point for the application.
/// </summary>
public class Program
{
    // Private constructor to prevent instantiation
    private Program() { }

    /// <summary>
    /// Application entry point.
    /// </summary>
    /// <param name="args">Command line arguments.</param>
    public static void Main(string[] args)
    {
        // Initialize the application builder
        var builder = WebApplication.CreateBuilder(args);
        builder.AddServiceDefaults();

        // Add API and problem details support
        builder.Services.AddProblemDetails();
        builder.Services.AddOpenApi();

        // Get base URL for configuration
        var baseUrl = builder.Configuration["Urls"]?.Split(';').FirstOrDefault();

        // Configure Blazor components
        builder
            .Services.AddRazorComponents()
            .AddInteractiveWebAssemblyComponents()
            .AddAuthenticationStateSerialization(options =>
            {
                // Serialize all claims for access in the browser
                // See: https://learn.microsoft.com/en-us/aspnet/core/blazor/security/?view=aspnetcore-9.0
                options.SerializeAllClaims = true;
            })
            .AddInteractiveServerComponents()
            .AddHubOptions(o =>
            {
                // Set maximum message size to 10MB
                o.MaximumReceiveMessageSize = 10 * 1024 * 1024;
            });

        // Add API controllers with OData support
        builder
            .Services.AddControllers(options =>
            {
                // Add OData exception filter
                options.Filters.Add<Filters.ODataExceptionFilterAttribute>();
            })
            .AddOData(options =>
            {
                // Enable OData query capabilities
                options.Select().Filter().OrderBy().Count().Expand().SetMaxTop(100);

                // Configure the OData model
                var modelBuilder = new ODataConventionModelBuilder();
                modelBuilder.Namespace = "ProScoring";
                modelBuilder.ContainerName = "ProScoringContainer";

                // Configure OrganizingAuthority entity
                var organizingAuthoritySet = modelBuilder.EntitySet<OrganizingAuthority>("OrganizingAuthorities");
                var organizingAuthorityType = organizingAuthoritySet.EntityType;
                organizingAuthorityType.HasKey(e => e.Id);

                // Add property descriptions for OrganizingAuthority
                organizingAuthorityType.Property(p => p.Name).IsRequired();
                organizingAuthorityType.Property(p => p.City);
                organizingAuthorityType.Property(p => p.State);
                organizingAuthorityType.Property(p => p.Country);
                organizingAuthorityType.Property(p => p.Email);
                organizingAuthorityType.Property(p => p.Phone);
                organizingAuthorityType.Property(p => p.Website);
                organizingAuthorityType.Property(p => p.Private);
                organizingAuthorityType.Property(p => p.ImageId);

                // Add navigation properties for OrganizingAuthority
                organizingAuthorityType.HasOptional(p => p.Image);
                organizingAuthorityType.HasOptional(p => p.CreatedBy);
                organizingAuthorityType.HasOptional(p => p.UpdatedBy);

                // Configure OrganizingAuthorityInfoDto entity
                var organizingAuthorityInfoSet = modelBuilder.EntitySet<OrganizingAuthorityInfoDto>(
                    "OrganizingAuthorityInfos"
                );
                var organizingAuthorityInfoType = organizingAuthorityInfoSet.EntityType;
                organizingAuthorityInfoType.HasKey(e => e.Id);

                // Add property descriptions for OrganizingAuthorityInfoDto
                organizingAuthorityInfoType.Property(p => p.Name).IsRequired();
                organizingAuthorityInfoType.Property(p => p.City);
                organizingAuthorityInfoType.Property(p => p.State);
                organizingAuthorityInfoType.Property(p => p.Country);
                organizingAuthorityInfoType.Property(p => p.Email);
                organizingAuthorityInfoType.Property(p => p.Phone);
                organizingAuthorityInfoType.Property(p => p.Website);
                organizingAuthorityInfoType.Property(p => p.Private);
                organizingAuthorityInfoType.Property(p => p.Approved);

                // Add the model to the OData configuration
                options.AddRouteComponents("odata", modelBuilder.GetEdmModel());
            });

        // Configure authentication services
        builder
            .Services.AddCascadingAuthenticationState()
            .AddScoped<IdentityUserAccessor>()
            .AddScoped<IdentityRedirectManager>()
            .AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

        // Configure authentication schemes
        builder
            .Services.AddAuthentication(options =>
            {
                options.DefaultScheme = IdentityConstants.ApplicationScheme;
                options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
            })
            .AddCookie(
                "Identity.Bearer",
                options =>
                {
                    options.LoginPath = "/Account/Login";
                    options.AccessDeniedPath = "/Account/AccessDenied";
                }
            )
            .AddIdentityCookies();

        // Add authorization services
        builder.Services.AddAuthorization();

        // Configure the cookie authentication options to return 401 for unauthorized API requests
        builder.Services.ConfigureApplicationCookie(options =>
        {
            options.Events.OnRedirectToLogin = context =>
            {
                if (context.Request.Path.StartsWithSegments("/api"))
                {
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    return Task.CompletedTask;
                }
                context.Response.Redirect(context.RedirectUri);
                return Task.CompletedTask;
            };
        });

        builder.Services.AddQuickGridEntityFrameworkAdapter();
        builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();

        // Add infrastructure and business logic services
        builder.AddInfrastructureServices();
        builder.AddBusinessLogicServices();
        //builder.SeedBusinessLogicData(); // TODO: this should not be called here, but when the app is run.
        builder.Services.AddSingleton<IDateTimeOffsetProvider, DateTimeOffsetProviderCpuTime>();

        // Add HttpContextAccessor and ServiceAuthorizationHelper
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddScoped<ServiceAuthorizationHelper>();

        if (!builder.Configuration.GetValue<bool>("CREATING_MIGRATION"))
        {
            // we don't need this for the running to create migrations.
            // trying to do it leads to DI issues we don't need to worry about.
            builder
                .Services.AddAuthorizationBuilder()
                .AddPolicy(
                    EditAuthorizationForResourceHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new EditAuthorizationForResourceHandler.Requirement());
                    }
                )
                .AddPolicy(
                    DeleteAuthorizationForResourceHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new DeleteAuthorizationForResourceHandler.Requirement());
                    }
                )
                .AddPolicy(
                    EditAuthorizationForPageWithIdHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new EditAuthorizationForPageWithIdHandler.Requirement());
                    }
                )
                .AddPolicy(
                    DeleteAuthorizationForPageWithIdHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new DeleteAuthorizationForPageWithIdHandler.Requirement());
                    }
                )
                .AddPolicy(
                    AuthorizeAllHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new AuthorizeAllHandler.Requirement());
                    }
                )
                .AddPolicy(
                    AuthorizeNoneHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new AuthorizeNoneHandler.Requirement());
                    }
                )
                .AddPolicy(
                    HmficAuthorizationHandler.PolicyName,
                    policy =>
                    {
                        policy.Requirements.Add(new HmficAuthorizationHandler.Requirement());
                    }
                );
            builder
                .Services.AddScoped<IAuthorizationHandler, EditAuthorizationForResourceHandler>()
                .AddScoped<IAuthorizationHandler, DeleteAuthorizationForResourceHandler>()
                .AddScoped<IAuthorizationHandler, EditAuthorizationForPageWithIdHandler>()
                .AddScoped<IAuthorizationHandler, DeleteAuthorizationForPageWithIdHandler>()
                .AddScoped<IAuthorizationHandler, AuthorizeAllHandler>()
                .AddScoped<IAuthorizationHandler, AuthorizeNoneHandler>()
                .AddScoped<IAuthorizationHandler, HmficAuthorizationHandler>();
        }

        // Add Radzen UI components and services
        builder.Services.AddRadzenComponents();

        // Register Radzen services
        builder.Services.AddScoped<DialogService>().AddScoped<NotificationService>().AddScoped<TooltipService>();

        // Configure logging
        builder.Services.AddLogging(logging =>
        {
            // Use Information level in production, Debug in development
            var logLevel = builder.Environment.IsDevelopment() ? LogLevel.Debug : LogLevel.Information;
            logging.SetMinimumLevel(logLevel);
            logging.AddConsole();
        });

        // Add application services
        builder
            .Services.AddScoped<MainLayoutContextService>()
            // Add local storage for client-side state persistence
            .AddBlazoredLocalStorage();

        // Configure local storage options
        builder.Services.Configure<LocalStorageOptions>(
            builder.Configuration.GetSection(LocalStorageOptions.SECTION_NAME)
        );

        // Add custom local storage service with expiration support
        builder.Services.AddScoped<ILocalStorageServiceWithExpiration, LocalStorageServiceWithExpiration>();

        // Add HTTP client for OrganizingAuthorityController with authentication support
        builder
            .Services.AddHttpClient<IOrganizingAuthorityHttpClient, OrganizingAuthorityHttpClient>(client =>
            {
                // Configure the client to use the current application's base URL
                // This ensures we're connecting to the same server that's running the app
                client.BaseAddress = new Uri(
                    builder.Configuration["BaseUrl"]
                        ?? builder.Configuration["Urls"]?.Split(';').FirstOrDefault()
                        ?? "http://localhost"
                );
            })
            .AddHttpMessageHandler<AuthenticatedHttpClientHandler>();

        // Register the authentication handler
        builder.Services.AddTransient<AuthenticatedHttpClientHandler>();

        // Add HTTP client for Documentation
        builder.Services.AddHttpClient("DocumentationClient");

        var app = builder.Build();

        app.MapDefaultEndpoints();

        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment())
        {
            // Development-specific middleware
            app.UseWebAssemblyDebugging();
            app.UseDeveloperExceptionPage();

            // API documentation
            app.MapOpenApi();
            app.MapScalarApiReference(options => options.Servers = []);
        }
        else
        {
            // Production error handling
            app.UseExceptionHandler("/error");

            // The default HSTS value is 30 days
            // See https://aka.ms/aspnetcore-hsts for more information
            app.UseHsts();
        }

        // Common middleware for all environments
        app.UseMigrationsEndPoint();
        app.UseHttpsRedirection();
        app.UseRouting();

        // Security middleware (order is important)
        app.UseAuthentication(); // Must come before Authorization
        app.UseAuthorization();
        app.UseAntiforgery(); // Must come after Authentication and Authorization

        // Error handling
        app.UseStatusCodePagesWithRedirects("/error/{0}");

        // Configure routes
        app.MapControllerRoute(
            name: "Error",
            pattern: "error/{statusCode:int}",
            defaults: new { controller = "Error", action = "Index" }
        );

        // Map static assets and Blazor components
        app.MapStaticAssets();
        app.MapRazorComponents<App>()
            .AddInteractiveServerRenderMode()
            .AddInteractiveWebAssemblyRenderMode()
            .AddAdditionalAssemblies(typeof(Client._Imports).Assembly);

        // Map identity and API endpoints
        app.MapAdditionalIdentityEndpoints();
        app.MapIdentityApi<ApplicationUser>();
        app.MapControllers();

        app.Run();
    }
}
