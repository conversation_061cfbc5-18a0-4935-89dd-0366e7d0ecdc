using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Authorization;

public interface IProScoringAuthorizationService : IAuthorizationProvider
{
    // because there is a 1:1 relationship between the targetId and the targetType, we can just use the targetId
    Task<UserAuthAction> CreateUserAuthActionAsync(string actorId, string targetId, string action);
    Task<List<UserAuthAction>> CreateUserAuthActionsAsync(string userId, IEnumerable<string> targetIds, string action);
    Task<List<UserAuthAction>> CreateUserAuthActionsAsync(
        IEnumerable<(string UserId, string TargetId, string Action)> sets
    );

    /// <summary>
    /// Deletes all the authorizations for the actor to the target
    /// </summary>
    /// <param name="actorId"></param>
    /// <param name="targetId"></param>
    /// <returns></returns>
    Task DeleteUserAuthActionAsync(string actorId, string targetId, string actionName);
    Task<int> DeleteAllUserAuthActionsForUserTargetAsync(string actorId, string targetId);
    Task DeleteUserAuthActionsAsync(IEnumerable<(string UserId, string TargetId, string Action)> sets);
    Task<IList<AuthAction>?> GetActionsAsync(string actorId, string targetId);
    Task<AuthAction?> GetActionAsync(string actorId, string targetId, string actionName);
    Task<IList<AuthAction>> GetAllowedActionsAsync(string actorId, string targetId);
    // 2025-02-10 -- decided to remove the update methods. We should just delete and add
    // there is no reason to keep the same action ID, which is all that this would really do
    // so does that mean that I should have a compound key of all three fields?
}
