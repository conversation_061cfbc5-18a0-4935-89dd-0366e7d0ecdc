using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using Proscoring.TestData.Playwright.DataLoaders;
using Proscoring.TestData.Playwright.Helpers;

namespace Proscoring.TestData.Playwright.Operations;

/// <summary>
/// Handles user registration operations through the UI.
/// </summary>
public class UserRegistration(ILogger<UserRegistration> logger, AspireAppHelper aspireHelper, JsonDataLoader dataLoader)
{
    #region fields
    private readonly ILogger<UserRegistration> _logger = logger;
    private readonly AspireAppHelper _aspireHelper = aspireHelper;
    private readonly JsonDataLoader _dataLoader = dataLoader;
    #endregion fields

    #region methods
    /// <summary>
    /// Registers users from the JSON data file through the UI.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task RegisterUsersFromJsonAsync()
    {
        _logger.LogInformation("Starting user registration process");

        var users = await _dataLoader.LoadUsersAsync();

        foreach (var user in users.Skip(1))
        {
            await <PERSON><PERSON>ing<PERSON><PERSON>serAsync(user);
            // Add a delay between registrations to avoid overloading the server
            // break; // only do one.
            await Task.Delay(2000);
        }

        _logger.LogInformation("User registration process completed");
    }

    /// <summary>
    /// Generates a password for a user based on their ID.
    /// </summary>
    /// <param name="userId">The user's ID.</param>
    /// <returns>A generated password string.</returns>
    internal static string GenerateUserPassword(string userId)
    {
        return $"P@ssw0rd_{userId}";
    }

    /// <summary>
    /// Registers a single user through the UI.
    /// </summary>
    /// <param name="userData">The user data to register.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task RegisterSingleUserAsync(JsonDataLoader.UserData userData)
    {
        try
        {
            _logger.LogInformation("Registering user {UserName}", userData.UserName);

            // Navigate to registration page through UI navigation instead of direct URL
            await NavigateToRegistrationPageAsync();

            var page = _aspireHelper.GetPage();

            // Wait for form to be fully loaded and interactive
            await page.WaitForSelectorAsync("#Input\\.GivenName", new() { State = WaitForSelectorState.Visible });
            _logger.LogDebug("Registration form loaded, starting to fill fields");

            // Fill the registration form
            await page.FillAsync("#Input\\.GivenName", userData.GivenName);
            await page.FillAsync("#Input\\.Surname", userData.Surname);
            await page.FillAsync("#Input\\.Email", userData.Email);

            // Generate a password with our helper method
            var password = GenerateUserPassword(userData.Id);

            await page.FillAsync("#Input\\.Password", password);
            await page.FillAsync("#Input\\.ConfirmPassword", password);

            _logger.LogDebug("Form filled successfully, proceeding to submit");

            // Submit the form
            await page.ClickAsync("button[type=submit]");

            // Wait for registration to complete
            await page.WaitForSelectorAsync("text=Registration confirmation");

            await page.ClickAsync("text=Click here to confirm your account");

            _logger.LogInformation("Successfully registered user {UserName}", userData.UserName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register user {UserName}", userData.UserName);
        }
    }

    /// <summary>
    /// Navigates to the registration page by clicking through the UI.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    private async Task NavigateToRegistrationPageAsync()
    {
        _logger.LogInformation("Navigating to registration page through UI");

        var page = _aspireHelper.GetPage();

        // Navigate to home page first
        await _aspireHelper.NavigateToAsync("/");

        // Click on the login button
        _logger.LogDebug("Clicking on the login button");
        await page.ClickAsync("text=Login");

        // Wait for the login page to load
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        // Click on the "Register as a new user" link
        _logger.LogDebug("Clicking on 'Register as a new user' link");
        await page.ClickAsync("text=Register as a new user");

        // Wait for registration page to load
        await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

        _logger.LogInformation("Successfully navigated to registration page");
    }
    #endregion methods
}
