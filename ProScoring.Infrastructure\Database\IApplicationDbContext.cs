using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Database;

public interface IApplicationDbContext
{
    DbSet<ActionHierarchy> ActionHierarchies { get; }
    DbSet<AuthAction> AuthActions { get; }
    DbSet<FileRecord> Files { get; }

    //DbSet<ImageMetadata> ImageMetadata { get; }
    DbSet<OrganizingAuthority> OrganizingAuthorities { get; }
    public DbSet<TargetType> TargetTypes { get; }
    DbSet<UserAuthAction> UserAuthActions { get; }
    DbSet<ApplicationUser> Users { get; }

    EntityEntry<TEntity> Entry<TEntity>(TEntity entity)
        where TEntity : class;
    int SaveChanges();
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
