using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a specific value for a boat rating.
/// Each rating can have multiple values of different types.
/// </summary>
public class RatingValue : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<RatingValue>
{
    public const string ID_PREFIX = "HV";
    public override string IdPrefix => ID_PREFIX;

    #region Properties
    /// <summary>
    /// Gets or sets the unique identifier of the rating value.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the rating this value belongs to.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [ForeignKey(nameof(Rating))]
    public required string RatingId { get; set; }

    /// <summary>
    /// Gets or sets the type of value being stored.
    /// </summary>
    [Required]
    [Column(Order = 30)]
    [ForeignKey(nameof(ValueType))]
    public required string ValueTypeId { get; set; }

    /// <summary>
    /// Gets or sets the numerical value.
    /// </summary>
    [Required]
    [Column(Order = 40)]
    public required float Value { get; set; }
    #endregion

    #region Navigation Properties
    /// <summary>
    /// Gets or sets the rating that this value belongs to.
    /// </summary>
    public virtual Rating Rating { get; set; } = null!;

    /// <summary>
    /// Gets or sets the type of this rating value.
    /// </summary>
    public virtual RatingValueType ValueType { get; set; } = null!;
    #endregion

    #region Methods
    /// <summary>
    /// Configures foreign key relationships for the RatingValue entity.
    /// </summary>
    /// <param name="entity">The entity type builder for RatingValue.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RatingValue> entity)
    {
        entity
            .HasOne(rv => rv.Rating)
            .WithMany(r => r.RatingValues)
            .HasForeignKey(rv => rv.RatingId)
            .OnDelete(DeleteBehavior.Cascade);
    }
    #endregion
}
