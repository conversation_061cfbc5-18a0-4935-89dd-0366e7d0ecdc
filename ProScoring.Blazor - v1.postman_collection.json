{"info": {"_postman_id": "3dae0491-c2dd-4a36-90c2-71faa1bfdbdd", "name": "ProScoring.Blazor | v1", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "2017824"}, "item": [{"name": "Account", "item": [{"name": "PerformExternalLogin", "item": [{"name": "/Account/PerformExternalLogin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "provider", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "returnUrl", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/PerformExternalLogin", "host": ["{{baseUrl}}"], "path": ["Account", "PerformExternalLogin"]}}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "provider", "value": "<string>", "description": "(Required) ", "type": "text"}, {"key": "returnUrl", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/PerformExternalLogin", "host": ["{{baseUrl}}"], "path": ["Account", "PerformExternalLogin"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "Logout", "item": [{"name": "/Account/Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "returnUrl", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/Logout", "host": ["{{baseUrl}}"], "path": ["Account", "Logout"]}}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "returnUrl", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/Logout", "host": ["{{baseUrl}}"], "path": ["Account", "Logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "Manage", "item": [{"name": "LinkExternalLogin", "item": [{"name": "/Account/Manage/LinkExternalLogin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "provider", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/Manage/LinkExternalLogin", "host": ["{{baseUrl}}"], "path": ["Account", "Manage", "LinkExternalLogin"]}}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "provider", "value": "<string>", "description": "(Required) ", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/Account/Manage/LinkExternalLogin", "host": ["{{baseUrl}}"], "path": ["Account", "Manage", "LinkExternalLogin"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "DownloadPersonalData", "item": [{"name": "/Account/Manage/DownloadPersonalData", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/Account/Manage/DownloadPersonalData", "host": ["{{baseUrl}}"], "path": ["Account", "Manage", "DownloadPersonalData"]}}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/Account/Manage/DownloadPersonalData", "host": ["{{baseUrl}}"], "path": ["Account", "Manage", "DownloadPersonalData"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}]}]}, {"name": "api", "item": [{"name": "File", "item": [{"name": "helloworld", "item": [{"name": "/api/File/helloworld", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/File/helloworld", "host": ["{{baseUrl}}"], "path": ["api", "File", "helloworld"]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/File/helloworld", "host": ["{{baseUrl}}"], "path": ["api", "File", "helloworld"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "upload", "item": [{"name": "/api/File/upload", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/File/upload", "host": ["{{baseUrl}}"], "path": ["api", "File", "upload"]}}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/File/upload", "host": ["{{baseUrl}}"], "path": ["api", "File", "upload"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}, {"name": "download", "item": [{"name": "{id}", "item": [{"name": "/api/File/download/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/File/download/:id", "host": ["{{baseUrl}}"], "path": ["api", "File", "download", ":id"], "variable": [{"key": "id", "value": "<string>", "description": "(Required) "}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/File/download/:id", "host": ["{{baseUrl}}"], "path": ["api", "File", "download", ":id"], "variable": [{"key": "id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": [], "body": ""}]}]}]}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "https://localhost:7292"}]}