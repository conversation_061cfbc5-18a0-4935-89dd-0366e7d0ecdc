// JavaScript functions for layout components

// Get the window width
window.getWindowWidth = function () {
    return window.innerWidth;
};

// Enable scrolling for demo pages
window.enableScrolling = function () {
    // Force scrolling to be enabled at all levels
    document.body.style.overflow = 'auto';
    document.documentElement.style.overflow = 'auto';
    document.body.style.height = 'auto';
    document.documentElement.style.height = 'auto';

    // Find the main content container and ensure it's scrollable
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.style.overflow = 'auto';
        mainContent.style.height = 'auto';
        mainContent.style.maxHeight = 'none';
    }

    // Find the RadzenBody and ensure it's scrollable
    const radzenBody = document.querySelector('.rz-body');
    if (radzenBody) {
        radzenBody.style.overflow = 'auto';
        radzenBody.style.height = 'auto';
        radzenBody.style.maxHeight = 'none';
    }

    // Find our demo page container and ensure it's scrollable
    const demoPage = document.querySelector('.scrollable-demo-page');
    if (demoPage) {
        demoPage.style.overflow = 'auto';
        demoPage.style.height = 'auto';
        demoPage.style.minHeight = '100vh';
    }

    // Add a class to the body to indicate scrolling is enabled
    document.body.classList.add('scrolling-enabled');

    // Force the page to recalculate layout
    window.dispatchEvent(new Event('resize'));

    // Log to console for debugging
    console.log('Scrolling enabled');
    console.log('Body height:', document.body.scrollHeight);
    console.log('Viewport height:', window.innerHeight);
};

// Store the resize handler function so we can remove it later
let resizeHandler = null;

// Set up resize handler
window.setupResizeHandler = function (dotNetReference) {
    // Create the handler function and store it
    resizeHandler = function () {
        dotNetReference.invokeMethodAsync('OnWindowResize');
    };

    // Add the event listener
    window.addEventListener('resize', resizeHandler);
};

// Remove resize handler
window.removeResizeHandler = function (dotNetReference) {
    // Only remove if we have a handler
    if (resizeHandler) {
        window.removeEventListener('resize', resizeHandler);
        resizeHandler = null;
    }
};

// Equalize card dimensions for organizing authority cards
window.equalizeCardDimensions = function (cardSelector = '[data-testid=oa-info-card]') {
    // Find all cards
    const cards = document.querySelectorAll(cardSelector);
    if (!cards || cards.length <= 1) {
        return; // No cards or only one card, nothing to equalize
    }

    // Reset any previously set heights and widths to get natural dimensions
    cards.forEach(card => {
        card.style.height = '';
        card.style.width = '';
    });

    // Allow the browser to recalculate natural dimensions
    setTimeout(() => {
        // Find the maximum height and width
        let maxHeight = 0;
        let maxWidth = 0;

        cards.forEach(card => {
            const rect = card.getBoundingClientRect();
            maxHeight = Math.max(maxHeight, rect.height);
            maxWidth = Math.max(maxWidth, rect.width);
        });

        // Set all cards to the maximum dimensions
        cards.forEach(card => {
            card.style.height = `${maxHeight}px`;
            card.style.width = `${maxWidth}px`;
        });

        console.log(`Equalized ${cards.length} cards to height: ${maxHeight}px, width: ${maxWidth}px`);
    }, 50); // Small delay to ensure the browser has recalculated natural dimensions
};

// Get the git commit hash
window.getGitCommitHash = function () {
    return new Promise((resolve, reject) => {
        // Make an AJAX request to get the git commit hash
        fetch('/api/version/git-hash')
            .then(response => {
                if (!response.ok) {
                    // If the API endpoint doesn't exist, return an empty string
                    resolve('');
                    return;
                }
                return response.text();
            })
            .then(hash => {
                resolve(hash || '');
            })
            .catch(error => {
                console.error('Error getting git commit hash:', error);
                resolve('');
            });
    });
};
