<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="Code Coverage" uri="datacollector://Microsoft/CodeCoverage/2.0">
        <Configuration>
          <CodeCoverage>
            <ModulePaths>
              <Include>
                <ModulePath>.*\.dll$</ModulePath>
              </Include>
              <Exclude>
                <ModulePath>.*\.Tests\.dll$</ModulePath>
                <ModulePath>.*\.*testadapter\.dll$</ModulePath>
              </Exclude>
            </ModulePaths>
            
            <!-- Files to exclude from code coverage -->
            <Sources>
              <Exclude>
                <Source>.*\\PostgreSql_Migrations\\.*</Source>
                <Source>.*\\Sqlite_Migrations\\.*</Source>
              </Exclude>
            </Sources>
            
            <!-- We recommend you do not change the following values: -->
            <UseVerifiableInstrumentation>True</UseVerifiableInstrumentation>
            <AllowLowIntegrityProcesses>True</AllowLowIntegrityProcesses>
            <CollectFromChildProcesses>True</CollectFromChildProcesses>
            <CollectAspDotNet>False</CollectAspDotNet>
          </CodeCoverage>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
