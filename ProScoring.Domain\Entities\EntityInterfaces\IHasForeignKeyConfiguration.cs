using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ProScoring.Domain.Entities.EntityInterfaces;

/// <summary>
/// Interface for entities that require foreign key configurations.
/// </summary>
/// <typeparam name="T">The entity type.</typeparam>
public interface IHasForeignKeyConfiguration<T>
    where T : class
{
    /// <summary>
    /// Configures foreign key relationships for the entity.
    /// </summary>
    /// <param name="entity">The entity type builder.</param>
    public static abstract void ConfigureForeignKeys(EntityTypeBuilder<T> entity);
}
