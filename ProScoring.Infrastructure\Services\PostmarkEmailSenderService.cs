using System;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PostmarkDotNet;
using ProScoring.Infrastructure.Exceptions;
using ProScoring.Infrastructure.Options;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Email sender service that uses Postmark API to send emails.
/// </summary>
public class PostmarkEmailSenderService(IConfiguration configuration, ILogger<PostmarkEmailSenderService> logger)
    : IEmailSender
{
    private readonly ILogger _logger = logger;
    private readonly EmailSenderOptions? _options = configuration
        .GetSection(EmailSenderOptions.SECTION_NAME)
        .Get<EmailSenderOptions>();

    /// <summary>
    /// Sends an email using the Postmark API.
    /// </summary>
    /// <param name="email">The recipient's email address.</param>
    /// <param name="subject">The subject of the email.</param>
    /// <param name="htmlMessage">The HTML body content of the email.</param>
    /// <returns>A task representing the asynchronous send operation.</returns>
    /// <exception cref="InvalidOperationException">Thrown when Postmark API configuration is missing.</exception>
    /// <exception cref="EmailException">Thrown when there's an error sending the email.</exception>
    public async Task SendEmailAsync(string email, string subject, string htmlMessage)
    {
        if (string.IsNullOrEmpty(_options?.ApiKey))
        {
            _logger.LogError("Postmark API key is not set, email will not be sent");
            throw new ConfigurationErrorsException("Postmark API key is not set.");
        }
        if (string.IsNullOrEmpty(_options.FromAddress))
        {
            _logger.LogError("Postmark From address is not set, email will not be sent");
            throw new ConfigurationErrorsException("Postmark From address is not set.");
        }
        var client = new PostmarkClient(_options.ApiKey);
        var message = new PostmarkMessage
        {
            To = email,
            From = _options.FromAddress,
            TrackOpens = true,
            Subject = subject,
            HtmlBody = htmlMessage,
        };
        PostmarkResponse sendResult;
        try
        {
            sendResult = await client.SendMessageAsync(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "failed to send email");
            throw new EmailException("Error in SendMessageAsync", ex);
        }
        if (sendResult.Status != PostmarkStatus.Success)
        {
            _logger.LogError("Failed to send email: {sendErrorMessage}", sendResult.Message);
            throw new EmailException("Failed to send email");
        }
    }
}
