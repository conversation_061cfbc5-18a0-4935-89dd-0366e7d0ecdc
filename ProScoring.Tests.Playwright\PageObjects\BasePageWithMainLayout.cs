using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Page object model for pages that use the main layout.
/// </summary>
public class BasePageWithMainLayout : BasePage
{
    #region Fields
    private readonly Lazy<SplitButtonTestObject> _userOptionsButtonLazy;
    #endregion Fields

    #region Constructors
    public BasePageWithMainLayout(IPage page, string pageUrl = null!)
        : base(page, pageUrl)
    {
        _userOptionsButtonLazy = new Lazy<SplitButtonTestObject>(() =>
        {
            return new SplitButtonTestObject(_page, UserOptionsButtonSelector);
        });
    }
    #endregion Constructors

    #region Properties
    public SplitButtonTestObject UserOptionsButton => _userOptionsButtonLazy.Value;
    #endregion Properties

    #region Selectors
    public static string LoginButtonSelector => "[data-testid=header-login-button]";
    public static string UserOptionsButtonSelector => "[data-testid=user-options-split-button]";
    public static string NavigationLinksSelector => "nav a";
    public static string LogoutOptionSelector => "[data-testid=logout-menu-item]";
    public static string MyAccountMenuSelector => "[data-testid=my-account-menu-item]";
    public static string SidebarSelector => "[data-testid=main-sidebar]";
    public static string SidebarToggleSelector => "[data-testid=sidebar-toggle]";
    public static string SidebarMenuItemsSelector => "[data-testid=sidebar-menu] [data-testid^=menu-item-]";

    public static string SidebarMenuItemSelector(string name) => $"[data-testid=menu-item-{name.ToLower()}]";

    public static string MainContentSelector => "[data-testid=main-content]";
    public static string MainHeaderSelector => "[data-testid=main-header]";
    #endregion Selectors

    #region Methods
    /// <summary>
    /// Verifies that the page has loaded correctly.
    /// </summary>
    /// <returns>A task representing the verification operation.</returns>
    public override async Task VerifyPageLoadedAsync()
    {
        // Wait for the main content to be visible
        await _page.WaitForSelectorAsync(MainContentSelector);
    }

    /// <summary>
    /// Clicks the login button.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickLoginButton()
    {
        await ClickAsync(LoginButtonSelector);
    }

    /// <summary>
    /// Checks if the login button is visible.
    /// </summary>
    /// <returns>True if the login button is visible, false otherwise.</returns>
    public async Task<bool> IsLoginButtonVisible()
    {
        return await IsVisibleAsync(LoginButtonSelector);
    }

    /// <summary>
    /// Checks if the user options button is visible.
    /// </summary>
    /// <returns>True if the user options button is visible, false otherwise.</returns>
    public async Task<bool> IsUserOptionsButtonVisible()
    {
        return await UserOptionsButton.IsVisibleAsync();
    }

    /// <summary>
    /// Clicks the user options button.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickMyAccountMenuItem()
    {
        await Task.Delay(100);
        await UserOptionsButton.ClickAsync();
        // Wait for user menu to appear
        await WaitForSelectorAsync(UserOptionsButton.MenuItemsSelector);
    }

    /// <summary>
    /// Gets the count of buttons in the user options menu.
    /// </summary>
    /// <returns>The number of buttons in the user options menu.</returns>
    public async Task<int> GetUserOptionsButtonCount()
    {
        // Get the user options button element
        var userOptionsButton = await _page.QuerySelectorAsync(UserOptionsButtonSelector);
        if (userOptionsButton == null)
        {
            return 0;
        }
        // Get all menu item buttons in the split button
        var menuButtons = await userOptionsButton.QuerySelectorAllAsync("button");

        // Return the count of menu item buttons
        return menuButtons.Count;
    }

    /// <summary>
    /// Clicks a specific sub-button in the user options menu.
    /// </summary>
    /// <param name="index">The index of the sub-button to click.</param>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickMyAccountMenuItemSubButton(int index)
    {
        // Get the user options button element
        var userOptionsButton = await _page.QuerySelectorAsync(UserOptionsButtonSelector);
        if (userOptionsButton == null)
        {
            throw new Exception("User options button not found.");
        }
        // Get all menu item buttons in the split button
        var menuButtons = await userOptionsButton.QuerySelectorAllAsync("button");

        // Return if we don't have enough buttons
        if (index < 0 || index >= menuButtons.Count)
        {
            throw new IndexOutOfRangeException(
                $"Menu item button index {index} is out of range. Only {menuButtons.Count} buttons available."
            );
        }
        // Click the specified button
        await menuButtons[index].ClickAsync();
    }

    /// <summary>
    /// Clicks the logout option.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickLogoutOption()
    {
        await ClickAsync(LogoutOptionSelector);
    }

    /// <summary>
    /// Clicks the account info option.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickAccountInfoOption()
    {
        await ClickAsync(MyAccountMenuSelector);
    }

    /// <summary>
    /// Gets all navigation links.
    /// </summary>
    /// <returns>A list of navigation link elements.</returns>
    public async Task<IReadOnlyList<IElementHandle>> GetNavigationLinks()
    {
        return await _page.QuerySelectorAllAsync(NavigationLinksSelector);
    }

    /// <summary>
    /// Gets the count of user menu options displayed in the dropdown menu.
    /// </summary>
    /// <returns>The number of options in the user menu.</returns>
    public async Task<int> GetUserMenuOptionsCount()
    {
        // Ensure the menu is open
        var menuItems = await _page.QuerySelectorAllAsync(".rz-splitbutton-menu .rz-menu-list li.rz-menuitem");

        // Return the count of menu items
        return menuItems.Count;
    }

    /// <summary>
    /// Checks if all user menu options are visible when the menu is opened.
    /// </summary>
    /// <returns>The count of visible user menu options.</returns>
    public async Task<int> GetCountUserMenuOptionsVisible()
    {
        // Get all menu items
        var menuItems = await UserOptionsButton.GetMenuItemsAsync();
        var count = 0;
        // Check if each menu item is visible
        foreach (var menuItem in menuItems)
        {
            var isVisible = await menuItem.IsVisibleAsync();
            if (isVisible)
            {
                count++;
            }
        }

        // Return the count of visible items
        return count;
    }

    /// <summary>
    /// Gets the text of all user menu options.
    /// </summary>
    /// <returns>A collection of strings containing the text of each menu option.</returns>
    public async Task<IEnumerable<string>> GetUserMenuOptionTexts()
    {
        // Get all menu items
        var menuItems = await _page.QuerySelectorAllAsync(".rz-splitbutton-menu .rz-menu-list li.rz-menuitem");

        // Extract the text content from each menu item
        var texts = new List<string>();
        foreach (var menuItem in menuItems)
        {
            var textContent = await menuItem.TextContentAsync();
            if (!string.IsNullOrWhiteSpace(textContent))
            {
                texts.Add(textContent.Trim());
            }
        }

        return texts;
    }

    /// <summary>
    /// Checks if the sidebar is visible.
    /// </summary>
    /// <returns>True if the sidebar is visible, false otherwise.</returns>
    public async Task<bool> IsSidebarVisible()
    {
        var sidebar = await _page.QuerySelectorAsync(SidebarSelector);
        if (sidebar == null)
            return false;

        // Check if the sidebar has the expanded class
        var classAttribute = await sidebar.GetAttributeAsync("class");
        return classAttribute != null && classAttribute.Contains("rz-sidebar-expanded");
    }

    /// <summary>
    /// Clicks the sidebar toggle button to show/hide the sidebar.
    /// </summary>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickSidebarToggle()
    {
        await ClickAsync(SidebarToggleSelector);
        // Wait a moment for the animation to complete
        await Task.Delay(500);
    }

    /// <summary>
    /// Gets all sidebar menu items.
    /// </summary>
    /// <returns>A list of sidebar menu item elements.</returns>
    public async Task<IReadOnlyList<IElementHandle>> GetSidebarMenuItems()
    {
        return await _page.QuerySelectorAllAsync(SidebarMenuItemsSelector);
    }

    /// <summary>
    /// Gets the text of all sidebar menu items.
    /// </summary>
    /// <returns>A collection of strings containing the text of each sidebar menu item.</returns>
    public async Task<IEnumerable<string>> GetSidebarMenuItemTexts()
    {
        var menuItems = await GetSidebarMenuItems();
        var texts = new List<string>();

        foreach (var menuItem in menuItems)
        {
            // Find the span with class "rz-navigation-item-text" which contains only the menu text (not the icon)
            var textSpan = await menuItem.QuerySelectorAsync(".rz-navigation-item-text");
            if (textSpan != null)
            {
                var textContent = await textSpan.TextContentAsync();
                if (!string.IsNullOrWhiteSpace(textContent))
                {
                    texts.Add(textContent.Trim());
                }
            }
        }

        return texts;
    }

    /// <summary>
    /// Clicks a sidebar menu item by its name.
    /// </summary>
    /// <param name="name">The name of the menu item to click (e.g., "Home", "About").</param>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickSidebarMenuItem(string name)
    {
        // Use the data-testid selector directly
        var selector = SidebarMenuItemSelector(name);
        await ClickAsync(selector);
    }

    /// <summary>
    /// Checks if the sidebar is responsive (changes based on screen size).
    /// </summary>
    /// <returns>True if the sidebar has the responsive class, false otherwise.</returns>
    public async Task<bool> IsSidebarResponsive()
    {
        var sidebar = await _page.QuerySelectorAsync(SidebarSelector);
        if (sidebar == null)
            return false;

        var classAttribute = await sidebar.GetAttributeAsync("class");
        return classAttribute != null && classAttribute.Contains("rz-sidebar-responsive");
    }

    /// <summary>
    /// Checks if the local storage contains the menu visibility state.
    /// </summary>
    /// <returns>True if the local storage contains the menu visibility state, false otherwise.</returns>
    public async Task<bool> HasMenuVisibilityInLocalStorage()
    {
        var result = await _page.EvaluateAsync<bool>("localStorage.getItem('ProScoring_MenuVisible') !== null");
        return result;
    }

    /// <summary>
    /// Gets the menu visibility state from local storage.
    /// </summary>
    /// <returns>The menu visibility state from local storage, or null if not found.</returns>
    public async Task<bool?> GetMenuVisibilityFromLocalStorage()
    {
        var result = await _page.EvaluateAsync<string>("localStorage.getItem('ProScoring_MenuVisible')");
        if (result == null)
            return null;

        return result.ToLower() == "true";
    }

    /// <summary>
    /// Clears the menu visibility state from local storage.
    /// </summary>
    /// <returns>A task representing the operation.</returns>
    public async Task ClearMenuVisibilityFromLocalStorage()
    {
        await _page.EvaluateAsync("localStorage.removeItem('ProScoring_MenuVisible')");
    }
    #endregion Methods
}
