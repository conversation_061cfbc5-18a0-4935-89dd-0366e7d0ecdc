using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.OData.Edm;
using Microsoft.OData.ModelBuilder;
using NSubstitute;
using ProScoring.Blazor.Controllers;
using ProScoring.BusinessLogic.ServiceInterfaces;
using ProScoring.Domain.Common;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.ServiceInterfaces;
using Xunit;

namespace ProScoring.Tests.WebApi;

public class OrganizingAuthorityControllerODataTests
{
    private readonly OrganizingAuthorityController _controller;
    private readonly IOrganizingAuthorityService _mockService;
    private readonly IProScoringAuthorizationService _mockAuthorizationService;
    private readonly IFileService _mockFileService;
    private readonly ILogger<OrganizingAuthorityController> _mockLogger;

    public OrganizingAuthorityControllerODataTests()
    {
        // Setup mocks
        _mockService = Substitute.For<IOrganizingAuthorityService>();
        _mockFileService = Substitute.For<IFileService>();
        _mockAuthorizationService = Substitute.For<IProScoringAuthorizationService>();
        _mockLogger = Substitute.For<ILogger<OrganizingAuthorityController>>();

        // Create controller
        _controller = new OrganizingAuthorityController(
            _mockService,
            _mockFileService,
            _mockAuthorizationService,
            _mockLogger
        );

        // Setup controller context for OData
        var httpContext = new DefaultHttpContext();
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };
    }

    [Fact]
    public async Task GetOData_ReturnsOkResult_WithAuthorities()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthority { Id = "O2", Name = "Authority 2" },
        }.AsQueryable();

        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Act
        var result = await _controller.GetOData();

        // Assert
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var returnValue = okResult.Value.Should().BeAssignableTo<IQueryable<OrganizingAuthority>>().Subject;
        returnValue.Count().Should().Be(2);
    }

    [Fact]
    public async Task GetOData_WithEnableQuery_SupportsFiltering()
    {
        // Arrange
        var authorities = new List<OrganizingAuthority>
        {
            new OrganizingAuthority { Id = "O1", Name = "Authority 1" },
            new OrganizingAuthority { Id = "O2", Name = "Authority 2" },
            new OrganizingAuthority { Id = "O3", Name = "Different Authority" },
        }.AsQueryable();

        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Create a test OData context with filter
        var queryOptions = new EnableQueryAttribute();

        // This test verifies that the controller returns data in a format that can be filtered
        // In a real OData request, the filtering would be done by the OData middleware

        // Act
        var result = await _controller.GetOData();

        // Assert
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        var returnValue = okResult.Value.Should().BeAssignableTo<IQueryable<OrganizingAuthority>>().Subject;

        // Manually apply a filter that OData would apply
        var filtered = returnValue.Where(a => a.Name.Contains("Different")).ToList();
        filtered.Count.Should().Be(1);
        filtered[0].Id.Should().Be("O3");
    }

    [Fact]
    public async Task Patch_ReturnsBadRequest_WhenIdDoesNotMatch()
    {
        // Arrange
        var authority = new OrganizingAuthority { Id = "O1", Name = "Updated Authority" };

        // Act
        var result = await _controller.Patch("O2", authority);

        // Assert
        result.Should().BeOfType<BadRequestResult>();
    }

    [Fact]
    public async Task Patch_ReturnsNotFound_WhenAuthorityDoesNotExist()
    {
        // Arrange
        var authority = new OrganizingAuthority { Id = "O1", Name = "Updated Authority" };
        _mockService.GetByIdAsync("O1").Returns((OrganizingAuthority?)null);

        // Act
        var result = await _controller.Patch("O1", authority);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task Patch_ReturnsNoContent_WhenUpdateIsSuccessful()
    {
        // Arrange
        var authority = new OrganizingAuthority { Id = "O1", Name = "Updated Authority" };
        _mockService.GetByIdAsync("O1").Returns(authority);
        _mockService.UpdateAsync(authority).Returns(authority);

        // Act
        var result = await _controller.Patch("O1", authority);

        // Assert
        result.Should().BeOfType<NoContentResult>();
        await _mockService.Received(1).UpdateAsync(authority);
    }

    [Fact]
    public void ODataModel_ShouldHaveCorrectMetadata()
    {
        // Arrange
        var modelBuilder = new ODataConventionModelBuilder();
        modelBuilder.Namespace = "ProScoring";
        modelBuilder.ContainerName = "ProScoringContainer";

        // Configure OrganizingAuthority entity
        var organizingAuthoritySet = modelBuilder.EntitySet<OrganizingAuthority>("OrganizingAuthorities");
        var organizingAuthorityType = organizingAuthoritySet.EntityType;
        organizingAuthorityType.HasKey(e => e.Id);

        // Add property descriptions for OrganizingAuthority
        organizingAuthorityType.Property(p => p.Name).IsRequired();

        // Add navigation properties for OrganizingAuthority
        organizingAuthorityType.HasOptional(p => p.Image);
        organizingAuthorityType.HasOptional(p => p.CreatedBy);
        organizingAuthorityType.HasOptional(p => p.UpdatedBy);

        // Configure OrganizingAuthorityInfoDto entity
        var organizingAuthorityInfoSet = modelBuilder.EntitySet<OrganizingAuthorityInfoDto>("OrganizingAuthorityInfos");
        var organizingAuthorityInfoType = organizingAuthorityInfoSet.EntityType;
        organizingAuthorityInfoType.HasKey(e => e.Id);

        // Act
        var model = modelBuilder.GetEdmModel();

        // Assert
        model.Should().NotBeNull();
        model.EntityContainer.Should().NotBeNull();
        model.EntityContainer.Name.Should().Be("ProScoringContainer");

        // Verify OrganizingAuthorities entity set
        var organizingAuthoritiesSet = model.EntityContainer.FindEntitySet("OrganizingAuthorities");
        organizingAuthoritiesSet.Should().NotBeNull();

        // Verify OrganizingAuthorityInfos entity set
        var organizingAuthorityInfosSet = model.EntityContainer.FindEntitySet("OrganizingAuthorityInfos");
        organizingAuthorityInfosSet.Should().NotBeNull();

        // Verify navigation properties
        var organizingAuthorityEntityType = model
            .SchemaElements.OfType<IEdmEntityType>()
            .FirstOrDefault(e => e.Name == "OrganizingAuthority");

        organizingAuthorityEntityType.Should().NotBeNull();

        var imageProperty = organizingAuthorityEntityType!
            .DeclaredNavigationProperties()
            .FirstOrDefault(p => p.Name == "Image");
        imageProperty.Should().NotBeNull();

        var createdByProperty = organizingAuthorityEntityType
            .DeclaredNavigationProperties()
            .FirstOrDefault(p => p.Name == "CreatedBy");
        createdByProperty.Should().NotBeNull();

        var updatedByProperty = organizingAuthorityEntityType
            .DeclaredNavigationProperties()
            .FirstOrDefault(p => p.Name == "UpdatedBy");
        updatedByProperty.Should().NotBeNull();
    }

    [Fact]
    public async Task GetOData_ShouldSupportExpand()
    {
        // Arrange
        var fileRecord = new FileRecord
        {
            Id = "F1",
            ContentType = "image/png",
            Path = "/images/test.png",
            Size = 1024,
            TrustedFileNameForDisplay = "test.png",
            UntrustedName = "test.png",
            UploadDate = DateTimeOffset.UtcNow,
        };

        var user = new ApplicationUser
        {
            Id = "U1",
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
        };

        var authority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Authority with Image",
            ImageId = "F1",
            Image = fileRecord,
            CreatedById = "U1",
            CreatedBy = user,
        };

        var authorities = new List<OrganizingAuthority> { authority }.AsQueryable();
        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Act
        var result = await _controller.GetOData();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var queryable = okResult.Value as IQueryable<OrganizingAuthority>;
        queryable.Should().NotBeNull();

        // Verify that the Image navigation property is included
        var authorityWithImage = queryable!.First();
        authorityWithImage.Image.Should().NotBeNull();
        authorityWithImage.Image!.Id.Should().Be("F1");

        // Verify that the CreatedBy navigation property is included
        authorityWithImage.CreatedBy.Should().NotBeNull();
        authorityWithImage.CreatedBy!.Id.Should().Be("U1");
    }

    [Fact]
    public async Task GetOData_ShouldApplyAuthorizationFiltering_ForUnauthenticatedUsers()
    {
        // Arrange
        var publicAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Public Authority",
            Private = false,
        };

        // Set up an unauthenticated user
        var httpContext = new DefaultHttpContext();
        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Mock the service to return only public authorities for unauthenticated users
        var authorities = new List<OrganizingAuthority> { publicAuthority }.AsQueryable();
        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Act
        var result = await _controller.GetOData();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var queryable = okResult.Value as IQueryable<OrganizingAuthority>;
        queryable.Should().NotBeNull();

        // Verify that only public authorities are returned
        var resultList = queryable!.ToList();
        resultList.Should().HaveCount(1);
        resultList[0].Id.Should().Be("O1");
        resultList[0].Private.Should().BeFalse();
    }

    [Fact]
    public async Task GetOData_ShouldApplyAuthorizationFiltering_ForAuthenticatedUsers()
    {
        // Arrange
        var publicAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Public Authority",
            Private = false,
        };

        var privateAuthority = new OrganizingAuthority
        {
            Id = "O2",
            Name = "Private Authority",
            Private = true,
        };

        // Set up an authenticated user
        var httpContext = new DefaultHttpContext();
        var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, "user1") };
        var identity = new ClaimsIdentity(claims, "TestAuth");
        var principal = new ClaimsPrincipal(identity);
        httpContext.User = principal;

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Mock the service to return both authorities for authenticated users with permissions
        var authorities = new List<OrganizingAuthority> { publicAuthority, privateAuthority }.AsQueryable();
        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Act
        var result = await _controller.GetOData();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var queryable = okResult.Value as IQueryable<OrganizingAuthority>;
        queryable.Should().NotBeNull();

        // Verify that both authorities are returned
        var resultList = queryable!.ToList();
        resultList.Should().HaveCount(2);
        resultList.Should().Contain(a => a.Id == "O1" && !a.Private);
        resultList.Should().Contain(a => a.Id == "O2" && a.Private);
    }

    [Fact]
    public async Task GetOData_ShouldReturnAllAuthorities_ForHMFICUsers()
    {
        // Arrange
        var publicAuthority = new OrganizingAuthority
        {
            Id = "O1",
            Name = "Public Authority",
            Private = false,
        };

        var privateAuthority = new OrganizingAuthority
        {
            Id = "O2",
            Name = "Private Authority",
            Private = true,
        };

        // Set up an HMFIC user
        var httpContext = new DefaultHttpContext();
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, "admin1"),
            new Claim(AuthTypes.HMFIC, "true"),
        };
        var identity = new ClaimsIdentity(claims, "TestAuth");
        var principal = new ClaimsPrincipal(identity);
        httpContext.User = principal;

        _controller.ControllerContext = new ControllerContext { HttpContext = httpContext };

        // Mock the service to return all authorities for HMFIC users
        var authorities = new List<OrganizingAuthority> { publicAuthority, privateAuthority }.AsQueryable();
        _mockService.GetFilteredQueryableForODataAsync().Returns(authorities);

        // Act
        var result = await _controller.GetOData();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        var queryable = okResult.Value as IQueryable<OrganizingAuthority>;
        queryable.Should().NotBeNull();

        // Verify that both authorities are returned
        var resultList = queryable!.ToList();
        resultList.Should().HaveCount(2);
        resultList.Should().Contain(a => a.Id == "O1" && !a.Private);
        resultList.Should().Contain(a => a.Id == "O2" && a.Private);
    }
}
