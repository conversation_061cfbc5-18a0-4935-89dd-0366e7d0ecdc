using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Domain.Enums;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// A boat that is participating in a regatta.
///
/// This is the competitor unit. This is what is scored.
///
/// If there are boat rotations, then RegattaBoat will be assigned to a
/// RegattaEquipment entity for each race.  In this case, the data input
/// when scoring will tie to the RegattaEquipment, and then be dereferenced
/// back to the RegattaBoat.
///
/// It has a Fleet when registering, and one or more Classes when racing.
/// Classes all start and are scored together
///
/// Regatta Network divides Classes into Divisions. Do we want to do this?
/// I think that having more than one Class per boat may be sufficient.
///
/// It can also have one or more SuperClasses, which are scored together and
/// combine boats from multiple classes. All SuperClasses should sail
/// the same course, but may have different starts.
///
/// A RegattaBoat may also have an associated Boat, from which it is derived.
/// Updates to the RegattaBoat update the Boat, but not vice versa unless
/// there is only a 1 to 1 relationship.
///
/// A RegattaBoat also has a skipper, and may have 0 or more crew members.
/// The Skipper and Crew members are of the type RegattaCompetitor.
/// </summary>
public class RegattaBoat : RegattaEntityBase<RegattaBoat>, IHasForeignKeyConfiguration<RegattaBoat>
{
    #region Constants

    /// <summary>
    /// The prefix used for RegattaBoat IDs.
    /// </summary>
    public const string ID_PREFIX = "RB";

    #endregion Constants

    #region Properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaBoat.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the country code ID for this boat.
    /// </summary>
    [Column(Order = 15)]
    [ForeignKey(nameof(CountryCode))]
    public string? CountryCodeId { get; set; }

    /// <summary>
    /// Gets or sets the sail number of the boat.
    /// </summary>
    [Column(Order = 20)]
    [StringLength(10)]
    public string? SailNumber { get; set; }

    /// <summary>
    /// Gets or sets the name of the boat.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(50)]
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the boat type.
    /// </summary>
    /// <remarks>
    /// TODO: Do we want this to be a foreign key to a BoatType table?
    /// </remarks>
    [Column(Order = 40)]
    [StringLength(50)]
    public string? BoatType { get; set; }

    /// <summary>
    /// Gets or sets the length of the boat.
    /// </summary>
    [Column(Order = 50)]
    public float? Length { get; set; }

    /// <summary>
    /// Gets or sets the unit of measurement for the boat length.
    /// </summary>
    [Column(Order = 60)]
    public LengthUnit? LengthUnit { get; set; }

    /// <summary>
    /// Gets or sets the ID of the master boat record.
    /// </summary>
    [Column(Order = 70)]
    [ForeignKey(nameof(MasterBoat))]
    public string? MasterBoatId { get; set; }

    /// <summary>
    /// Gets or sets the regatta fleet ID that this boat belongs to.
    /// </summary>
    [Column(Order = 80)]
    [ForeignKey(nameof(RegattaFleet))]
    public string? RegattaFleetId { get; set; }

    /// <summary>
    /// Gets or sets the regatta class ID that this boat belongs to.
    /// </summary>
    [Column(Order = 90)]
    [ForeignKey(nameof(RegattaClass))]
    public string? RegattaClassId { get; set; }

    /// <summary>
    /// Gets or sets the ID of the primary skipper.
    /// </summary>
    [Column(Order = 100)]
    [ForeignKey(nameof(Skipper))]
    public string? SkipperId { get; set; }

    /// <summary>
    /// Gets or sets the ID of the secondary skipper.
    /// </summary>
    [Column(Order = 110)]
    [ForeignKey(nameof(Skipper2))]
    public string? Skipper2Id { get; set; }

    #endregion Properties

    #region Navigation Properties

    /// <summary>
    /// Gets or sets the country code for this boat.
    /// </summary>
    public virtual CountryCode? CountryCode { get; set; }

    /// <summary>
    /// Gets or sets the master boat record.
    /// </summary>
    public virtual Boat? MasterBoat { get; set; }

    /// <summary>
    /// Gets or sets the regatta fleet that this boat belongs to.
    /// </summary>
    public virtual RegattaFleet? RegattaFleet { get; set; }

    /// <summary>
    /// Gets or sets the regatta class that this boat belongs to.
    /// </summary>
    public virtual RegattaClass? RegattaClass { get; set; }

    /// <summary>
    /// Gets or sets the primary skipper.
    /// </summary>
    public virtual RegattaCompetitor? Skipper { get; set; }

    /// <summary>
    /// Gets or sets the secondary skipper.
    /// </summary>
    public virtual RegattaCompetitor? Skipper2 { get; set; }

    /// <summary>
    /// Gets or sets the collection of competitors associated with this boat.
    /// </summary>
    public virtual ICollection<RegattaBoatCompetitor> RegattaBoatCompetitors { get; set; } =
        new List<RegattaBoatCompetitor>();

    /// <summary>
    /// Gets or sets the collection of ratings associated with this boat.
    /// </summary>
    public virtual ICollection<RegattaRating> RegattaRatings { get; set; } = new List<RegattaRating>();

    #endregion Navigation Properties

    #region DB Configuration

    #region IHasForeignKeyConfiguration Implementation
    /// <summary>
    /// Configures foreign key relationships for the RegattaBoat entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the RegattaBoat entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaBoat> entity)
    {
        entity.HasOne(rb => rb.MasterBoat).WithMany().HasForeignKey(rb => rb.MasterBoatId);
        entity.HasOne(rb => rb.CountryCode).WithMany().HasForeignKey(rb => rb.CountryCodeId);
        entity.HasOne(rb => rb.RegattaFleet).WithMany().HasForeignKey(rb => rb.RegattaFleetId);
        entity.HasOne(rb => rb.RegattaClass).WithMany().HasForeignKey(rb => rb.RegattaClassId);
        entity.HasOne(rb => rb.Skipper).WithMany().HasForeignKey(rb => rb.SkipperId);
        entity.HasOne(rb => rb.Skipper2).WithMany().HasForeignKey(rb => rb.Skipper2Id);
    }

    #endregion IHasForeignKeyConfiguration Implementation

    #endregion DB Configuration
}
