using Xunit;
using FluentAssertions;
using NSubstitute;
using ProScoring.Blazor.Services;
using System; // For Action
using System.Threading.Tasks; // For Task

namespace ProScoring.Tests.Blazor.Services
{
    public class MainLayoutContextServiceTests
    {
        [Fact]
        public void InitialState_LoginButtonVisible_IsTrue()
        {
            // Arrange
            var service = new MainLayoutContextService();

            // Assert
            service.LoginButtonVisible.Should().BeTrue();
        }

        [Fact]
        public void UpdateLayout_SetsLoginButtonVisible_And_InvokesOnLayoutChanged()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var eventRaised = false;
            service.OnLayoutChanged += () => eventRaised = true;

            // Act
            service.UpdateLayout(loginButtonVisible: false);

            // Assert
            service.LoginButtonVisible.Should().BeFalse();
            eventRaised.Should().BeTrue();
        }

        [Fact]
        public void UpdateLayout_NullValue_DoesNotChangeLoginButtonVisible_But_InvokesOnLayoutChanged()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var initialLoginButtonVisible = service.LoginButtonVisible; // Should be true
            var eventRaised = false;
            service.OnLayoutChanged += () => eventRaised = true;

            // Act
            service.UpdateLayout(loginButtonVisible: null);

            // Assert
            service.LoginButtonVisible.Should().Be(initialLoginButtonVisible);
            eventRaised.Should().BeTrue();
        }

        [Fact]
        public void UpdateLayout_NoParameters_DoesNotChangeLoginButtonVisible_But_InvokesOnLayoutChanged()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var initialLoginButtonVisible = service.LoginButtonVisible; // Should be true
            var eventRaised = false;
            service.OnLayoutChanged += () => eventRaised = true;

            // Act
            service.UpdateLayout();

            // Assert
            service.LoginButtonVisible.Should().Be(initialLoginButtonVisible);
            eventRaised.Should().BeTrue();
        }

        [Fact]
        public async Task OpenLoginDialogAsync_NoMethodSet_DoesNothing()
        {
            // Arrange
            var service = new MainLayoutContextService();

            // Act
            // Assert - should not throw an exception
            await service.OpenLoginDialogAsync();
        }

        [Fact]
        public async Task OpenLoginDialogAsync_MethodSet_InvokesRegisteredMethod_WithNullAction()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var mockDialogMethod = Substitute.For<Func<Action?, Task>>();
            service.SetOpenLoginDialogMethod(mockDialogMethod);

            // Act
            await service.OpenLoginDialogAsync();

            // Assert
            await mockDialogMethod.Received(1).Invoke(null);
        }

        [Fact]
        public async Task OpenLoginDialogAsync_MethodSet_InvokesRegisteredMethod_WithProvidedAction()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var mockDialogMethod = Substitute.For<Func<Action?, Task>>();
            service.SetOpenLoginDialogMethod(mockDialogMethod);
            Action sampleAction = () => { };

            // Act
            await service.OpenLoginDialogAsync(sampleAction);

            // Assert
            await mockDialogMethod.Received(1).Invoke(sampleAction);
        }

        [Fact]
        public void OnLayoutChanged_Event_SubscribersAreNotified()
        {
            // Arrange
            var service = new MainLayoutContextService();
            var handler1Called = false;
            var handler2Called = false;

            service.OnLayoutChanged += () => handler1Called = true;
            service.OnLayoutChanged += () => handler2Called = true;

            // Act
            service.UpdateLayout(); // This should trigger the OnLayoutChanged event

            // Assert
            handler1Called.Should().BeTrue();
            handler2Called.Should().BeTrue();
        }
    }
}
