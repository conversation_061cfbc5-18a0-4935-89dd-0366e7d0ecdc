{"workbench.colorCustomizations": {"activityBar.activeBackground": "#725ede", "activityBar.background": "#725ede", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#e9a295", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#e7e7e799", "editorGroup.border": "#725ede", "panel.border": "#725ede", "sash.hoverBorder": "#725ede", "sideBar.border": "#725ede", "statusBar.background": "#4d34d5", "statusBar.debuggingBackground": "#bcd534", "statusBar.debuggingForeground": "#15202b", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#725ede", "statusBarItem.remoteBackground": "#4d34d5", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#4d34d5", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#4d34d599", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#4d34d5", "cSpell.words": ["AAES", "accessibilities", "Anacortes", "appsettings", "ASPNETCORE", "Blazor", "Blazored", "Bunit", "<PERSON><PERSON>", "goodbyeworld", "Guidish", "HMFIC", "HSTS", "nameof", "Neovolve", "nosnippet", "nowrap", "Npgsql", "onblur", "onclick", "organizingauthorities", "parameterless", "PHRF", "PHRFNW", "proscoring", "<PERSON><PERSON><PERSON>", "rendermode", "rgba", "<PERSON>", "Seaview", "Serilog", "<PERSON><PERSON>", "stylesheet", "S<PERSON><PERSON>", "Westhaven", "<PERSON><PERSON><PERSON>"], "files.associations": {"*.yaml": "yaml", "appsettings*.json": "jsonc"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"*.razor": "${basename}.razor.css"}}