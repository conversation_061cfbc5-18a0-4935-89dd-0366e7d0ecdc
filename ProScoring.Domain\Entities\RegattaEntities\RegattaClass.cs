using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.RegattaEntities;

/// <summary>
/// This is the group of boats that race together and are scored together.
/// </summary>
public class RegattaClass : RegattaEntityBase<RegattaClass>, IHasForeignKeyConfiguration<RegattaClass>
{
    #region constants

    /// <summary>
    /// The prefix used for RegattaClass IDs.
    /// </summary>
    public const string ID_PREFIX = "RC";

    #endregion

    #region properties

    /// <summary>
    /// Gets the prefix used for generating IDs for RegattaClass.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the name of the class.
    /// </summary>
    [Column(Order = 20)]
    [StringLength(50, MinimumLength = 3)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description of the class.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the ID of the regatta fleet this class belongs to.
    /// </summary>
    [ForeignKey(nameof(RegattaFleet))]
    [Column(Order = 40)]
    public required string RegattaFleetId { get; set; }

    /// <summary>
    /// Gets or sets the regatta fleet this class belongs to.
    /// </summary>
    public virtual RegattaFleet? RegattaFleet { get; set; }

    #endregion

    #region DB Configuration

    /// <summary>
    /// Configures foreign key relationships for the RegattaClass entity.
    /// </summary>
    /// <param name="entity">The entity type builder for the RegattaClass entity.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RegattaClass> entity)
    {
        entity
            .HasOne(x => x.RegattaFleet)
            .WithMany()
            .HasForeignKey(x => x.RegattaFleetId)
            .OnDelete(DeleteBehavior.Restrict);
    }

    #endregion
}
