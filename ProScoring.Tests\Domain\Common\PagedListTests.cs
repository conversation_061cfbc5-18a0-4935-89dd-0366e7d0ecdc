using System.Collections.Generic;
using FluentAssertions;
using ProScoring.Domain.Common;
using Xunit;

namespace ProScoring.Tests.Domain.Common;

/// <summary>
/// Contains unit tests for the PagedList class functionality.
/// </summary>
public class PagedListTests
{
    #region Constructor Tests

    [Fact]
    public void Constructor_SetsProperties_Correctly()
    {
        // Arrange
        var items = new List<string> { "Item1", "Item2", "Item3" };
        var totalCount = 10;
        var pageNumber = 2;
        var pageSize = 3;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.Items.Should().BeEquivalentTo(items);
        pagedList.TotalCount.Should().Be(totalCount);
        pagedList.PageNumber.Should().Be(pageNumber);
        pagedList.PageSize.Should().Be(pageSize);
    }

    #endregion // Constructor Tests

    #region TotalPages Tests

    [Fact]
    public void TotalPages_WithExactDivision_ReturnsCorrectValue()
    {
        // Arrange
        var items = new List<string> { "Item1", "Item2" };
        var totalCount = 10;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, 1, pageSize);

        // Assert
        pagedList.TotalPages.Should().Be(5); // 10 items / 2 per page = 5 pages
    }

    [Fact]
    public void TotalPages_WithRemainder_RoundsUp()
    {
        // Arrange
        var items = new List<string> { "Item1", "Item2" };
        var totalCount = 11;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, 1, pageSize);

        // Assert
        pagedList.TotalPages.Should().Be(6); // 11 items / 2 per page = 5.5, rounds up to 6
    }

    [Fact]
    public void TotalPages_WithZeroItems_ReturnsZero()
    {
        // Arrange
        var items = new List<string>();
        var totalCount = 0;
        var pageSize = 5;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, 1, pageSize);

        // Assert
        pagedList.TotalPages.Should().Be(0);
    }

    #endregion // TotalPages Tests

    #region HasPreviousPage Tests

    [Fact]
    public void HasPreviousPage_OnFirstPage_ReturnsFalse()
    {
        // Arrange
        var items = new List<string> { "Item1", "Item2" };
        var pageNumber = 1;

        // Act
        var pagedList = new PagedList<string>(items, 10, pageNumber, 2);

        // Assert
        pagedList.HasPreviousPage.Should().BeFalse();
    }

    [Fact]
    public void HasPreviousPage_OnSecondPage_ReturnsTrue()
    {
        // Arrange
        var items = new List<string> { "Item3", "Item4" };
        var pageNumber = 2;

        // Act
        var pagedList = new PagedList<string>(items, 10, pageNumber, 2);

        // Assert
        pagedList.HasPreviousPage.Should().BeTrue();
    }

    [Fact]
    public void HasPreviousPage_OnLastPage_ReturnsTrue()
    {
        // Arrange
        var items = new List<string> { "Item9", "Item10" };
        var pageNumber = 5;
        var totalCount = 10;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasPreviousPage.Should().BeTrue();
    }

    #endregion // HasPreviousPage Tests

    #region HasNextPage Tests

    [Fact]
    public void HasNextPage_OnFirstPage_ReturnsTrue()
    {
        // Arrange
        var items = new List<string> { "Item1", "Item2" };
        var pageNumber = 1;
        var totalCount = 10;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasNextPage.Should().BeTrue();
    }

    [Fact]
    public void HasNextPage_OnMiddlePage_ReturnsTrue()
    {
        // Arrange
        var items = new List<string> { "Item5", "Item6" };
        var pageNumber = 3;
        var totalCount = 10;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasNextPage.Should().BeTrue();
    }

    [Fact]
    public void HasNextPage_OnLastPage_ReturnsFalse()
    {
        // Arrange
        var items = new List<string> { "Item9", "Item10" };
        var pageNumber = 5;
        var totalCount = 10;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasNextPage.Should().BeFalse();
    }

    [Fact]
    public void HasNextPage_OnLastPageWithRemainder_ReturnsFalse()
    {
        // Arrange
        var items = new List<string> { "Item11" };
        var pageNumber = 6;
        var totalCount = 11;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasNextPage.Should().BeFalse();
    }

    #endregion // HasNextPage Tests

    #region Edge Cases

    [Fact]
    public void PagedList_WithZeroPageSize_CalculatesTotalPagesCorrectly()
    {
        // Arrange
        var items = new List<string>();
        var totalCount = 10;
        var pageNumber = 1;
        var pageSize = 0;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        // Division by zero should result in "infinity", but we're using Math.Ceiling which would return infinity
        // In this case, we expect the implementation to handle this by returning a very large number or infinity
        // For practical purposes, we'll just check it's greater than the total count
        pagedList.TotalPages.Should().BeGreaterThan(totalCount);
    }

    [Fact]
    public void PagedList_WithNegativePageNumber_HasPreviousPageIsFalse()
    {
        // Arrange
        var items = new List<string>();
        var totalCount = 10;
        var pageNumber = -1;
        var pageSize = 2;

        // Act
        var pagedList = new PagedList<string>(items, totalCount, pageNumber, pageSize);

        // Assert
        pagedList.HasPreviousPage.Should().BeFalse();
    }

    #endregion // Edge Cases
}
