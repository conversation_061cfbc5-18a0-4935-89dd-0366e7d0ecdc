﻿using ProScoring.Infrastructure.ServiceInterfaces;

namespace ProScoring.Infrastructure.Services;

/// <summary>
/// Provides the current date and time using the CPU's clock.
/// </summary>
public class DateTimeOffsetProviderCpuTime : IDateTimeOffsetProvider
{
    /// <summary>
    /// Gets the current UTC date and time from the system clock.
    /// </summary>
    public DateTimeOffset UtcNow => DateTimeOffset.UtcNow;
}
