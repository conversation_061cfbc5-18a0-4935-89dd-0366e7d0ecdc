using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a type of value that can be stored in a rating.
/// </summary>
public class RatingValueType : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<RatingValueType>
{
    public const string ID_PREFIX = "HK";
    public override string IdPrefix => ID_PREFIX;

    #region Properties
    /// <summary>
    /// Gets or sets the unique identifier of the value type.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; } = null!;

    /// <summary>
    /// Gets or sets the name of the value type.
    /// </summary>
    [Required]
    [Column(Order = 20)]
    [StringLength(50)]
    public required string Name { get; set; }

    /// <summary>
    /// Gets or sets the rating type this value type belongs to.
    /// </summary>
    [Required]
    [Column(Order = 30)]
    [ForeignKey(nameof(RatingType))]
    public required string RatingTypeId { get; set; }
    #endregion

    #region Navigation Properties
    /// <summary>
    /// Gets or sets the rating type this value type belongs to.
    /// </summary>
    public virtual RatingType RatingType { get; set; } = null!;

    #endregion

    #region Methods
    /// <summary>
    /// Configures foreign key relationships for the ValueType entity.
    /// </summary>
    /// <param name="entity">The entity type builder for ValueType.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<RatingValueType> entity)
    {
        entity
            .HasOne(vt => vt.RatingType)
            .WithMany()
            .HasForeignKey(vt => vt.RatingTypeId)
            .OnDelete(DeleteBehavior.Restrict);
    }
    #endregion
}
