<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.Testing" Version="9.0.0" />
    <PackageReference Include="AutoFixture.AutoNSubstitute" Version="4.18.1" />
    <PackageReference Include="bunit" Version="1.38.5" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="7.1.0" />
    <PackageReference Include="FluentAssertions.BUnit" Version="0.0.67" />
    <PackageReference Include="FluentAssertions.Web" Version="1.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.3" />
    <PackageReference Include="Neovolve.Logging.Xunit" Version="6.3.0" />
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Polly.Extensions" Version="8.5.2" />
    <PackageReference Include="RichardSzalay.MockHttp" Version="7.0.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProScoring.AppHost\ProScoring.AppHost.csproj" />
    <ProjectReference Include="..\ProScoring.Domain\ProScoring.Domain.csproj" />
    <ProjectReference Include="..\ProScoring.Infrastructure\ProScoring.Infrastructure.csproj" />
    <ProjectReference Include="..\ProScoring.Blazor\ProScoring.Blazor\ProScoring.Blazor.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="System.Net" />
    <Using Include="Microsoft.Extensions.DependencyInjection" />
    <Using Include="Aspire.Hosting.ApplicationModel" />
    <Using Include="Aspire.Hosting.Testing" />
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="IntegrationTests\Blazor\" />
  </ItemGroup>



</Project>
