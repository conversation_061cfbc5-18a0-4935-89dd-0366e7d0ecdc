using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler for delete operations on entities.
/// </summary>
public class DeleteAuthorizationForResourceHandler(IAuthorizationProvider authorizationProvider)
    : AuthorizationHandlerForResourceBase<DeleteAuthorizationForResourceHandler.Requirement>(
        authorizationProvider,
        AuthTypes.Actions.DELETE
    )
{
    public const string PolicyName = "DeleteResourcePolicy";

    /// <summary>
    /// The authorization requirement for delete operations.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
