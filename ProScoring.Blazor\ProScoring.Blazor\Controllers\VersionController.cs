using System.Reflection;
using Microsoft.AspNetCore.Mvc;

namespace ProScoring.Blazor.Controllers;

/// <summary>
/// Controller for retrieving application version information.
/// </summary>
[ApiController]
[Route("api/version")]
public class VersionController(ILogger<VersionController> logger) : ControllerBase
{
    private readonly ILogger<VersionController> _logger = logger;

    /// <summary>
    /// Returns the application version from the assembly's informational version attribute.
    /// </summary>
    /// <returns>Version information in JSON format.</returns>
    [HttpGet]
    public IActionResult GetVersion()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var informationalVersion = assembly
                .GetCustomAttribute<AssemblyInformationalVersionAttribute>()
                ?.InformationalVersion;

            var versionInfo = new { Version = informationalVersion ?? "Unknown" };
            return Ok(versionInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting version information");
            return StatusCode(500, "Error getting version information");
        }
    }
}
