/* Styles for demo pages */

/* Force scrollbar to always be visible */
html, body {
    overflow-y: auto !important;
    height: auto !important;
    min-height: 100% !important;
    max-height: none !important;
    position: relative !important;
}

/* Main content area */
main, .rz-body {
    overflow-y: auto !important;
    height: auto !important;
    min-height: 100% !important;
    max-height: none !important;
}

/* Scrollable demo page */
.scrollable-demo-page {
    height: auto !important;
    min-height: 100vh !important;
    max-height: none !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    position: relative !important;
    padding-right: 10px !important; /* Add some padding to prevent content from touching the scrollbar */
}

/* Fix for Radzen components */
.rz-layout, .rz-layout-content {
    height: auto !important;
    min-height: 100% !important;
    max-height: none !important;
    overflow-y: auto !important;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 12px; /* Wider scrollbar for better visibility */
    height: 12px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* For Firefox */
html {
    scrollbar-width: thin;
    scrollbar-color: #888 #f1f1f1;
}

/* Add some bottom padding to ensure all content is visible */
.scrollable-demo-page {
    padding-bottom: 100px !important;
}

/* Force scrollbars to be visible */
.force-scrollbar {
    overflow-y: scroll !important;
}
