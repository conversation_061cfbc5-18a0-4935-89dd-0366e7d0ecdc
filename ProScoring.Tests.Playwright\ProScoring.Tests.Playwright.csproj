<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <UserSecretsId>ProScoring.Blazor-49b61f80-2eed-4d14-bf6b-d9496502f802</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="appsettings.Test.json" />
    <None Remove="playwright.config.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.Test.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="playwright.config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="9.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="Microsoft.Playwright.Xunit" Version="1.50.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="7.1.0" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProScoring.Blazor\ProScoring.Blazor\ProScoring.Blazor.csproj" />
    <ProjectReference Include="..\ProScoring.Infrastructure\ProScoring.Infrastructure.csproj" />
    <ProjectReference Include="..\ProScoring.Domain\ProScoring.Domain.csproj" />
  </ItemGroup>

  <!-- Conditionally run Playwright installation -->
  <Target Name="InstallPlaywright" AfterTargets="Restore" Condition="'$(SkipPlaywrightInstall)' != 'true'">
    <Exec Command="pwsh -Command &quot;Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass; playwright install chromium&quot;" Condition="$([MSBuild]::IsOSPlatform('Windows'))" />
    <Exec Command="playwright install chromium" Condition="!$([MSBuild]::IsOSPlatform('Windows'))" />
  </Target>

</Project>
