namespace ProScoring.Domain.Entities.EntityInterfaces;

/// <summary>
/// Interface for entities that provide initial seed data.
/// </summary>
/// <typeparam name="T">The type of entity to be seeded.</typeparam>
public interface IHasInitialSeedData<T>
    where T : class
{
    /// <summary>
    /// Gets the array of seed data for the entity.
    /// </summary>
    public static abstract T[] SeedData { get; }
}
