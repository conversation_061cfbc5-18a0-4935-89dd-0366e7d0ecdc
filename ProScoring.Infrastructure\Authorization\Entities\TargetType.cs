using System.ComponentModel.DataAnnotations;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Domain.Entities.RegattaEntities;

namespace ProScoring.Infrastructure.Authorization.Entities;

/// <summary>
/// This class is used to map authorization target IDs to their respective types.
/// see <see cref="ProScoringAuthorizationService.GetTargetTypeAsync(string)"/>
/// </summary>
public class TargetType : IHasInitialSeedData<TargetType>
{
    public static TargetType[] SeedData =>
        [
            new TargetType { IdPrefix = AuthTypes.UNIVERSAL_TARGET, Name = AuthTypes.UNIVERSAL_TARGET },
            new TargetType { IdPrefix = OrganizingAuthority.ID_PREFIX, Name = nameof(OrganizingAuthority) },
            new TargetType { IdPrefix = Regatta.ID_PREFIX, Name = nameof(Regatta) },
        ];

    [Key]
    [StringLength(5)]
    public string IdPrefix { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the name of the target type.
    /// </summary>
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
}
