using System.Linq;
using FluentAssertions;
using Microsoft.OData.Edm;
using Microsoft.OData.ModelBuilder;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.WebApi;

/// <summary>
/// Tests for the OData model configuration.
/// </summary>
public class OrganizingAuthorityODataModelTests
{
    [Fact]
    public void OrganizingAuthorityInfoDto_ODataModel_ShouldIncludeApprovedProperty()
    {
        // Arrange
        var modelBuilder = new ODataConventionModelBuilder();
        modelBuilder.Namespace = "ProScoring";
        modelBuilder.ContainerName = "ProScoringContainer";

        // Configure OrganizingAuthorityInfoDto entity
        var organizingAuthorityInfoSet = modelBuilder.EntitySet<OrganizingAuthorityInfoDto>("OrganizingAuthorityInfos");
        var organizingAuthorityInfoType = organizingAuthorityInfoSet.EntityType;
        organizingAuthorityInfoType.HasKey(e => e.Id);

        // Add property descriptions for OrganizingAuthorityInfoDto
        organizingAuthorityInfoType.Property(p => p.Name).IsRequired();
        organizingAuthorityInfoType.Property(p => p.City);
        organizingAuthorityInfoType.Property(p => p.State);
        organizingAuthorityInfoType.Property(p => p.Country);
        organizingAuthorityInfoType.Property(p => p.Email);
        organizingAuthorityInfoType.Property(p => p.Phone);
        organizingAuthorityInfoType.Property(p => p.Website);
        organizingAuthorityInfoType.Property(p => p.Private);
        organizingAuthorityInfoType.Property(p => p.Approved);

        // Act
        var model = modelBuilder.GetEdmModel();

        // Assert
        model.Should().NotBeNull();

        // Get the OrganizingAuthorityInfoDto entity type
        var entityType = model
            .SchemaElements.OfType<IEdmEntityType>()
            .FirstOrDefault(e => e.Name == "OrganizingAuthorityInfoDto");

        entityType.Should().NotBeNull();

        // Verify the Approved property exists
        var approvedProperty = entityType!.DeclaredProperties.FirstOrDefault(p => p.Name == "Approved");

        approvedProperty.Should().NotBeNull();
        approvedProperty!.Type.FullName().Should().Be("Edm.Boolean");
    }
}
