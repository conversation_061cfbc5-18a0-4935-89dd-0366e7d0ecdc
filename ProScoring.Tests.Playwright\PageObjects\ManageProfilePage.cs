using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Page object model for the user profile management page.
/// </summary>
public class ManageProfilePage : BasePageWithMainLayout
{
    #region Selectors
    public static string UsernameInputSelector => "[data-testid='username-input']";
    public static string GivenNameInputSelector => "[data-testid='given-name-input']";
    public static string SurnameInputSelector => "[data-testid='surname-input']";
    public static string PhoneNumberInputSelector => "[data-testid='phone-number-input']";
    public static string SaveButtonSelector => "[data-testid='save-profile-button']";
    public static string ValidationSummarySelector => "ul[role='alert'] > li.validation-message";
    public static string StatusMessageSelector => "h3:has-text('Profile') + div[role='alert']";
    #endregion Selectors

    #region Constructors
    public ManageProfilePage(IPage page)
        : base(page, "Account/Manage") { }
    #endregion Constructors

    #region Methods
    /// <summary>
    /// Verifies that the profile management page has loaded correctly.
    /// </summary>
    /// <returns>A task representing the verification operation.</returns>
    public override async Task VerifyPageLoadedAsync()
    {
        await _page.WaitForSelectorAsync(UsernameInputSelector);
        await _page.WaitForSelectorAsync(GivenNameInputSelector);
        await _page.WaitForSelectorAsync(SurnameInputSelector);
        await _page.WaitForSelectorAsync(PhoneNumberInputSelector);
        await _page.WaitForSelectorAsync(SaveButtonSelector);
    }

    /// <summary>
    /// Gets the current username displayed in the form.
    /// </summary>
    /// <returns>The username.</returns>
    public async Task<string?> GetUsernameAsync()
    {
        var usernameInput = await _page.QuerySelectorAsync(UsernameInputSelector);
        return usernameInput != null ? await usernameInput.GetAttributeAsync("value") : null;
    }

    /// <summary>
    /// Gets the current given name (first name) displayed in the form.
    /// </summary>
    /// <returns>The given name.</returns>
    public async Task<string?> GetGivenNameAsync()
    {
        var givenNameInput = await _page.QuerySelectorAsync(GivenNameInputSelector);
        return givenNameInput != null ? await givenNameInput.GetAttributeAsync("value") : null;
    }

    /// <summary>
    /// Gets the current surname (last name) displayed in the form.
    /// </summary>
    /// <returns>The surname.</returns>
    public async Task<string?> GetSurnameAsync()
    {
        var surnameInput = await _page.QuerySelectorAsync(SurnameInputSelector);
        return surnameInput != null ? await surnameInput.GetAttributeAsync("value") : null;
    }

    /// <summary>
    /// Gets the current phone number displayed in the form.
    /// </summary>
    /// <returns>The phone number.</returns>
    public async Task<string?> GetPhoneNumberAsync()
    {
        var phoneNumberInput = await _page.QuerySelectorAsync(PhoneNumberInputSelector);
        return phoneNumberInput != null ? await phoneNumberInput.GetAttributeAsync("value") : null;
    }

    /// <summary>
    /// Updates the user profile with the provided information.
    /// </summary>
    /// <param name="givenName">The given name (first name) to update.</param>
    /// <param name="surname">The surname (last name) to update.</param>
    /// <param name="phoneNumber">The phone number to update.</param>
    /// <returns>A task representing the update operation.</returns>
    public async Task UpdateProfileAsync(string? givenName = null, string? surname = null, string? phoneNumber = null)
    {
        if (givenName != null)
        {
            await FillAsync(GivenNameInputSelector, givenName);
        }

        if (surname != null)
        {
            await FillAsync(SurnameInputSelector, surname);
        }

        if (phoneNumber != null)
        {
            await FillAsync(PhoneNumberInputSelector, phoneNumber);
        }

        await ClickAsync(SaveButtonSelector);

        // Wait for the form submission to complete
        await WaitForNavigationAsync();
    }

    /// <summary>
    /// Gets any validation error messages displayed on the page.
    /// </summary>
    /// <returns>The validation error messages text, or null if there are no errors.</returns>
    public async Task<string?> GetValidationErrorsAsync()
    {
        if (await IsVisibleAsync(ValidationSummarySelector))
        {
            return await GetTextContentAsync(ValidationSummarySelector);
        }
        return null;
    }

    /// <summary>
    /// Gets any status message displayed on the page after form submission.
    /// </summary>
    /// <returns>The status message text, or null if there is no status message.</returns>
    public async Task<string?> GetStatusMessageAsync()
    {
        if (await IsVisibleAsync(StatusMessageSelector))
        {
            return await GetTextContentAsync(StatusMessageSelector);
        }
        return null;
    }
    #endregion Methods
}
