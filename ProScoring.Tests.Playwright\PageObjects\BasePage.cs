using System.Threading.Tasks;
using Microsoft.Playwright;

namespace ProScoring.Tests.Playwright.PageObjects;

/// <summary>
/// Base class for all page object models.
/// </summary>
public abstract class BasePage
{
    /// <summary>
    /// This should be set by the fixture before any tests are run.
    /// </summary>
    public static string BaseUrl { get; set; } = "localhost";

    #region Fields
    protected readonly IPage _page;
    #endregion // Fields

    #region Properties
    public string PageUrl { get; protected set; }
    #endregion // Properties

    #region Constructors
    protected BasePage(IPage page, string pageUrl = null!)
    {
        _page = page;
        PageUrl = BaseUrl.TrimEnd('/') + "/" + pageUrl?.TrimStart('/');
    }
    #endregion // Constructors

    #region Methods
    /// <summary>
    /// Navigates to this page.
    /// </summary>
    /// <returns>A task representing the navigation operation.</returns>
    public virtual async Task NavigateAsync()
    {
        await _page.GotoAsync(PageUrl);
        await VerifyPageLoadedAsync();
    }

    /// <summary>
    /// Verifies that the page has loaded correctly.
    /// </summary>
    /// <returns>A task representing the verification operation.</returns>
    public abstract Task VerifyPageLoadedAsync();

    /// <summary>
    /// Clicks an element on the page.
    /// </summary>
    /// <param name="selector">The selector for the element to click.</param>
    /// <returns>A task representing the click operation.</returns>
    public async Task ClickAsync(string selector)
    {
        await _page.ClickAsync(selector);
    }

    /// <summary>
    /// Fills a form field with text.
    /// </summary>
    /// <param name="selector">The selector for the form field.</param>
    /// <param name="text">The text to fill.</param>
    /// <returns>A task representing the fill operation.</returns>
    public async Task FillAsync(string selector, string text)
    {
        await _page.FillAsync(selector, text);
    }

    /// <summary>
    /// Checks if an element is visible on the page.
    /// </summary>
    /// <param name="selector">The selector for the element to check.</param>
    /// <returns>True if the element is visible, false otherwise.</returns>
    public async Task<bool> IsVisibleAsync(string selector)
    {
        return await _page.IsVisibleAsync(selector);
    }

    /// <summary>
    /// Gets the text content of an element.
    /// </summary>
    /// <param name="selector">The selector for the element.</param>
    /// <returns>The text content of the element.</returns>
    public async Task<string?> GetTextContentAsync(string selector)
    {
        return await _page.TextContentAsync(selector);
    }

    /// <summary>
    /// Waits for navigation to complete.
    /// </summary>
    /// <returns>A task representing the wait operation.</returns>
    public async Task WaitForNavigationAsync()
    {
        await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
    }

    /// <summary>
    /// Waits for a selector to be visible.
    /// </summary>
    /// <param name="selector">The selector to wait for.</param>
    /// <returns>A task representing the wait operation.</returns>
    public async Task WaitForSelectorAsync(string selector)
    {
        await _page.WaitForSelectorAsync(selector);
    }
    #endregion // Methods
}
