@page "/organizingauthorities/details"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using ProScoring.Blazor.Extensions
@using ProScoring.BusinessLogic.ServiceInterfaces
@using ProScoring.Domain.Entities
@using ProScoring.Infrastructure.Authorization
@using Ra<PERSON>zen
@using Radzen.Blazor
@inject IOrganizingAuthorityService organizingAuthorityService
@inject NavigationManager navigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Details: @(organizingAuthority?.Name ?? "Loading...")</PageTitle>

<div class="d-flex justify-content-center align-items-start" style="min-height: 100vh; padding: 2rem;">
    <RadzenCard Style="width: 100%; max-width: 800px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 8px;">
        @if (organizingAuthority != null)
        {
            <RadzenStack Gap="1rem" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start">
                @if (!string.IsNullOrEmpty(organizingAuthority?.ImageId))
                {
                    <RadzenImage Path="@($"/api/file/download/{organizingAuthority.ImageId}")"
                        AlternateText="@($"{organizingAuthority.Name} Burgee")"
                        Style="width: 100px; height: 100px; object-fit: contain;" />
                }
                <RadzenText TextStyle="TextStyle.H3">Details: @(organizingAuthority?.Name ?? "Loading...")</RadzenText>
            </RadzenStack>
            <hr />
            <RadzenStack Gap="1rem">
                <RadzenRow>
                    <RadzenColumn Size="4" Class="rz-text-align-right">
                        <RadzenText TextStyle="TextStyle.H6">Name:</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="8">
                        <RadzenText>@organizingAuthority.Name</RadzenText>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow>
                    <RadzenColumn Size="4" Class="rz-text-align-right">
                        <RadzenText TextStyle="TextStyle.H6">Website:</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="8">
                        @if (!string.IsNullOrEmpty(organizingAuthority.Website))
                        {
                            <RadzenLink Path="@organizingAuthority.Website" Target="_blank"
                                Text="@organizingAuthority.Website" />
                        }
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow>
                    <RadzenColumn Size="4" Class="rz-text-align-right">
                        <RadzenText TextStyle="TextStyle.H6">Email:</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="8">
                        @if (!string.IsNullOrEmpty(organizingAuthority.Email))
                        {
                            <RadzenLink Path="@($"mailto:{organizingAuthority.Email}")" Text="@organizingAuthority.Email" />
                        }
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow>
                    <RadzenColumn Size="4" Class="rz-text-align-right">
                        <RadzenText TextStyle="TextStyle.H6">Phone:</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="8">
                        @if (!string.IsNullOrEmpty(organizingAuthority.Phone))
                        {
                            <RadzenLink Path="@($"tel:{organizingAuthority.Phone}")" Text="@organizingAuthority.Phone" />
                        }
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow>
                    <RadzenColumn Size="4" Class="rz-text-align-right">
                        <RadzenText TextStyle="TextStyle.H6">Address:</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="8">
                        <RadzenText>
                            @organizingAuthority.AddressLine1<br />
                            @if (!string.IsNullOrEmpty(organizingAuthority.AddressLine2))
                            {
                                @organizingAuthority.AddressLine2
                                <br />
                            }
                            @organizingAuthority.City, @organizingAuthority.State @organizingAuthority.PostalCode<br />
                            @organizingAuthority.Country
                        </RadzenText>
                    </RadzenColumn>
                </RadzenRow>
                @if (organizingAuthority.Private)
                {
                    <RadzenRow>
                        <RadzenColumn Size="4" Class="rz-text-align-right">
                            <RadzenText TextStyle="TextStyle.H6">Private:</RadzenText>
                        </RadzenColumn>
                        <RadzenColumn Size="8">
                            <RadzenText>@(organizingAuthority.Private ? "Yes" : "No")</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                }
            </RadzenStack>
            <div class="d-flex justify-content-between align-items-center gap-3 mt-4">
                <div>
                    @if (IsHmfic)
                    {
                        <RadzenButton Text="List" ButtonStyle="ButtonStyle.Light" Click="@GoToList" Class="ml-2"
                            @attributes="@("oa-list-button".AsTestId())" />
                    }
                </div>
                <div class="gap-3">
                    <RadzenButton Text="Back" ButtonStyle="ButtonStyle.Light" Click="@GoBack"
                        @attributes="@("oa-back-button".AsTestId())" />
                    <AuthorizeView Policy="@EditAuthorizationForPageWithIdHandler.PolicyName">
                        <Authorized>
                            <RadzenButton Text="Edit" ButtonStyle="ButtonStyle.Primary" Click="Edit"
                                @attributes="@("edit-oa-button".AsTestId())" />
                        </Authorized>
                    </AuthorizeView>
                </div>
            </div>
        }
        else
        {
            <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
        }
    </RadzenCard>
</div>

@code {
    [SupplyParameterFromQuery(Name = "id")]
    public string? Id { get; set; }
    private OrganizingAuthority? organizingAuthority;
    private bool IsHmfic { get; set; }

    /// <summary>
    /// Initializes the component and loads organizing authority data.
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        // Check if the user has HMFIC rights
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        IsHmfic = authState.User.HasClaim(c => c.Type == AuthTypes.HMFIC && c.Value == "true");

        if (!string.IsNullOrEmpty(Id))
        {
            organizingAuthority = await organizingAuthorityService.GetByIdAsync(Id);
        }
    }

    /// <summary>
    /// Navigates to the edit page for the current organizing authority.
    /// </summary>
    private void Edit()
    {
        navigationManager.NavigateTo($"/organizingauthorities/edit?id={organizingAuthority!.Id}");
    }

    /// <summary>
    /// Navigates back to the previous page.
    /// </summary>
    private void GoBack()
    {
        navigationManager.NavigateTo("javascript:window.history.back()");
    }

    /// <summary>
    /// Navigates to the organizing authorities list page.
    /// </summary>
    private void GoToList()
    {
        navigationManager.NavigateTo("/organizingauthorities");
    }
}
