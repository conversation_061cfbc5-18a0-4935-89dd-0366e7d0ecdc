using System.Collections.Generic;
using System.Threading.Tasks;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;

namespace ProScoring.Blazor.Services;

/// <summary>
/// Interface for making HTTP requests to the OrganizingAuthorityController.
/// </summary>
public interface IOrganizingAuthorityHttpClient
{
    /// <summary>
    /// Gets organizing authorities using OData query.
    /// </summary>
    /// <param name="query">The OData query string.</param>
    /// <returns>A list of organizing authorities that match the query.</returns>
    Task<(IEnumerable<OrganizingAuthority>, int TotalCount)> GetWithODataQueryAsync(string query);

    /// <summary>
    /// Gets organizing authority information DTOs and total count using OData query.
    /// </summary>
    /// <param name="query">The OData query string.</param>
    /// <returns>An object containing a list of organizing authority information DTOs and the total count.</returns>
    Task<(IEnumerable<OrganizingAuthorityInfoDto> Items, int TotalCount)> GetInfoWithODataQueryAsync(string query);

    /// <summary>
    /// Gets all unique states from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique states.</returns>
    Task<IEnumerable<string>> GetAllStatesAsync();

    /// <summary>
    /// Gets all unique countries from organizing authorities.
    /// </summary>
    /// <returns>A list of all unique countries.</returns>
    Task<IEnumerable<string>> GetAllCountriesAsync();

    /// <summary>
    /// Gets the total count of organizing authorities.
    /// </summary>
    /// <returns>The total number of organizing authorities.</returns>
    Task<int> GetTotalCountAsync();
}
