@page "/Account/Login"

@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using ProScoring.Domain.Entities
@using System.ComponentModel.DataAnnotations
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services

@inject ILogger<Login> Logger
@inject IdentityRedirectManager RedirectManager
@inject NavigationManager NavigationManager
@inject SignInManager<ApplicationUser> SignInManager
@inject MainLayoutContextService LayoutContext

<PageTitle>Log in</PageTitle>

<h1>Log in</h1>
<div class="row">
    <div class="col-lg-6">
        <section>
            <StatusMessage Message="@errorMessage" />
            <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login">
                <DataAnnotationsValidator />
                <h2>Use a local account to log in.</h2>
                <hr />
                <ValidationSummary class="text-danger" role="alert" data-testid="validation-summary" />
                <div class="form-floating mb-3">
                    <InputText @bind-Value="Input.Email" id="Input.Email" class="form-control" autocomplete="username"
                        aria-required="true" placeholder="<EMAIL>" data-testid="email-input" />
                    <label for="Input.Email" class="form-label">Email</label>
                    <ValidationMessage For="() => Input.Email" class="text-danger" data-testid="email-validation" />
                </div>
                <div class="form-floating mb-3">
                    <InputText type="password" @bind-Value="Input.Password" id="Input.Password" class="form-control"
                        autocomplete="current-password" aria-required="true" placeholder="password"
                        data-testid="password-input" />
                    <label for="Input.Password" class="form-label">Password</label>
                    <ValidationMessage For="() => Input.Password" class="text-danger"
                        data-testid="password-validation" />
                </div>
                <div class="checkbox mb-3">
                    <label class="form-label">
                        <InputCheckbox @bind-Value="Input.RememberMe" class="darker-border-checkbox form-check-input"
                            data-testid="remember-me-checkbox" />
                        Remember me
                    </label>
                </div>
                <div>
                    <button type="submit" class="w-100 btn btn-lg btn-primary" data-testid="submit-login-button">Log
                        in</button>
                </div>
                <div>
                    <p>
                        <a href="Account/ForgotPassword">Forgot your password?</a>
                    </p>
                    <p>
                        <a
                            href="@(NavigationManager.GetUriWithQueryParameters("Account/Register", 
                                                                                                                                                                                                                                                           new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl }))">Register
                            as a new user</a>
                    </p>
                    <p>
                        <a href="Account/ResendEmailConfirmation">Resend email confirmation</a>
                    </p>
                </div>
            </EditForm>
        </section>
    </div>
    <div class="col-lg-4 col-lg-offset-2">
        <section>
            <h3>Use another service to log in.</h3>
            <hr />
            <ExternalLoginPicker />
        </section>
    </div>
</div>

@code {
    private string? errorMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    /// <summary>
    /// Initializes the login page and clears any existing external authentication cookies.
    /// </summary>
    /// <returns>A task representing the asynchronous operation.</returns>
    protected override async Task OnInitializedAsync()
    {
        LayoutContext.UpdateLayout(loginButtonVisible: false);
        if (HttpContext is null)
        {
            Console.Error.WriteLine(@"HttpContext is null in {nameof(Login.razor)}.{nameof(OnInitializeAsync)}");
            return;
        }
        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }
    }

    /// <summary>
    /// Processes the login attempt with the provided credentials.
    /// </summary>
    /// <returns>A task representing the asynchronous login operation.</returns>
    public async Task LoginUser()
    {
        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await SignInManager.PasswordSignInAsync(
        Input.Email,
        Input.Password,
        Input.RememberMe,
        lockoutOnFailure: false);

        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");
            RedirectManager.RedirectTo(ReturnUrl);
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo(
            "Account/LoginWith2fa",
            new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else
        {
            errorMessage = "Error: Invalid login attempt.";
        }
    }

    /// <summary>
    /// Model class for capturing login form input.
    /// </summary>
    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }
}
