@page "/demo/organizing-authority-info"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using ProScoring.Blazor.Components.Shared
@using ProScoring.Blazor.Extensions
@using ProScoring.Blazor.Services
@using ProScoring.Domain.Common
@using ProScoring.Domain.Dtos
@using ProScoring.Infrastructure.Authorization
@using System.Security.Claims
@attribute [Authorize(Policy = "HmficAuthorizationPolicy")]
@inject DialogService DialogService
@inject TooltipService TooltipService
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IOrganizingAuthorityHttpClient OrganizingAuthorityClient
@inject IJSRuntime JSRuntime

<PageTitle>Organizing Authority Info Component Demo</PageTitle>

<style>
    /* Direct inline styles to ensure scrolling works */
    .scrollable-demo-page {
        height: auto;
        overflow-y: auto;
        padding: 2rem;
        margin-bottom: 100px;
        /* Extra space at bottom */
    }
</style>

<div class="scrollable-demo-page">
    <RadzenStack Orientation="Orientation.Vertical" Gap="2rem">
        <RadzenText TextStyle="TextStyle.H3">Organizing Authority Info Component Demo</RadzenText>

        <RadzenCard>
            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                <RadzenText TextStyle="TextStyle.H5">Display Options</RadzenText>

                <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                    <RadzenLabel Text="Display Mode" />
                    <RadzenSelectBar @bind-Value="@displayMode" Style="margin-bottom: 20px;">
                        <Items>
                            <RadzenSelectBarItem Text="Card" Value="@DisplayMode.Card" />
                            <RadzenSelectBarItem Text="Row" Value="@DisplayMode.Row" />
                        </Items>
                    </RadzenSelectBar>
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                    <RadzenLabel Text="Show Additional Info" />
                    <RadzenSwitch @bind-Value="@showAdditionalInfo" />
                </RadzenStack>
            </RadzenStack>
        </RadzenCard>

        <RadzenText TextStyle="TextStyle.H5">Real Data from Database</RadzenText>

        @if (isLoading)
        {
            <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate"
                Style="margin-bottom: 20px" />
        }
        else if (!string.IsNullOrEmpty(errorMessage))
        {
            <RadzenAlert AlertStyle="AlertStyle.Danger" ShowIcon="true" ShowCloseButton="false">
                @errorMessage
            </RadzenAlert>
        }
        else if (authorityItems != null && authorityItems.Any())
        {
            <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                <RadzenCard>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem"
                        JustifyContent="JustifyContent.SpaceBetween">
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                            <RadzenLabel Text="Search" />
                            <RadzenTextBox Placeholder="Search..." Change="@(args => ApplyFilter(args))"
                                Style="width: 200px;" />
                        </RadzenStack>

                        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                            <RadzenLabel Text="Sort By" />
                            <RadzenDropDown TValue="string" Data="@(new[] { "Name", "City", "State", "Country" })"
                                Value="@sortBy" Change="@OnSortFieldChanged" Style="width: 150px;" />

                            <RadzenButton Icon="@(sortOrder == "asc" ? "arrow_upward" : "arrow_downward")"
                                Click="@OnToggleSortOrder" ButtonStyle="ButtonStyle.Light" />
                        </RadzenStack>
                    </RadzenStack>
                </RadzenCard>

                <RadzenDataGrid Data="@authorityItems" TItem="OrganizingAuthorityInfoDto" AllowPaging="false"
                    SelectionMode="DataGridSelectionMode.Single" RowSelect="@OnRowSelect">
                    <Columns>
                        <RadzenDataGridColumn TItem="OrganizingAuthorityInfoDto" Property="Name" Title="Name" />
                        <RadzenDataGridColumn TItem="OrganizingAuthorityInfoDto" Property="City" Title="City" />
                        <RadzenDataGridColumn TItem="OrganizingAuthorityInfoDto" Property="State" Title="State" />
                        <RadzenDataGridColumn TItem="OrganizingAuthorityInfoDto" Property="Country" Title="Country" />
                        <RadzenDataGridColumn TItem="OrganizingAuthorityInfoDto" Property="Private" Title="Private">
                            <Template Context="data">
                                <RadzenIcon Icon="@(data.Private ? "lock" : "lock_open")" />
                            </Template>
                        </RadzenDataGridColumn>
                    </Columns>
                </RadzenDataGrid>

                <RadzenPager Count="@totalCount" PageSize="@pageSize" PageNumbersCount="5" PageChanged="@OnPageChanged" />

                @if (selectedAuthority != null)
                {
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem"
                        JustifyContent="JustifyContent.SpaceBetween">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                            <RadzenText TextStyle="TextStyle.H5">Selected Authority</RadzenText>
                            <RadzenButton Icon="arrow_back" ButtonStyle="ButtonStyle.Light"
                                Click="@(async () => await PreviousAuthority())"
                                Disabled="@(authorityItems.Count <= 1 || (selectedAuthorityIndex <= 0 && currentPage <= 1))"
                                @attributes="@("previous-authority-button".AsTestId())" />
                            <RadzenButton Icon="arrow_forward" ButtonStyle="ButtonStyle.Light"
                                Click="@(async () => await NextAuthority())"
                                Disabled="@(authorityItems.Count <= 1 || (selectedAuthorityIndex >= authorityItems.Count - 1 && currentPage >= Math.Ceiling((double)totalCount / pageSize)))"
                                @attributes="@("next-authority-button".AsTestId())" />
                        </RadzenStack>
                        <RadzenText TextStyle="TextStyle.Body1">
                            @(selectedAuthorityIndex + 1) of @authorityItems.Count (Page
                            @currentPage of @Math.Ceiling((double)totalCount / pageSize))
                        </RadzenText>
                    </RadzenStack>
                    <OrganizingAuthorityInfo Authority="@selectedAuthority" DisplayMode="@displayMode"
                        ShowAdditionalInfo="@showAdditionalInfo" />
                }
            </RadzenStack>
        }
        else
        {
            <RadzenAlert AlertStyle="AlertStyle.Info" ShowIcon="true" ShowCloseButton="false">
                No organizing authorities found. Please create some organizing authorities first.
            </RadzenAlert>
        }

        <RadzenText TextStyle="TextStyle.H5">Sample Data Examples</RadzenText>

        <RadzenText TextStyle="TextStyle.H6">Full Data Example</RadzenText>
        <OrganizingAuthorityInfo Authority="@fullDataAuthority" DisplayMode="@displayMode"
            ShowAdditionalInfo="@showAdditionalInfo" />

        <RadzenText TextStyle="TextStyle.H6">Minimal Data Example</RadzenText>
        <OrganizingAuthorityInfo Authority="@minimalDataAuthority" DisplayMode="@displayMode"
            ShowAdditionalInfo="@showAdditionalInfo" />

        <RadzenText TextStyle="TextStyle.H6">No Data Example</RadzenText>
        <OrganizingAuthorityInfo Authority="@nullAuthority" DisplayMode="@displayMode"
            ShowAdditionalInfo="@showAdditionalInfo" />
    </RadzenStack>
</div>

<!-- Add a spacer at the bottom to ensure content is scrollable -->
<div style="height: 50px;"></div>

@code {
    #region Properties
    private DisplayMode displayMode = DisplayMode.Card;
    private bool showAdditionalInfo = true;
    private List<OrganizingAuthorityInfoDto> authorityItems = new();
    private OrganizingAuthorityInfoDto? selectedAuthority;
    private int selectedAuthorityIndex = 0; // Track the index of the selected authority
    private OrganizingAuthorityInfoDto? fullDataAuthority;
    private OrganizingAuthorityInfoDto? minimalDataAuthority;
    private OrganizingAuthorityInfoDto? nullAuthority = null;
    private bool isLoading = true;
    private string? errorMessage;

    // Pagination parameters
    private int currentPage = 1;
    private int pageSize = 5;
    private int totalCount = 0;
    private string sortBy = "Name"; // Not nullable to avoid warnings
    private string sortOrder = "asc"; // Not nullable to avoid warnings
    private string? filter = null;
    #endregion Properties

    #region Methods
    protected override async Task OnInitializedAsync()
    {
        // The HMFIC authorization is now handled by the policy attribute
        // so we don't need to check it manually here

        await LoadDataAsync();

        // Keep the sample data for the demo examples
        fullDataAuthority = new OrganizingAuthorityInfoDto
        {
            Id = "O123",
            Name = "Seattle Yacht Club",
            Email = "<EMAIL>",
            Phone = "************",
            Website = "https://seattleyachtclub.org",
            City = "Seattle",
            State = "Washington",
            Country = "USA",
            ImageId = null, // No image for demo purposes
            Private = false
        };

        minimalDataAuthority = new OrganizingAuthorityInfoDto
        {
            Id = "O456",
            Name = "Minimal Yacht Club",
            ImageId = null,
            Private = true
        };
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Enable scrolling for this page - with a slight delay to ensure DOM is fully rendered
            await Task.Delay(100); // Short delay
            await JSRuntime.InvokeVoidAsync("enableScrolling");

            // Try again after a longer delay to ensure it works
            await Task.Delay(500);
            await JSRuntime.InvokeVoidAsync("enableScrolling");
        }
    }

    private async Task LoadDataAsync()
    {
        try
        {
            // Always set isLoading to true when loading data
            isLoading = true;
            errorMessage = null;

            // Build OData query string
            var odataQuery = new List<string>();

            // Add pagination
            odataQuery.Add($"$skip={(currentPage - 1) * pageSize}");
            odataQuery.Add($"$top={pageSize}");

            // Add sorting
            odataQuery.Add($"$orderby={sortBy} {sortOrder}");

            // Add filtering if provided
            if (!string.IsNullOrEmpty(filter))
            {
                odataQuery.Add($"$filter=contains(Name,'{filter}') or contains(City,'{filter}') or contains(State,'{filter}')");
            }

            // Count total items
            var countQuery = "$count=true";

            // Combine all query parts
            var fullQuery = string.Join("&", odataQuery) + "&" + countQuery;

            // Fetch real data from the controller via HTTP client using OData
            var result = await OrganizingAuthorityClient.GetInfoWithODataQueryAsync(fullQuery);

            // Convert to list and store
            authorityItems = (result.Items).ToList();

            totalCount = result.TotalCount;

            // Select the first authority as the default selected authority if available
            if (authorityItems.Any())
            {
                // If we don't have a selected authority or we're not in a navigation operation
                // (which would have set selectedAuthority already)
                if (selectedAuthority == null || selectedAuthorityIndex < 0)
                {
                    selectedAuthorityIndex = 0;
                    selectedAuthority = authorityItems.First();
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error loading organizing authorities: {ex.Message}";
        }
        finally
        {
            // Always set isLoading to false when done loading
            isLoading = false;
        }
    }

    private async Task OnPageChanged(PagerEventArgs args)
    {
        // Convert from 0-based PageIndex to 1-based currentPage
        int newPage = args.PageIndex + 1;

        if (newPage != currentPage)
        {
            currentPage = newPage;
            await LoadDataAsync();
        }
    }

    private async Task ChangeSort(string column)
    {
        if (sortBy == column)
        {
            // Toggle sort order
            sortOrder = sortOrder == "asc" ? "desc" : "asc";
        }
        else
        {
            sortBy = column;
            sortOrder = "asc";
        }

        await LoadDataAsync();
    }

    private async Task OnSortFieldChanged(object value)
    {
        if (value is string field)
        {
            await ChangeSort(field);
        }
    }

    private async Task OnToggleSortOrder()
    {
        // Just toggle the sort order for the current field
        sortOrder = sortOrder == "asc" ? "desc" : "asc";
        await LoadDataAsync();
    }

    private async Task ApplyFilter(string? newFilter)
    {
        filter = newFilter;
        currentPage = 1; // Reset to first page when filtering
        await LoadDataAsync();
    }

    private void OnRowSelect(OrganizingAuthorityInfoDto authority)
    {
        if (authorityItems != null)
        {
            // Find the index of the selected authority in the list
            for (int i = 0; i < authorityItems.Count; i++)
            {
                if (authorityItems[i].Id == authority.Id)
                {
                    selectedAuthorityIndex = i;
                    selectedAuthority = authority;
                    return;
                }
            }
        }

        // If we get here, we didn't find the authority in the list
        selectedAuthority = authority;
    }

    /// <summary>
    /// Navigate to the previous authority in the list, handling pagination if necessary
    /// </summary>
    private async Task PreviousAuthority()
    {
        if (authorityItems == null || authorityItems.Count <= 1)
            return;

        // If we're at the first item in the current page
        if (selectedAuthorityIndex <= 0)
        {
            // Check if there's a previous page
            if (currentPage > 1)
            {
                // Load the previous page
                currentPage--;

                // Set the index to -1 to indicate we want the last item after loading
                selectedAuthorityIndex = -1;

                await LoadDataAsync();

                // Select the last item in the new page
                if (authorityItems.Any())
                {
                    selectedAuthorityIndex = authorityItems.Count - 1;
                    selectedAuthority = authorityItems[selectedAuthorityIndex];
                }
            }
        }
        else
        {
            // Just move to the previous item in the current page
            selectedAuthorityIndex--;
            selectedAuthority = authorityItems[selectedAuthorityIndex];
        }
    }

    /// <summary>
    /// Navigate to the next authority in the list, handling pagination if necessary
    /// </summary>
    private async Task NextAuthority()
    {
        if (authorityItems == null || authorityItems.Count <= 1)
            return;

        // If we're at the last item in the current page
        if (selectedAuthorityIndex >= authorityItems.Count - 1)
        {
            // Check if there's a next page
            if (currentPage < Math.Ceiling((double)totalCount / pageSize))
            {
                // Load the next page
                currentPage++;

                // We want the first item after loading
                selectedAuthorityIndex = 0;

                await LoadDataAsync();

                // Select the first item in the new page
                if (authorityItems.Any())
                {
                    selectedAuthorityIndex = 0;
                    selectedAuthority = authorityItems[selectedAuthorityIndex];
                }
            }
        }
        else
        {
            // Just move to the next item in the current page
            selectedAuthorityIndex++;
            selectedAuthority = authorityItems[selectedAuthorityIndex];
        }
    }

    private void ShowTooltip(ElementReference elementReference, string text, TooltipOptions options)
    {
        TooltipService.Open(elementReference, text, options);
    }
    #endregion Methods
}
