using System.Security.Claims;
using FluentAssertions;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using ProScoring.Tests.Helpers;

namespace ProScoring.Tests.Infrastructure;

public class NewApplicationDbContextTests
{
    #region fields
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ApplicationDbContext _context;
    private readonly FixedDateTimeOffsetProvider _dateTimeProvider;
    private readonly IValueGenerator _idGenerator;
    private readonly ILogger<ApplicationDbContext> _logger;
    private readonly string _userId = "U-test-user-id";
    private readonly DateTimeOffset _testTime;
    #endregion

    public NewApplicationDbContextTests()
    {
        _authStateProvider = Substitute.For<AuthenticationStateProvider>();
        _logger = Substitute.For<ILogger<ApplicationDbContext>>();
        _idGenerator = new CustomIdValueGenerator(
            Substitute.For<IIdGenerationUtilService>(),
            Substitute.For<ILogger<CustomIdValueGenerator>>()
        );
        _dateTimeProvider = new FixedDateTimeOffsetProvider(1776, 7, 4, 12, 0, 0);
        _testTime = _dateTimeProvider.UtcNow;

        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        SetupAuthenticationState();

        _context = new ApplicationDbContext(options, _authStateProvider, _idGenerator, _logger, _dateTimeProvider);
    }

    #region methods

    [Fact]
    public void SaveChanges_WhenAddingEntity_SetsChangeTrackingFields()
    {
        // Arrange
        var user = new ApplicationUser();
        _context.Set<ApplicationUser>().Add(user);

        // Act
        _context.SaveChanges();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenAddingEntity_SetsChangeTrackingFields()
    {
        // Arrange
        var user = new ApplicationUser();
        await _context.Set<ApplicationUser>().AddAsync(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(_userId);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenNoChanges_DoesNotUpdateFields()
    {
        // Arrange

        var createTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createTime.Year, createTime.Month, createTime.Day);
        var user = new ApplicationUser();
        SetupAuthenticationState("original-user");

        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();

        _dateTimeProvider.Pop();
        SetupAuthenticationState();

        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Reattach without modifications
        _context.Set<ApplicationUser>().Attach(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(createTime);
        user.UpdatedById.Should().Be("original-user");
    }

    [Fact]
    public async Task SaveChangesAsync_WhenUpdatingEntity_UpdatesChangeTrackingFields()
    {
        // Arrange
        var createdTime = _testTime.AddDays(-1);
        _dateTimeProvider.SetDate(createdTime.Year, createdTime.Month, createdTime.Day);
        var user = new ApplicationUser();

        SetupAuthenticationState("original-user");
        await _context.Set<ApplicationUser>().AddAsync(user);
        await _context.SaveChangesAsync();
        SetupAuthenticationState();

        _dateTimeProvider.Pop();
        // Clear tracking to simulate a fresh load
        _context.ChangeTracker.Clear();

        // Modify user
        user.PhoneNumber = "modified";
        _context.Set<ApplicationUser>().Update(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(createdTime);
        user.CreatedById.Should().Be("original-user");
        user.UpdatedAt.Should().Be(_testTime);
        user.UpdatedById.Should().Be(_userId);
    }

    [Fact]
    public async Task FileRecordSaveChangesAsync_WithNoAuthenticationState_UsesNullUser()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromException<AuthenticationState>(new InvalidOperationException()));

        var fileRecord = new FileRecord
        {
            ContentType = "application/octet-stream",
            Path = "test/path",
            TrustedFileNameForDisplay = "test.txt",
            UntrustedName = "original.txt",
        };
        await _context.Set<FileRecord>().AddAsync(fileRecord);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        fileRecord.CreatedAt.Should().Be(_testTime);
        fileRecord.UpdatedAt.Should().Be(_testTime);
        fileRecord.CreatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
        fileRecord.UpdatedById.Should().Be(ApplicationDbContext.NULL_USER_ID);
    }

    [Fact]
    public async Task ApplicationUserSaveChangesAsync_WithNoAuthenticationState_UsesNewUserId()
    {
        // Arrange
        _authStateProvider
            .GetAuthenticationStateAsync()
            .Returns(Task.FromException<AuthenticationState>(new InvalidOperationException()));

        var user = new ApplicationUser
        {
            UserName = "testuser",
            Email = "<EMAIL>",
            Id = "testuser-id",
        };
        await _context.Set<ApplicationUser>().AddAsync(user);

        // Act
        await _context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(_testTime);
        user.UpdatedAt.Should().Be(_testTime);
        user.CreatedById.Should().Be(user.Id);
        user.UpdatedById.Should().Be(user.Id);
    }

    private void SetupAuthenticationState(string? userId = null)
    {
        userId ??= _userId;
        var claims = new[] { new Claim(ClaimTypes.NameIdentifier, userId) };
        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var authState = new AuthenticationState(principal);

        _authStateProvider.GetAuthenticationStateAsync().Returns(Task.FromResult(authState));
    }

    #endregion
}
