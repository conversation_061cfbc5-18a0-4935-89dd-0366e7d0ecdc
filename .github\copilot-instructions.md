# Copilot Instructions
## General Instructions
This project is for a web application for registering and scoring participants in sailing regattas.

It will allow users to register either with saved profile information, or, if they don't have an account, with temporary profile information.
## Misc Instructions to check on
- make sure that `string.join` has the proper casing.  I think it needs to be capital J.
## Frameworks and Libraries
- Blazor UI code uses Radzen.Blazor components.
- The project uses Entity Framework Core for data access.
## Code style guidelines:
- IMPORTANT: NEVER discard any existing comments 
- usings should be first and sorted alphabetically.
- namespaces should be file scoped whenever possible.
- any class elements that are within a custom region, other than "properties", "fields", "methods" should not be moved or sorted unless explicitly stated.
- regions should be used to group class elements.
- Use the coding guidelines in .editorconfig file when reformatting the code.
- I prefer to have my code files organized with:
  - fields first (public, protected, private, static, readonly)
  - then constructors
  - then properties
    - grouped by access level (public, protected, private)
    - and in alphabetical order
    - if properties are decorated with Column(Order) attribute, then sort by the order value
  - then methods
    - grouped by access level (public, protected, private)
    - and in alphabetical order
  - then events
    - grouped by access level (public, protected, private)
    - and in alphabetical order
  - then nested types
    - grouped by access level (public, protected, private)
    - and in alphabetical order
- always  wrapping long lines. 

- always include thorough documentation for methods and events.
- always include documentation for classes and interfaces.

## Tests
- Tests should use xUnit as the testing framework.
- Tests should use NSubstitute for mocking dependencies.
- Tests should use FluentAssertions for assertions.
- Tests should be organized in the same way as the code they are testing.
- Tests should be named using the format: ClassName_MethodName_ExpectedBehavior.

### Specific Instructions
Example of a well-documented method:
/// <summary>
/// Registers a new participant for an event.
/// </summary>
/// <param name="participant">The participant to register.</param>
/// <param name="eventId">The ID of the event.</param>
/// <returns>True if registration is successful, otherwise false.</returns>
public bool RegisterParticipant(Participant participant, string eventId)
{
    // Method implementation
}
