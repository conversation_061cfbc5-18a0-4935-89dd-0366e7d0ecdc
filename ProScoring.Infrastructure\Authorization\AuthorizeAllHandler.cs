using Microsoft.AspNetCore.Authorization;

namespace ProScoring.Infrastructure.Authorization;

/// <summary>
/// Authorization handler that authorizes all authenticated users.
/// This handler is useful for resources that require authentication but don't need specific permissions.
/// </summary>
public class AuthorizeAllHandler : AuthorizationHandler<AuthorizeAllHandler.Requirement>
{
    /// <summary>
    /// The policy name used for the authorize all requirement.
    /// </summary>
    public static readonly string PolicyName = nameof(AuthorizeAllHandler).Replace("Handler", "Policy");

    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, Requirement requirement)
    {
        if (context.User.Identity?.IsAuthenticated == true)
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// The authorization requirement for the authorize all policy.
    /// This class is used to identify the requirement type for the handler.
    /// </summary>
    public class Requirement : IAuthorizationRequirement { }
}
