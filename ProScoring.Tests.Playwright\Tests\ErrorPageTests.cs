using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the error pages of the ProScoring application.
/// </summary>
public class ErrorPageTests : IClassFixture<PlaywrightFixture>
{
    #region Fields
    private readonly PlaywrightFixture _fixture;
    #endregion

    #region Constructors
    public ErrorPageTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion

    #region Test Methods
    /// <summary>
    /// Tests that navigating to a non-existent page shows the 404 error page.
    /// </summary>
    [Fact]
    public async Task Navigating_To_NonExistent_Page_Shows_404()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Act - Navigate to a non-existent page
            await page.GotoAsync($"{_fixture.Settings.BaseUrl}/non-existent-page-12345");

            // Assert - Should be redirected to error page
            page.Url.Should().Contain("/error");

            // Verify error page content
            var errorPage = new ErrorPage(page);
            await errorPage.VerifyPageLoadedAsync();

            var errorCode = await errorPage.GetErrorCodeAsync();
            errorCode.Should().Contain("404");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking the return button on the error page navigates back to home.
    /// </summary>
    [Fact]
    public async Task Clicking_Return_Button_On_Error_Page_Navigates_To_Home()
    {
        // Arrange - Go to error page
        var page = await _fixture.CreatePageAsync();
        try
        {
            await page.GotoAsync($"{_fixture.Settings.BaseUrl}/non-existent-page-12345");

            var errorPage = new ErrorPage(page);
            await errorPage.VerifyPageLoadedAsync();

            // Act - Click return to home button
            await errorPage.ClickReturnToHomeAsync();

            // Assert - Should be back on home page
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Normalize URLs by removing trailing slashes for comparison
            var currentUrl = page.Url.TrimEnd('/');
            var expectedBaseUrl = _fixture.Settings.BaseUrl.TrimEnd('/');

            // Check if we're at the home page (either exactly at base URL or base URL + "/")
            currentUrl.Should().BeOneOf(expectedBaseUrl, expectedBaseUrl + "/");
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that error page correctly displays the status code and error message.
    /// </summary>
    [Fact]
    public async Task Error_Page_Displays_Correct_Information()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Act - Navigate to a non-existent page
            await page.GotoAsync($"{_fixture.Settings.BaseUrl}/non-existent-page-12345");

            // Assert - Error page should show correct information
            var errorPage = new ErrorPage(page);
            await errorPage.VerifyPageLoadedAsync();

            var errorCode = await errorPage.GetErrorCodeAsync();
            errorCode.Should().Contain("404");

            // Verify error message is present
            var errorMessage = await errorPage.GetErrorMessageAsync();
            errorMessage.Should().NotBeNullOrEmpty();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that navigating to a page requiring authorization when unauthenticated shows the proper error page.
    /// </summary>
    [Fact]
    public async Task Navigating_To_Protected_Route_When_Unauthenticated_Shows_Error()
    {
        // Arrange
        var page = await _fixture.CreatePageAsync();
        try
        {
            // Act - Navigate to a protected route without authentication
            await page.GotoAsync($"{_fixture.Settings.BaseUrl}/xxx123");

            // Assert - Should be redirected to error page
            var errorPage = new ErrorPage(page);
            await errorPage.VerifyPageLoadedAsync();

            var errorCode = await errorPage.GetErrorCodeAsync();
            // The status code may be 401 (Unauthorized) or 403 (Forbidden) depending on your setup
            errorCode.Should().BeOneOf("401", "403", "404");

            var errorMessage = await errorPage.GetErrorMessageAsync();
            errorMessage.Should().NotBeNullOrEmpty();
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion
}
