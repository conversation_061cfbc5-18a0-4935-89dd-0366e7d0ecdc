using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Playwright;
using ProScoring.Tests.Playwright.PageObjects;
using ProScoring.Tests.Playwright.TestFixtures;
using Xunit;

namespace ProScoring.Tests.Playwright.Tests;

/// <summary>
/// Tests for the user options functionality of the ProScoring application.
/// </summary>
public class UserOptionsTests : IClassFixture<PlaywrightFixture>
{
    #region Fields
    private readonly PlaywrightFixture _fixture;
    #endregion Fields

    #region Constructors
    public UserOptionsTests(PlaywrightFixture fixture)
    {
        _fixture = fixture;
    }
    #endregion Constructors

    #region Test Methods
    /// <summary>
    /// Tests that the user options menu contains the expected number of options.
    /// </summary>
    [Fact]
    public async Task UserOptions_Menu_Contains_Two_Options()
    {
        // Arrange - Log in first
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);

            // Wait for redirect after login
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Act - Open user options menu
            var homePage = new HomePage(page);
            await homePage.ClickMyAccountMenuItem();

            // Assert - Verify there are 2 options
            var optionsCount = await homePage.GetUserMenuOptionsCount();
            optionsCount.Should().Be(2);
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking the logout option logs the user out.
    /// </summary>
    [Fact]
    public async Task Clicking_Logout_Option_Logs_User_Out()
    {
        // Arrange - Log in first
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);

            // Wait for redirect after login
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Act - Open user options and click logout
            var homePage = new HomePage(page);
            await homePage.ClickMyAccountMenuItem();
            await homePage.ClickLogoutOption();

            // Wait for redirect after logout
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Assert - Login button should be visible again
            var isLoginButtonVisible = await homePage.IsLoginButtonVisible();
            isLoginButtonVisible.Should().BeTrue();
        }
        finally
        {
            await page.CloseAsync();
        }
    }

    /// <summary>
    /// Tests that clicking the account info option navigates to the account page.
    /// </summary>
    [Fact]
    public async Task Clicking_AccountInfo_Option_Navigates_To_Account_Page()
    {
        // Arrange - Log in first
        var page = await _fixture.CreatePageAsync();
        try
        {
            var loginPage = new LoginPage(page);
            await loginPage.NavigateAsync();
            await loginPage.LoginAsync(_fixture.Settings.TestUserEmail, _fixture.Settings.TestUserPassword);

            // Wait for redirect after login
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Act - Open user options and click account info
            var homePage = new HomePage(page);
            await homePage.ClickMyAccountMenuItem();
            await homePage.ClickAccountInfoOption();

            // Wait for redirect to account page
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(1000); // Sleep for 1 second

            // Assert - We should be on the account page
            page.Url.ToLower().Should().Contain("/account");

            // Verify account page elements
            var manageProfilePage = new ManageProfilePage(page);
            await manageProfilePage.VerifyPageLoadedAsync();

            var username = await manageProfilePage.GetUsernameAsync();
            username.Should().NotBeNullOrEmpty();
        }
        finally
        {
            await page.CloseAsync();
        }
    }
    #endregion Test Methods
}
