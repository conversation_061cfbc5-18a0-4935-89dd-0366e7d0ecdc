using System.Linq.Dynamic.Core;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using MockQueryable.NSubstitute;
using NSubstitute;
using ProScoring.Domain.Entities;
using ProScoring.Infrastructure.Authorization;
using ProScoring.Infrastructure.Authorization.Entities;
using ProScoring.Infrastructure.Database;
using ProScoring.Tests.Helpers;
using Xunit.Abstractions;

namespace ProScoring.Tests.Infrastructure;

public class ProScoringAuthorizationServiceTests
{
    private ProScoringAuthorizationService _authorizationService;
    private readonly IApplicationDbContext _mockDbContext;
    private readonly ITestOutputHelper _output;

    public ProScoringAuthorizationServiceTests(ITestOutputHelper output)
    {
        _output = output;
        _mockDbContext = Substitute.For<IApplicationDbContext>();
        _authorizationService = new ProScoringAuthorizationService(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>()
        );
    }

    #region helpers

    /// <summary>
    /// Creates an AuthAction with the specified name.
    /// </summary>
    /// <param name="name">The name of the auth action.</param>
    /// <returns>A new AuthAction instance.</returns>
    private AuthAction CreateAuthAction(string name)
    {
        return new AuthAction { Name = name };
    }

    /// <summary>
    /// Creates a list of AuthActions with the specified names.
    /// </summary>
    /// <param name="names">The names of the auth actions to create.</param>
    /// <returns>A list of AuthAction instances.</returns>
    private List<AuthAction> CreateAuthActions(params string[] names)
    {
        return names.Select(name => CreateAuthAction(name)).ToList();
    }

    #endregion helpers

    #region methods

    /// <summary>
    /// Note: this test didn't fail before I made the change that was suposed to fix it.
    /// </summary>
    /// <returns></returns>
    [Fact]
    public async Task ExpandActionsAsync_ShouldExecuteWithoutException_WhenIncludeExpressionIsValid()
    {
        // Arrange
        // Create reusable auth actions
        var action1 = CreateAuthAction("Action1");
        var action2 = CreateAuthAction("Action2");

        var authActions = new List<AuthAction> { action1, action2 };

        var actionHierarchies = new List<ActionHierarchy>
        {
            new ActionHierarchy
            {
                ParentAction = action1,
                ParentActionName = action1.Name,
                ChildAction = action2,
                ChildActionName = action2.Name,
            },
        }
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.ActionHierarchies.Returns(actionHierarchies);

        var authActionsDbSet = authActions.AsQueryable().BuildMockDbSet();
        _mockDbContext.AuthActions.Returns(authActionsDbSet);

        // Act
        var exception = await Record.ExceptionAsync(
            () => _authorizationService.ExpandActionsAsync(new List<AuthAction> { action1 })
        );

        // Assert
        Assert.Null(exception);
    }

    [Fact]
    public async Task CreateUserAuthActionAsync_ShouldCreateUserAuthAction()
    {
        // Arrange
        var userId = "user1";
        var targetId = "T134";
        var action = AuthTypes.Actions.VIEW;
        var authAction = CreateAuthAction(action);
        var userAuthAction = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
        };

        var spyAuthService = Substitute.ForPartsOf<ProScoringAuthorizationService>(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>()
        );

        var mockUaaDbSet = new List<UserAuthAction>().AsQueryable().BuildMockDbSet();
        _mockDbContext.AuthActions.FindAsync(action).Returns(authAction);
        _mockDbContext.Users.FindAsync(userId).Returns(new ApplicationUser { Id = userId });
        _mockDbContext.UserAuthActions.Returns(mockUaaDbSet);
        var mockTargetTypeDbSet = new List<TargetType>
        {
            new TargetType { IdPrefix = "T", Name = "TestTargetType" },
        }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.TargetTypes.Returns(mockTargetTypeDbSet);

        _mockDbContext.ClearReceivedCalls(); // there were calls because of the setup.

        // Act
        var result = await spyAuthService.CreateUserAuthActionAsync(userId, targetId, action);

        // Assert
        result.Should().NotBeNull();
        result.UserId.Should().Be(userId);
        result.TargetId.Should().Be(targetId);
        result.AuthActionName.Should().Be(action);

        // check it confirms the action
        await _mockDbContext.Received(1).AuthActions.FindAsync(action);
        // check it confirms the user
        await _mockDbContext.Received(1).Users.FindAsync(userId);
        // check it confirms the target type
        await _mockDbContext.Received().TargetTypes.FindAsync("T");
        // check we confirm the action doesn't already exist
        await spyAuthService.Received(1).GetActionAsync(userId, targetId, action);
        // check it adds the action
        _mockDbContext.UserAuthActions.Received(1).Add(Arg.Is<UserAuthAction>(uaa => uaa.Equals(userAuthAction)));
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task CreateUserAuthActionsAsync_ShouldCreateMultipleUserAuthActions()
    {
        // Arrange
        var userId = "user1";
        var targetIds = new List<string> { "T1234", "T2345" };
        var action = AuthTypes.Actions.VIEW;
        var authAction = CreateAuthAction(action);
        var user = new ApplicationUser { Id = userId };

        _mockDbContext.AuthActions.FindAsync(action).Returns(authAction);
        var mockUaaDbSet = new List<UserAuthAction>().AsQueryable().BuildMockDbSet();
        _mockDbContext.Users.FindAsync(userId).Returns(user);
        _mockDbContext.UserAuthActions.Returns(mockUaaDbSet);
        var mockTargetTypeDbSet = new List<TargetType>
        {
            new TargetType { IdPrefix = "T", Name = "TestTargetType" },
        }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.TargetTypes.Returns(mockTargetTypeDbSet);

        var spyAuthService = Substitute.ForPartsOf<ProScoringAuthorizationService>(
            _mockDbContext,
            _output.BuildSafeLoggerFor<ProScoringAuthorizationService>()
        );

        _mockDbContext.ClearReceivedCalls(); // there may have been were calls because of the setup.

        // Act
        var result = await spyAuthService.CreateUserAuthActionsAsync(userId, targetIds, action);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(targetIds.Count);
        result.ForEach(uaa =>
        {
            uaa.UserId.Should().Be(userId);
            uaa.AuthActionName.Should().Be(action);
        });
        result.Select(uaa => uaa.TargetId).Should().BeEquivalentTo(targetIds);
        // check it confirms the user
        await _mockDbContext.Received(1).Users.FindAsync(userId);
        // check it confirms the action
        await _mockDbContext.Received(1).AuthActions.FindAsync(action);
        // check it confirms the target type
        await _mockDbContext.Received().TargetTypes.FindAsync("T");
        // check we confirm the action doesn't already exist
        await spyAuthService
            .Received(targetIds.Count)
            .GetActionAsync(userId, Arg.Is<string>(tId => targetIds.Contains(tId)), action);
        // check it adds the actions
        _mockDbContext
            .UserAuthActions.Received(1)
            .AddRange(Arg.Is<IEnumerable<UserAuthAction>>(uaas => uaas.All(uaa => uaa.UserId == userId)));
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task DeleteAllUserAuthActionsForUserTargetAsync_ShouldDeleteUserAuthAction()
    {
        // Arrange
        var userId = "user1";
        var targetId = "target1";
        var action = AuthTypes.Actions.VIEW;
        var userAuthAction = new UserAuthAction
        {
            UserId = userId,
            TargetId = targetId,
            AuthActionName = action,
        };

        _mockDbContext.UserAuthActions.FindAsync(userId, targetId, action).Returns(userAuthAction);

        // Act
        await _authorizationService.DeleteUserAuthActionAsync(userId, targetId, action);

        // Assert
        _mockDbContext.UserAuthActions.Received(1).Remove(userAuthAction);
        await _mockDbContext.Received(1).SaveChangesAsync(Arg.Any<CancellationToken>());
    }

    /*
    [Fact]
    public async Task DeleteUserAuthActionsAsync_ShouldDeleteMultipleUserAuthActions()
    {
        // Arrange
        var userAuthActions = new List<string> {AuthTypes.Actions.ADMIN, "authAction2" };
        var userAuthActions = userAuthActionIds
            .Select(id => new UserAuthAction { Id = id })
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.Setup(db => db.UserAuthActions).Returns(userAuthActions.Object);

        // Act
        await _authorizationService.DeleteUserAuthActionsAsync(userAuthActionIds);

        // Assert
        _mockDbContext.Verify(
            db => db.UserAuthActions.RemoveRange(It.IsAny<IEnumerable<UserAuthAction>>()),
            Times.Once
        );
        _mockDbContext.Verify(db => db.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(Skip = "Need to setup mock hierarchy")]
    public async Task GetAllowedActionsAsync_ShouldReturnAllowedActions()
    {
        // Arrange
        var actorId = "user1";
        var targetId = "target1";
        var actions = new List<string> { "view", "edit" };
        var userAuthActions = actions
            .Select(action => new UserAuthAction
            {
                UserId = actorId,
                TargetId = targetId,
                AuthAction = new AuthAction { Name = action },
            })
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.Setup(db => db.UserAuthActions).Returns(userAuthActions.Object);

        // This test is failing because the hierarchies aren't getting setup or mocked.
        // Act
        var result = await _authorizationService.GetAllowedActionsAsync(actorId, targetId);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(actions);
    }

    [Fact(Skip = "Need to setup mock hierarchy")]
    public async Task IsAuthorizedAsync_ShouldReturnFalse_WhenUserIsNotAuthorized()
    {
        // Arrange
        var actorId = "user1";
        var targetId = "target1";
        var action = "view";
        var actions = new List<string> { "edit" };
        var userAuthActions = actions
            .Select(a => new UserAuthAction
            {
                UserId = actorId,
                TargetId = targetId,
                AuthAction = new AuthAction { Name = a },
            })
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.Setup(db => db.UserAuthActions).Returns(userAuthActions.Object);

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actorId, targetId, action);

        // Assert
        result.Should().BeFalse();
    }

    [Fact(Skip = "Need to setup mock hierarchy")]
    public async Task IsAuthorizedAsync_ShouldReturnTrue_WhenUserIsAuthorized()
    {
        // Arrange
        var actorId = "user1";
        var targetId = "target1";
        var action = "view";
        var actions = new List<string> { action };
        var userAuthActions = actions
            .Select(a => new UserAuthAction
            {
                UserId = actorId,
                TargetId = targetId,
                AuthAction = new AuthAction { Name = a },
            })
            .AsQueryable()
            .BuildMockDbSet();

        _mockDbContext.Setup(db => db.UserAuthActions).Returns(userAuthActions.Object);

        // Act
        var result = await _authorizationService.IsAuthorizedAsync(actorId, targetId, action);

        // Assert
        result.Should().BeTrue();
    }

    [Fact(
        Skip = "    System.ArgumentException : Argument expression is not valid\r\n at .AuthActions.FirstOrDefaultAsync(a => a.Name == action)"
    )]
    public async Task UpdateUserAuthActionAsync_ShouldUpdateUserAuthAction()
    {
        // Arrange
        var userAuthActionId = "authAction1";
        var userId = "user1";
        var targetId = "target1";
        var action = "view";
        var authAction = new AuthAction { Id = "action1", Name = action };
        var userAuthAction = new UserAuthAction
        {
            Id = userAuthActionId,
            UserId = userId,
            TargetId = targetId,
        };

        var authActions = new List<AuthAction> { authAction }
            .AsQueryable()
            .BuildMockDbSet();
        _mockDbContext.Setup(db => db.UserAuthActions.FindAsync(userAuthActionId)).ReturnsAsync(userAuthAction);
        _mockDbContext.Setup(db => db.Users.FindAsync(userId)).ReturnsAsync(new ApplicationUser { Id = userId });
        _mockDbContext.Setup(db => db.AuthActions).Returns(authActions.Object);

        // Act
        var result = await _authorizationService.UpdateUserAuthActionAsync(userId, targetId, userAuthActionId, action);

        // Assert
        result.Should().NotBeNull();
        result.UserId.Should().Be(userId);
        result.TargetId.Should().Be(targetId);
        result.AuthActionId.Should().Be(authAction.Id);
        _mockDbContext.Verify(db => db.UserAuthActions.Update(userAuthAction), Times.Once);
        _mockDbContext.Verify(db => db.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
*/
    #endregion
}
