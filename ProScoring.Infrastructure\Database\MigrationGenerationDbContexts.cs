using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.RegattaEntities;
using ProScoring.Infrastructure.Authorization.Entities;

namespace ProScoring.Infrastructure.Database;

#region classes

/// <summary>
/// This class is used to create the database schema for the application
/// for different database providers.
/// </summary>
[ExcludeFromCodeCoverage]
public class PostgreSqlCreationApplicationDbContext : IdentityDbContext<ApplicationUser>, IApplicationDbContext
{
    #region properties
    // Note: When adding a new DbSet, remember to add it
    // to the MigrationGenerationDbContexts.cs file.
    // I tried to get them to work with a common base class,
    // but had a problem with the options argument to the constructor.
    //  file:///c:/_dev/ProScoring/ProScoringNet9/ProScoring.Infrastructure/Database/MigrationGenerationDbContexts.cs

    public virtual DbSet<ActionHierarchy> ActionHierarchies { get; }
    public virtual DbSet<AuthAction> AuthActions { get; }
    public virtual DbSet<FileRecord> Files { get; }
    public virtual DbSet<OrganizingAuthority> OrganizingAuthorities { get; }
    public virtual DbSet<Regatta> Regattas { get; }
    public virtual DbSet<RegattaBoat> RegattaBoats { get; }
    public virtual DbSet<RegattaClass> RegattaClasses { get; }
    public virtual DbSet<RegattaCompetitor> RegattaCompetitors { get; }
    public virtual DbSet<RegattaExternalLink> RegattaExternalLinks { get; }
    public virtual DbSet<RegattaFleet> RegattaFleets { get; }
    public virtual DbSet<RegattaRating> RegattaRatings { get; }
    public virtual DbSet<RegattaRatingValue> RegattaRatingValues { get; }
    public virtual DbSet<TargetType> TargetTypes { get; }
    public virtual DbSet<UserAuthAction> UserAuthActions { get; }
    #endregion

    public PostgreSqlCreationApplicationDbContext(DbContextOptions<PostgreSqlCreationApplicationDbContext> options)
        : base(options)
    {
        ActionHierarchies = Set<ActionHierarchy>();
        AuthActions = Set<AuthAction>();
        Files = Set<FileRecord>();
        OrganizingAuthorities = Set<OrganizingAuthority>();
        Regattas = Set<Regatta>();
        RegattaBoats = Set<RegattaBoat>();
        RegattaClasses = Set<RegattaClass>();
        RegattaCompetitors = Set<RegattaCompetitor>();
        RegattaExternalLinks = Set<RegattaExternalLink>();
        RegattaFleets = Set<RegattaFleet>();
        RegattaRatings = Set<RegattaRating>();
        RegattaRatingValues = Set<RegattaRatingValue>();
        TargetTypes = Set<TargetType>();
        UserAuthActions = Set<UserAuthAction>();
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        // Process each entity type defined in the model
        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            var entity = builder.Entity(entityType.ClrType);
            var typedEntityBuilder = builder.CreateTypedEntityBuilder(entityType.ClrType);

            // Configure auto-generated IDs if the entity implements IHasAutoInsertedId
            entityType.ConfigureAutoGeneratedId(entity);

            // Configure compound keys if the entity implements IHasCompoundKey<T>
            entityType.ConfigureCompoundKeys(typedEntityBuilder);

            // Configure foreign keys if the entity implements IHasForeignKeyConfiguration<T>
            entityType.ConfigureForeignKeys(typedEntityBuilder);

            // Configure seed data if the entity implements IHasInitialSeedData<T>
            entityType.ConfigureSeedData(typedEntityBuilder);
        }
    }
}

[ExcludeFromCodeCoverage]
public class PostgreSqlCreationApplicationDbContextFactor
    : IDesignTimeDbContextFactory<PostgreSqlCreationApplicationDbContext>
{
    public PostgreSqlCreationApplicationDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<PostgreSqlCreationApplicationDbContext>();
        optionsBuilder.UseNpgsql("Host=localhost;Database=proscoring;Username=********;Password=********");

        return new PostgreSqlCreationApplicationDbContext(optionsBuilder.Options);
    }
}

[ExcludeFromCodeCoverage]
public class SqliteCreationApplicationDbContext : IdentityDbContext<ApplicationUser>, IApplicationDbContext
{
    #region properties
    // Note: When adding a new DbSet, remember to add it
    // to the MigrationGenerationDbContexts.cs file.
    // I tried to get them to work with a common base class,
    // but had a problem with the options argument to the constructor.
    //  file:///c:/_dev/ProScoring/ProScoringNet9/ProScoring.Infrastructure/Database/MigrationGenerationDbContexts.cs

    public virtual DbSet<ActionHierarchy> ActionHierarchies { get; }
    public virtual DbSet<AuthAction> AuthActions { get; }
    public virtual DbSet<FileRecord> Files { get; }
    public virtual DbSet<OrganizingAuthority> OrganizingAuthorities { get; }
    public virtual DbSet<Regatta> Regattas { get; }
    public virtual DbSet<RegattaBoat> RegattaBoats { get; }
    public virtual DbSet<RegattaClass> RegattaClasses { get; }
    public virtual DbSet<RegattaCompetitor> RegattaCompetitors { get; }
    public virtual DbSet<RegattaExternalLink> RegattaExternalLinks { get; }
    public virtual DbSet<RegattaFleet> RegattaFleets { get; }
    public virtual DbSet<RegattaRating> RegattaRatings { get; }
    public virtual DbSet<RegattaRatingValue> RegattaRatingValues { get; }
    public virtual DbSet<TargetType> TargetTypes { get; }
    public virtual DbSet<UserAuthAction> UserAuthActions { get; }
    #endregion

    public SqliteCreationApplicationDbContext(DbContextOptions<SqliteCreationApplicationDbContext> options)
        : base(options)
    {
        ActionHierarchies = Set<ActionHierarchy>();
        AuthActions = Set<AuthAction>();
        Files = Set<FileRecord>();
        OrganizingAuthorities = Set<OrganizingAuthority>();
        Regattas = Set<Regatta>();
        RegattaBoats = Set<RegattaBoat>();
        RegattaClasses = Set<RegattaClass>();
        RegattaCompetitors = Set<RegattaCompetitor>();
        RegattaExternalLinks = Set<RegattaExternalLink>();
        RegattaFleets = Set<RegattaFleet>();
        RegattaRatings = Set<RegattaRating>();
        RegattaRatingValues = Set<RegattaRatingValue>();
        TargetTypes = Set<TargetType>();
        UserAuthActions = Set<UserAuthAction>();
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        // Process each entity type defined in the model
        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            var entity = builder.Entity(entityType.ClrType);
            var typedEntityBuilder = builder.CreateTypedEntityBuilder(entityType.ClrType);

            // Configure auto-generated IDs if the entity implements IHasAutoInsertedId
            entityType.ConfigureAutoGeneratedId(entity);

            // Configure compound keys if the entity implements IHasCompoundKey<T>
            entityType.ConfigureCompoundKeys(typedEntityBuilder);

            // Configure foreign keys if the entity implements IHasForeignKeyConfiguration<T>
            entityType.ConfigureForeignKeys(typedEntityBuilder);

            // Configure seed data if the entity implements IHasInitialSeedData<T>
            entityType.ConfigureSeedData(typedEntityBuilder);
        }
    }
}

[ExcludeFromCodeCoverage]
public class SqliteCreationApplicationDbContextFactor : IDesignTimeDbContextFactory<SqliteCreationApplicationDbContext>
{
    public SqliteCreationApplicationDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<SqliteCreationApplicationDbContext>();
        optionsBuilder.UseSqlite("Data Source=proscoring.db");

        return new SqliteCreationApplicationDbContext(optionsBuilder.Options);
    }
}

#endregion
