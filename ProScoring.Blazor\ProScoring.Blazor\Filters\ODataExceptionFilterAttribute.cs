using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.OData.Extensions;
using Microsoft.OData;

namespace ProScoring.Blazor.Filters;

/// <summary>
/// Exception filter for handling OData-specific exceptions.
/// </summary>
public class ODataExceptionFilterAttribute : ExceptionFilterAttribute
{
    private readonly ILogger<ODataExceptionFilterAttribute> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="ODataExceptionFilterAttribute"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public ODataExceptionFilterAttribute(ILogger<ODataExceptionFilterAttribute> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Called when an exception occurs during the execution of an action.
    /// </summary>
    /// <param name="context">The exception context.</param>
    public override void OnException(ExceptionContext context)
    {
        if (context.Exception is ODataException odataException)
        {
            _logger.LogWarning(odataException, "OData query error: {ErrorMessage}", odataException.Message);

            var error = new
            {
                error = new
                {
                    code = "ODataQueryError",
                    message = odataException.Message,
                    details = GetODataErrorDetails(context, odataException),
                },
            };

            context.Result = new BadRequestObjectResult(error);
            context.ExceptionHandled = true;
        }
        else if (
            context.Exception is InvalidOperationException invalidOpException
            && context.HttpContext.Request.ODataFeature() != null
        )
        {
            _logger.LogWarning(
                invalidOpException,
                "Invalid OData operation: {ErrorMessage}",
                invalidOpException.Message
            );

            var error = new
            {
                error = new
                {
                    code = "InvalidODataOperation",
                    message = "An error occurred while processing the OData request",
                    details = new[]
                    {
                        new
                        {
                            code = "InvalidOperation",
                            message = invalidOpException.Message,
                            target = context.HttpContext.Request.Path,
                        },
                    },
                },
            };

            context.Result = new BadRequestObjectResult(error);
            context.ExceptionHandled = true;
        }
    }

    private static object[] GetODataErrorDetails(ExceptionContext context, ODataException exception)
    {
        var details = new List<object>();

        // Add query details
        var queryOptions = context
            .HttpContext.Request.Query.Where(q => q.Key.StartsWith("$"))
            .Select(q => new
            {
                code = "QueryOption",
                message = $"Check the syntax of the {q.Key} parameter",
                target = q.Key,
            });

        details.AddRange(queryOptions);

        // Add exception details
        if (exception.InnerException != null)
        {
            details.Add(
                new
                {
                    code = "InnerError",
                    message = exception.InnerException.Message,
                    target = "InnerException",
                }
            );
        }

        return details.ToArray();
    }
}
