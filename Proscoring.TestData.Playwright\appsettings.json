{"AspireApp": {"ExecutablePath": "c:\\_dev\\ProScoring\\ProScoringNet9\\ProScoring.AppHost\\ProScoring.AppHost.csproj", "BaseUrl": "https://localhost:7292", "InheritEnvironmentVariables": true, "UseDotNetCli": true, "LaunchAspireApp": false, "Arguments": "--launch-profile Development", "WorkingDirectory": "c:\\_dev\\ProScoring\\ProScoringNet9\\ProScoring.AppHost", "EnvironmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "DOTNET_ENVIRONMENT": "Development"}, "StartupTimeoutSeconds": 60}, "DataFiles": {"UsersJsonPath": "c:\\_dev\\ProScoring\\ProScoringNet9\\Proscoring.TestData.Playwright\\Data\\Users.json", "OrganizingAuthoritiesJsonPath": "c:\\_dev\\ProScoring\\ProScoringNet9\\Proscoring.TestData.Playwright\\Data\\OrganizingAuthorities.json"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "logs/log.txt", "rollingInterval": "Day"}}]}}