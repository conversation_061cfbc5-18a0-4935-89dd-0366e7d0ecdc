using ProScoring.Domain.Entities.EntityInterfaces;

namespace ProScoring.Domain.Entities.DbSupportBaseClasses;

public abstract class LastChangeTrackingWithAutoInsertedIdBase : LastChangeTrackingBase, IHasAutoInsertedId
{
    #region properties

    /// <summary>
    /// Gets or sets the ID of the entity. This property is abstract, so inheriting classes can set
    /// MaxLength and Key attributes.<para> It is more natural to have the ID property in the entity class
    /// rather than in the base class.
    /// </summary>
    public abstract string? Id { get; set; }

    /// <summary>
    /// Gets the length of the ID.
    /// <para>The default value is <c>8</c>.
    /// </summary>
    public virtual int IdLength { get; } = 8;

    /// <summary>
    /// Gets a value indicating whether the ID should be padded to the specified length.
    /// <para>The default value is <c>false</c>.
    /// </summary>
    public bool IdPadToLength { get; } = false;

    /// <summary>
    /// Gets the prefix to be used for the ID. This property is abstract.
    /// </summary>
    public abstract string IdPrefix { get; }

    #endregion
}
