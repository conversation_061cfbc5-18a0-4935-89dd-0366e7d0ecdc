using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using ProScoring.Domain.Entities;

namespace ProScoring.Infrastructure.Identity;

/// <summary>
/// Custom claims principal factory that adds name-related claims to the user.
/// </summary>
public class CustomClaimsPrincipalFactory : UserClaimsPrincipalFactory<ApplicationUser>
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CustomClaimsPrincipalFactory"/> class.
    /// </summary>
    /// <param name="userManager">The user manager.</param>
    /// <param name="optionsAccessor">The options accessor.</param>
    public CustomClaimsPrincipalFactory(
        UserManager<ApplicationUser> userManager,
        IOptions<IdentityOptions> optionsAccessor
    )
        : base(userManager, optionsAccessor) { }

    /// <summary>
    /// Generates and adds claims to the user.
    /// </summary>
    /// <param name="user">The application user.</param>
    /// <returns>A task that represents the asynchronous claims generation operation.</returns>
    protected override async Task<ClaimsIdentity> GenerateClaimsAsync(ApplicationUser user)
    {
        // Get the default claims first (includes ClaimTypes.Name as username)
        var identity = await base.GenerateClaimsAsync(user);

        // Add standard name claims if properties are populated
        if (!string.IsNullOrEmpty(user.GivenName))
        {
            identity.AddClaim(new Claim(ClaimTypes.GivenName, user.GivenName));
        }

        if (!string.IsNullOrEmpty(user.Surname))
        {
            identity.AddClaim(new Claim(ClaimTypes.Surname, user.Surname));
        }

        // Add full name claim if available - user.Name is already properly computed in ApplicationUser class
        if (!string.IsNullOrEmpty(user.Name))
        {
            // Use custom claim type for full name to avoid overriding the username
            identity.AddClaim(new Claim(ProScoringClaimTypes.FullName, user.Name));
        }

        return identity;
    }
}
