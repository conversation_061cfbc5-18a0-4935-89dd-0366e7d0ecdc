<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>ProScoring.AppHost-698ac339-9fd1-4dc5-b9cd-79c4d66330fb</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProScoring.ApiService\ProScoring.ApiService.csproj" />
    <ProjectReference Include="..\ProScoring.Blazor\ProScoring.Blazor\ProScoring.Blazor.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.0.0" />
    <PackageReference Include="Aspire.Hosting.Azure.PostgreSQL" Version="9.0.0" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.0.0" />
  </ItemGroup>

</Project>
