using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ProScoring.Domain.Entities;
using ProScoring.Domain.Entities.DbSupportBaseClasses;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Domain.Enums;

namespace ProScoring.Domain.Entities;

/// <summary>
/// Represents a boat.
///
/// The Application keeps a list of Boats.
/// Boats may have a many-to-one relationship with ApplicationUsers
/// (i.e. an owner can have many boats, but a boat can only have one owner).
/// If a boat is registered by multiple users, they will each have a copy of the
/// boat that they can change the properties of, but a user cannot
/// change properties of another users boat.
///
/// When Users register for a regatta, then the boat info is copied to
/// a RegattaBoat, and there is a one-to-many relationship between Boat and RegattaBoat
///
/// When Golf-Handicaps are developed, they will be applied to Boats, using the performance
/// of their derived RegattaBoats in calculating the handicap.
///
/// A boat can have one or more <see cref="Ratings"/> which have different <see cref="RatingType"/>s.
/// Rating types are things like PHRF, PHRFNW, ORC, Portsmouth, etc.
///
/// Ratings can have one or more RatingValues, which are the actual values of the rating.
/// Ratings of type PHRF will have one value, where ORC has multiple values depending on the
/// type of course, wind range, etc.
/// </summary>
public class Boat : LastChangeTrackingWithAutoInsertedIdBase, IHasForeignKeyConfiguration<Boat>
{
    #region Constants

    /// <summary>
    /// The prefix used for Boat IDs.
    /// </summary>
    public const string ID_PREFIX = "B";

    #endregion Constants

    #region Properties

    /// <summary>
    /// Gets or sets the ID of the boat.
    /// </summary>
    [Key]
    [MaxLength(12)]
    [Column(Order = 10)]
    public override string? Id { get; set; }

    /// <summary>
    /// Gets or sets the country code ID for this boat.
    /// </summary>
    [Column(Order = 20)]
    [ForeignKey(nameof(CountryCode))]
    public string? CountryCodeId { get; set; }

    /// <summary>
    /// Gets or sets the country code for this boat.
    /// </summary>
    public virtual CountryCode? CountryCode { get; set; }

    /// <summary>
    /// Gets or sets the sail number of the boat.
    /// </summary>
    [Column(Order = 30)]
    [StringLength(10)]
    public string? SailNumber { get; set; }

    /// <summary>
    /// Gets or sets the name of the boat.
    /// </summary>
    [Column(Order = 40)]
    [StringLength(50)]
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the boat type.
    /// </summary>
    /// <remarks>
    /// TODO: Do we want this to be a foreign key to a BoatType table?
    /// </remarks>
    [Column(Order = 50)]
    [StringLength(50)]
    public string? BoatType { get; set; }

    /// <summary>
    /// Gets or sets the length of the boat.
    /// </summary>
    [Column(Order = 60)]
    public float? Length { get; set; }

    /// <summary>
    /// Gets or sets the unit of measurement for the boat length.
    /// </summary>
    [Column(Order = 70)]
    public LengthUnit? LengthUnit { get; set; }

    /// <summary>
    /// Gets or sets the owner ID of the boat.
    /// </summary>
    [Required]
    [Column(Order = 80)]
    // [ForeignKey(nameof(Owner))] // this is configured in the ConfigureForeignKeys method
    public required string OwnerId { get; set; }

    /// <summary>
    /// Gets or sets the owner of the boat.
    /// </summary>
    public virtual ApplicationUser Owner { get; set; } = null!;

    /// <summary>
    /// Gets or sets the prefix to be used for the ID.
    /// </summary>
    public override string IdPrefix => ID_PREFIX;

    /// <summary>
    /// Gets or sets the collection of ratings for this boat.
    /// </summary>
    public virtual ICollection<Rating> Ratings { get; set; } = new List<Rating>();

    #endregion Properties

    #region DB Configuration

    #region IHasForeignKeyConfiguration Implementation

    /// <summary>
    /// Configures the foreign keys for the Boat entity.
    /// </summary>
    /// <param name="entity">The entity type builder.</param>
    public static void ConfigureForeignKeys(EntityTypeBuilder<Boat> entity)
    {
        entity.HasOne(e => e.Owner).WithMany().HasForeignKey(e => e.OwnerId).OnDelete(DeleteBehavior.Restrict);
    }

    #endregion IHasForeignKeyConfiguration Implementation

    #endregion DB Configuration
}
